{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\yonghu\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\yonghu\\list.vue", "mtime": 1642386767403}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["AddOrUpdate", "styleJs", "data", "searchForm", "key", "sessionTable", "role", "form", "id", "username", "password", "yo<PERSON><PERSON><PERSON><PERSON>", "yonghuPhone", "yonghuIdNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sexTypes", "yonghuEmail", "newMoney", "createTime", "dataList", "pageIndex", "pageSize", "totalPage", "dataListLoading", "dataListSelections", "showFlag", "sfshVisiable", "shForm", "chartVisiable", "addOrUpdateFlag", "contents", "layouts", "echartsDate", "Date", "json_fields", "created", "listStyle", "init", "getDataList", "contentStyleChange", "mounted", "$storage", "get", "filters", "htmlfilter", "val", "replace", "components", "computed", "methods", "chartDialog", "_this2", "_this", "params", "dateFormat", "riqi", "getFullYear", "thisTable", "tableName", "sumColum", "date", "$nextTick", "statistic", "$echarts", "document", "getElementById", "$http", "url", "method", "then", "_ref", "code", "series", "yAxis", "for<PERSON>ach", "item", "index", "tempMap", "name", "legend", "type", "push", "option", "tooltip", "trigger", "axisPointer", "crossStyle", "color", "toolbox", "feature", "magicType", "show", "saveAsImage", "xAxis", "axisLabel", "formatter", "setOption", "window", "onresize", "resize", "$message", "message", "duration", "onClose", "search", "_ref2", "res", "p<PERSON><PERSON>y", "i", "length", "xinzi", "total", "value", "title", "text", "left", "radius", "center", "emphasis", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "contentSearchStyleChange", "contentBtnAdAllStyleChange", "contentSearchBtnStyleChange", "contentTableBtnStyleChange", "contentPageStyleChange", "_this3", "querySelectorAll", "el", "textAlign", "inputFontPosition", "style", "height", "inputHeight", "lineHeight", "inputFontColor", "fontSize", "inputFontSize", "borderWidth", "inputBorderWidth", "borderStyle", "inputBorderStyle", "borderColor", "inputBorderColor", "borderRadius", "inputBorderRadius", "backgroundColor", "inputBgColor", "inputTitle", "inputTitleColor", "inputTitleSize", "setTimeout", "inputIconColor", "_this4", "searchBtnHeight", "searchBtnFontColor", "searchBtnFontSize", "searchBtnBorderWidth", "searchBtnBorderStyle", "searchBtnBorderColor", "searchBtnBorderRadius", "searchBtnBgColor", "_this5", "btnAdAllHeight", "btnAdAllAddFontColor", "btnAdAllFontSize", "btnAdAllBorderWidth", "btnAdAllBorderStyle", "btnAdAllBorderColor", "btnAdAllBorderRadius", "btnAdAllAddBgColor", "btnAdAllDelFontColor", "btnAdAllDelBgColor", "btnAdAllWarnFontColor", "btnAdAllWarnBgColor", "rowStyle", "_ref3", "row", "rowIndex", "tableStripe", "tableStripeFontColor", "cellStyle", "_ref4", "tableStripeBgColor", "headerRowStyle", "_ref5", "tableHeaderFontColor", "headerCellStyle", "_ref6", "tableHeaderBgColor", "arr", "pageTotal", "pageSizes", "pagePrevNext", "pagePager", "pageJumper", "join", "pageEachNum", "_this6", "page", "limit", "sort", "undefined", "_ref7", "list", "sizeChangeHandle", "currentChangeHandle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addOrUpdateHandler", "_this7", "crossAddOrUpdateFlag", "$refs", "addOrUpdate", "download", "file", "open", "delete<PERSON><PERSON><PERSON>", "_this8", "ids", "Number", "map", "$confirm", "concat", "confirmButtonText", "cancelButtonText", "_ref8", "error", "msg", "resetPassword", "_ref9", "alert", "yonghuUploadSuccess", "_ref10", "yonghuUploadError"], "sources": ["src/views/modules/yonghu/list.vue"], "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"main-content\">\r\n\r\n        <!-- 条件查询 -->\r\n        <div v-if=\"showFlag\">\r\n            <el-form :inline=\"true\" :model=\"searchForm\" class=\"form-content\">\r\n                <el-row :gutter=\"20\" class=\"slt\" :style=\"{justifyContent:contents.searchBoxPosition=='1'?'flex-start':contents.searchBoxPosition=='2'?'center':'flex-end'}\">\r\n                                 \r\n                     <el-form-item :label=\"contents.inputTitle == 1 ? '用户姓名' : ''\">\r\n                         <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuName\" placeholder=\"用户姓名\" clearable></el-input>\r\n                     </el-form-item>\r\n         \r\n                     <el-form-item :label=\"contents.inputTitle == 1 ? '用户手机号' : ''\">\r\n                         <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuPhone\" placeholder=\"用户手机号\" clearable></el-input>\r\n                     </el-form-item>\r\n         \r\n                     <el-form-item :label=\"contents.inputTitle == 1 ? '用户身份证号' : ''\">\r\n                         <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuIdNumber\" placeholder=\"用户身份证号\" clearable></el-input>\r\n                     </el-form-item>\r\n                                        \r\n\r\n\r\n                    <el-form-item>\r\n                        <el-button type=\"success\" @click=\"search()\">查询<i class=\"el-icon-search el-icon--right\"/></el-button>\r\n                    </el-form-item>\r\n                </el-row>\r\n                <el-row class=\"ad\" :style=\"{justifyContent:contents.btnAdAllBoxPosition=='1'?'flex-start':contents.btnAdAllBoxPosition=='2'?'center':'flex-end'}\">\r\n                    <el-form-item>\r\n                        <el-button\r\n                                v-if=\"isAuth('yonghu','新增')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-plus\"\r\n                                @click=\"addOrUpdateHandler()\"\r\n                        >新增</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('yonghu','删除')\"\r\n                                :disabled=\"dataListSelections.length <= 0\"\r\n                                type=\"danger\"\r\n                                icon=\"el-icon-delete\"\r\n                                @click=\"deleteHandler()\"\r\n                        >删除</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('yonghu','报表')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-pie-chart\"\r\n                                @click=\"chartDialog()\"\r\n                        >报表</el-button>\r\n                        &nbsp;\r\n                        <a style=\"text-decoration:none\" class=\"el-button el-button--success\"\r\n                           v-if=\"isAuth('yonghu','导入导出')\"\r\n                           icon=\"el-icon-download\"\r\n                           href=\"http://localhost:8080/tiyuguan/upload/yonghuMuBan.xls\"\r\n                        >批量导入用户数据模板</a>\r\n                        &nbsp;\r\n                        <el-upload\r\n                                v-if=\"isAuth('yonghu','导入导出')\"\r\n                                style=\"display: inline-block\"\r\n                                action=\"tiyuguan/file/upload\"\r\n                                :on-success=\"yonghuUploadSuccess\"\r\n                                :on-error=\"yonghuUploadError\"\r\n                                :show-file-list = false>\r\n                            <el-button\r\n                                    v-if=\"isAuth('yonghu','导入导出')\"\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-upload2\"\r\n                            >批量导入用户数据</el-button>\r\n                        </el-upload>\r\n                        &nbsp;\r\n                        <!-- 导出excel -->\r\n                        <download-excel v-if=\"isAuth('yonghu','导入导出')\" style=\"display: inline-block\" class = \"export-excel-wrapper\" :data = \"dataList\" :fields = \"json_fields\" name = \"yonghu.xls\">\r\n                            <!-- 导出excel -->\r\n                            <el-button\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-download\"\r\n                            >导出</el-button>\r\n                        </download-excel>\r\n                        &nbsp;\r\n                    </el-form-item>\r\n                </el-row>\r\n            </el-form>\r\n            <div class=\"table-content\">\r\n                <el-table class=\"tables\" :size=\"contents.tableSize\" :show-header=\"contents.tableShowHeader\"\r\n                          :header-row-style=\"headerRowStyle\" :header-cell-style=\"headerCellStyle\"\r\n                          :border=\"contents.tableBorder\"\r\n                          :fit=\"contents.tableFit\"\r\n                          :stripe=\"contents.tableStripe\"\r\n                          :row-style=\"rowStyle\"\r\n                          :cell-style=\"cellStyle\"\r\n                          :style=\"{width: '100%',fontSize:contents.tableContentFontSize,color:contents.tableContentFontColor}\"\r\n                          v-if=\"isAuth('yonghu','查看')\"\r\n                          :data=\"dataList\"\r\n                          v-loading=\"dataListLoading\"\r\n                          @selection-change=\"selectionChangeHandler\">\r\n                    <el-table-column  v-if=\"contents.tableSelection\"\r\n                                      type=\"selection\"\r\n                                      header-align=\"center\"\r\n                                      align=\"center\"\r\n                                      width=\"50\">\r\n                    </el-table-column>\r\n                    <el-table-column label=\"索引\" v-if=\"contents.tableIndex\" type=\"index\" width=\"50\" />\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"username\"\r\n                                   header-align=\"center\"\r\n                                   label=\"账户\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.username}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"yonghuName\"\r\n                                   header-align=\"center\"\r\n                                   label=\"用户姓名\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuName}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"yonghuPhone\"\r\n                                   header-align=\"center\"\r\n                                   label=\"用户手机号\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuPhone}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"yonghuIdNumber\"\r\n                                   header-align=\"center\"\r\n                                   label=\"用户身份证号\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuIdNumber}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\" prop=\"yonghuPhoto\"\r\n                                     header-align=\"center\"\r\n                                     width=\"200\"\r\n                                     label=\"用户头像\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div v-if=\"scope.row.yonghuPhoto\">\r\n                                <img :src=\"scope.row.yonghuPhoto\" width=\"100\" height=\"100\">\r\n                            </div>\r\n                            <div v-else>无图片</div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"sexTypes\"\r\n                                      header-align=\"center\"\r\n                                      label=\"性别\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.sexValue}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"yonghuEmail\"\r\n                                   header-align=\"center\"\r\n                                   label=\"电子邮箱\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuEmail}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"newMoney\"\r\n                                   header-align=\"center\"\r\n                                   label=\"余额\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.newMoney}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column width=\"300\" :align=\"contents.tableAlign\"\r\n                                     header-align=\"center\"\r\n                                     label=\"操作\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button v-if=\"isAuth('yonghu','查看')\" type=\"success\" icon=\"el-icon-tickets\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id,'info')\">详情</el-button>\r\n                            <el-button v-if=\"isAuth('yonghu','修改')\" type=\"primary\" icon=\"el-icon-edit\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id)\">修改</el-button>\r\n                            <el-button v-if=\"isAuth('yonghu','删除')\" type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteHandler(scope.row.id)\">删除</el-button>\r\n\r\n                            <el-button v-if=\"isAuth('yonghu','修改')\" type=\"success\" icon=\"el-icon-tickets\" size=\"mini\" @click=\"resetPassword(scope.row.id)\">重置密码</el-button>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n                <el-pagination\r\n                        clsss=\"pages\"\r\n                        :layout=\"layouts\"\r\n                        @size-change=\"sizeChangeHandle\"\r\n                        @current-change=\"currentChangeHandle\"\r\n                        :current-page=\"pageIndex\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"Number(contents.pageEachNum)\"\r\n                        :total=\"totalPage\"\r\n                        :small=\"contents.pageStyle\"\r\n                        class=\"pagination-content\"\r\n                        :background=\"contents.pageBtnBG\"\r\n                        :style=\"{textAlign:contents.pagePosition==1?'left':contents.pagePosition==2?'center':'right'}\"\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <!-- 添加/修改页面  将父组件的search方法传递给子组件-->\r\n        <add-or-update v-if=\"addOrUpdateFlag\" :parent=\"this\" ref=\"addOrUpdate\"></add-or-update>\r\n\r\n\r\n\r\n        <el-dialog title=\"统计报表\" :visible.sync=\"chartVisiable\" width=\"800\">\r\n            <el-date-picker\r\n                    v-model=\"echartsDate\"\r\n                    type=\"year\"\r\n                    placeholder=\"选择年\">\r\n            </el-date-picker>\r\n            <el-button @click=\"chartDialog()\">查询</el-button>\r\n            <div id=\"statistic\" style=\"width:100%;height:600px;\"></div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"chartVisiable = false\">返回</el-button>\r\n\t\t\t</span>\r\n        </el-dialog>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import AddOrUpdate from \"./add-or-update\";\r\n    import styleJs from \"../../../utils/style.js\";\r\n    export default {\r\n        data() {\r\n        return {\r\n            searchForm: {\r\n                key: \"\"\r\n            },\r\n            sessionTable : \"\",//登录账户所在表名\r\n            role : \"\",//权限\r\n    //级联表下拉框搜索条件\r\n    //当前表下拉框搜索条件\r\n            form:{\r\n                id : null,\r\n                username : null,\r\n                password : null,\r\n                yonghuName : null,\r\n                yonghuPhone : null,\r\n                yonghuIdNumber : null,\r\n                yonghuPhoto : null,\r\n                sexTypes : null,\r\n                yonghuEmail : null,\r\n                newMoney : null,\r\n                createTime : null,\r\n            },\r\n            dataList: [],\r\n            pageIndex: 1,\r\n            pageSize: 10,\r\n            totalPage: 0,\r\n            dataListLoading: false,\r\n            dataListSelections: [],\r\n            showFlag: true,\r\n            sfshVisiable: false,\r\n            shForm: {},\r\n            chartVisiable: false,\r\n            addOrUpdateFlag:false,\r\n            contents:null,\r\n            layouts: '',\r\n\r\n            echartsDate: new Date(),//echarts的时间查询字段\r\n\r\n            //导出excel\r\n            json_fields: {\r\n                //级联表字段\r\n                //本表字段\r\n                     '账户': \"username\",\r\n                     '用户姓名': \"yonghuName\",\r\n                     '用户手机号': \"yonghuPhone\",\r\n                     '用户身份证号': \"yonghuIdNumber\",\r\n                     '用户头像': \"yonghuPhoto\",\r\n                     '性别': \"sexTypes\",\r\n                     '电子邮箱': \"yonghuEmail\",\r\n                     '余额': \"newMoney\",\r\n            },\r\n\r\n            };\r\n        },\r\n        created() {\r\n            this.contents = styleJs.listStyle();\r\n            this.init();\r\n            this.getDataList();\r\n            this.contentStyleChange()\r\n        },\r\n        mounted() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n        },\r\n        filters: {\r\n            htmlfilter: function (val) {\r\n                return val.replace(/<[^>]*>/g).replace(/undefined/g,'');\r\n            }\r\n        },\r\n        components: {\r\n            AddOrUpdate,\r\n        },\r\n        computed: {\r\n        },\r\n        methods: {\r\n            chartDialog() {\r\n                let _this = this;\r\n                let params = {\r\n                    dateFormat :\"%Y\"\r\n                    ,riqi :_this.echartsDate.getFullYear()\r\n                    ,thisTable : {//当前表\r\n                        tableName :\"shangdian_shouyin\"//当前表表名\r\n                        ,sumColum : 'shangdian_shouyin_true_price' //求和字段\r\n                        ,date : 'insert_time'//分组日期字段\r\n                        // ,string : 'name,leixing'//分组字符串字段\r\n                        // ,types : 'shangdian_shouyin_types'//分组下拉框字段\r\n                    }\r\n                    // ,joinTable : {//级联表（可以不存在）\r\n                    //     tableName :\"yuangong\"//级联表表名\r\n                    //     // ,date : 'insert_time'//分组日期字段\r\n                    //     ,string : 'yuangong_name'//分组字符串字段\r\n                    //     // ,types : 'insertTime'//分组下拉框字段\r\n                    // }\r\n                }\r\n                _this.chartVisiable = true;\r\n                _this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"barSum\",\r\n                        method: \"get\",\r\n                        params: params\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n\r\n\r\n                            //柱状图 求和 已成功使用\r\n                            //start\r\n                            let series = [];//具体数据值\r\n                            data.data.yAxis.forEach(function (item,index) {\r\n                                let tempMap = {};\r\n                                tempMap.name=data.data.legend[index];\r\n                                tempMap.type='bar';\r\n                                tempMap.data=item;\r\n                                series.push(tempMap);\r\n\r\n                            })\r\n\r\n                            var option = {\r\n                                tooltip: {\r\n                                    trigger: 'axis',\r\n                                    axisPointer: {\r\n                                        type: 'cross',\r\n                                        crossStyle: {\r\n                                            color: '#999'\r\n                                        }\r\n                                    }\r\n                                },\r\n                                toolbox: {\r\n                                    feature: {\r\n                                        // dataView: { show: true, readOnly: false },  // 数据查看\r\n                                        magicType: { show: true, type: ['line', 'bar'] },//切换图形展示方式\r\n                                        // restore: { show: true }, // 刷新\r\n                                        saveAsImage: { show: true }//保存\r\n                                    }\r\n                                },\r\n                                legend: {\r\n                                    data: data.data.legend//标题  可以点击导致某一列数据消失\r\n                                },\r\n                                xAxis: [\r\n                                    {\r\n                                        type: 'category',\r\n                                        name: '月份',\r\n                                        data: data.data.xAxis,\r\n                                        axisPointer: {\r\n                                            type: 'shadow'\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                yAxis: [\r\n                                    {\r\n                                        type: 'value',//不能改\r\n                                        name: '元',//y轴单位\r\n                                        axisLabel: {\r\n                                            formatter: '{value} 元' // 后缀\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                series:series//具体数据\r\n                            };\r\n                            // 使用刚指定的配置项和数据显示图表。\r\n                            statistic.setOption(option);\r\n                            //根据窗口的大小变动图表\r\n                            window.onresize = function () {\r\n                                statistic.resize();\r\n                            };\r\n                            //end\r\n\r\n\r\n\r\n                            //饼状图 原先自带的 未修改过\r\n                            //start\r\n                            /*let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表。\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图表\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }*/\r\n\r\n                            //end\r\n                        }else {\r\n                            this.$message({\r\n                                message: \"报表未查询到数据\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n                this.chartVisiable = !this.chartVisiable;\r\n                this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"group/xinzitongji/xinzi\",\r\n                        method: \"get\",\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表。\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图表\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }\r\n                        }\r\n                    });\r\n                // xcolumn ycolumn\r\n                });\r\n            },\r\n            contentStyleChange() {\r\n                this.contentSearchStyleChange()\r\n                this.contentBtnAdAllStyleChange()\r\n                this.contentSearchBtnStyleChange()\r\n                this.contentTableBtnStyleChange()\r\n                this.contentPageStyleChange()\r\n            },\r\n            contentSearchStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-input__inner').forEach(el => {\r\n                        let textAlign = 'left'\r\n                        if(this.contents.inputFontPosition == 2)\r\n                            textAlign = 'center'\r\n                            if (this.contents.inputFontPosition == 3) textAlign = 'right'\r\n                                el.style.textAlign = textAlign\r\n                            el.style.height = this.contents.inputHeight\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                            el.style.color = this.contents.inputFontColor\r\n                            el.style.fontSize = this.contents.inputFontSize\r\n                            el.style.borderWidth = this.contents.inputBorderWidth\r\n                            el.style.borderStyle = this.contents.inputBorderStyle\r\n                            el.style.borderColor = this.contents.inputBorderColor\r\n                            el.style.borderRadius = this.contents.inputBorderRadius\r\n                            el.style.backgroundColor = this.contents.inputBgColor\r\n                    })\r\n                    if (this.contents.inputTitle) {\r\n                        document.querySelectorAll('.form-content .slt .el-form-item__label').forEach(el => {\r\n                            el.style.color = this.contents.inputTitleColor\r\n                            el.style.fontSize = this.contents.inputTitleSize\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }\r\n                    setTimeout(() => {\r\n                        document.querySelectorAll('.form-content .slt .el-input__prefix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__suffix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__icon').forEach(el => {\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }, 10 )\r\n                })\r\n            },\r\n            // 搜索按钮\r\n            contentSearchBtnStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.searchBtnHeight\r\n                        el.style.color = this.contents.searchBtnFontColor\r\n                        el.style.fontSize = this.contents.searchBtnFontSize\r\n                        el.style.borderWidth = this.contents.searchBtnBorderWidth\r\n                        el.style.borderStyle = this.contents.searchBtnBorderStyle\r\n                        el.style.borderColor = this.contents.searchBtnBorderColor\r\n                        el.style.borderRadius = this.contents.searchBtnBorderRadius\r\n                        el.style.backgroundColor = this.contents.searchBtnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 新增、批量删除\r\n            contentBtnAdAllStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .ad .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllAddFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllAddBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--danger').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllDelFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllDelBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--warning').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllWarnFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllWarnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 表格\r\n            rowStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {color: this.contents.tableStripeFontColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            cellStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {backgroundColor: this.contents.tableStripeBgColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            headerRowStyle({row, rowIndex}) {\r\n                return {color: this.contents.tableHeaderFontColor}\r\n            },\r\n            headerCellStyle({row, rowIndex}) {\r\n                return {backgroundColor: this.contents.tableHeaderBgColor}\r\n            },\r\n            // 表格按钮\r\n            contentTableBtnStyleChange() {\r\n                // this.$nextTick(()=>{\r\n                //   setTimeout(()=>{\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--success').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDetailFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDetailBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--primary').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnEditFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnEditBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--danger').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDelFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDelBgColor\r\n                //     })\r\n\r\n                //   }, 50)\r\n                // })\r\n            },\r\n            // 分页\r\n            contentPageStyleChange() {\r\n                let arr = []\r\n                if (this.contents.pageTotal) arr.push('total')\r\n                if (this.contents.pageSizes) arr.push('sizes')\r\n                if (this.contents.pagePrevNext) {\r\n                    arr.push('prev')\r\n                    if (this.contents.pagePager) arr.push('pager')\r\n                    arr.push('next')\r\n                }\r\n                if (this.contents.pageJumper) arr.push('jumper')\r\n                this.layouts = arr.join()\r\n                this.contents.pageEachNum = 10\r\n            },\r\n\r\n            init() {\r\n            },\r\n            search() {\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 获取数据列表\r\n            getDataList() {\r\n                this.dataListLoading = true;\r\n                let params = {\r\n                    page: this.pageIndex,\r\n                    limit: this.pageSize,\r\n                    sort: 'id',\r\n                }\r\n\r\n                                 \r\n                if (this.searchForm.yonghuName!= '' && this.searchForm.yonghuName!= undefined) {\r\n                    params['yonghuName'] = '%' + this.searchForm.yonghuName + '%'\r\n                }\r\n         \r\n                if (this.searchForm.yonghuPhone!= '' && this.searchForm.yonghuPhone!= undefined) {\r\n                    params['yonghuPhone'] = '%' + this.searchForm.yonghuPhone + '%'\r\n                }\r\n         \r\n                if (this.searchForm.yonghuIdNumber!= '' && this.searchForm.yonghuIdNumber!= undefined) {\r\n                    params['yonghuIdNumber'] = '%' + this.searchForm.yonghuIdNumber + '%'\r\n                }\r\n                                        \r\n                params['yonghuDelete'] = 1// 逻辑删除字段 1 未删除 2 删除\r\n\r\n\r\n                this.$http({\r\n                    url: \"yonghu/page\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.dataList = data.data.list;\r\n                        this.totalPage = data.data.total;\r\n                    }else{\r\n                        this.dataList = [];\r\n                        this.totalPage = 0;\r\n                    }\r\n                    this.dataListLoading = false;\r\n                });\r\n\r\n                //查询级联表搜索条件所有列表\r\n                //查询当前表搜索条件所有列表\r\n            },\r\n            //每页数\r\n            sizeChangeHandle(val) {\r\n                this.pageSize = val;\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 当前页\r\n            currentChangeHandle(val) {\r\n                this.pageIndex = val;\r\n                this.getDataList();\r\n            },\r\n            // 多选\r\n            selectionChangeHandler(val) {\r\n                this.dataListSelections = val;\r\n            },\r\n            // 添加/修改\r\n            addOrUpdateHandler(id, type) {\r\n                this.showFlag = false;\r\n                this.addOrUpdateFlag = true;\r\n                this.crossAddOrUpdateFlag = false;\r\n                if (type != 'info') {\r\n                    type = 'else';\r\n                }\r\n                this.$nextTick(() => {\r\n                    this.$refs.addOrUpdate.init(id, type);\r\n                });\r\n            },\r\n            // 下载\r\n            download(file) {\r\n                window.open(\" ${file} \")\r\n            },\r\n            // 删除\r\n            deleteHandler(id) {\r\n                var ids = id ? [Number(id)] : this.dataListSelections.map(item => {\r\n                    return Number(item.id);\r\n                });\r\n\r\n                this.$confirm(`确定进行[${id ? \"删除\" : \"批量删除\"}]操作?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: \"yonghu/delete\",\r\n                        method: \"post\",\r\n                        data: ids\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            this.$message({\r\n                                message: \"操作成功\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }else{\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n            resetPassword(id) {\r\n                //重置密码\r\n                this.$http({\r\n                    url: \"yonghu/resetPassword?id=\" + id,\r\n                    method: \"get\",\r\n                    // id:id\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        alert('重置成功,密码已重置为123456');\r\n                    }\r\n                });\r\n            },\r\n            // 导入功能上传文件成功后调用导入方法\r\n            yonghuUploadSuccess(data){\r\n                let _this = this;\r\n                _this.$http({\r\n                    url: \"yonghu/batchInsert?fileName=\" + data.file,\r\n                    method: \"get\"\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        _this.$message({\r\n                            message: \"导入用户数据成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.search();\r\n                            }\r\n                        });\r\n                    }else{\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n\r\n            },\r\n            // 导入功能上传文件失败后调用导入方法\r\n            yonghuUploadError(data){\r\n                this.$message.error('上传失败');\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.slt {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .ad {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .pages {\r\n    & /deep/ el-pagination__sizes{\r\n      & /deep/ el-input__inner {\r\n        height: 22px;\r\n        line-height: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .el-button+.el-button {\r\n    margin:0;\r\n  }\r\n\r\n  .tables {\r\n\t& /deep/ .el-button--success {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(121, 244, 164, 0.54);\r\n\t}\r\n\r\n\t& /deep/ .el-button--primary {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(169, 115, 232, 0.65);\r\n\t}\r\n\r\n\t& /deep/ .el-button--danger {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(241, 120, 186, 1);\r\n\t}\r\n\r\n    & /deep/ .el-button {\r\n      margin: 4px;\r\n    }\r\n  }\r\n</style>\r\n\r\n\r\n"], "mappings": ";;;;;;;;;;;AAyOA,OAAAA,WAAA;AACA,OAAAC,OAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;QACAC,GAAA;MACA;MACAC,YAAA;MAAA;MACAC,IAAA;MAAA;MACA;MACA;MACAC,IAAA;QACAC,EAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,cAAA;QACAC,WAAA;QACAC,QAAA;QACAC,WAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACAC,QAAA;MACAC,SAAA;MACAC,QAAA;MACAC,SAAA;MACAC,eAAA;MACAC,kBAAA;MACAC,QAAA;MACAC,YAAA;MACAC,MAAA;MACAC,aAAA;MACAC,eAAA;MACAC,QAAA;MACAC,OAAA;MAEAC,WAAA,MAAAC,IAAA;MAAA;;MAEA;MACAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAL,QAAA,GAAA7B,OAAA,CAAAmC,SAAA;IACA,KAAAC,IAAA;IACA,KAAAC,WAAA;IACA,KAAAC,kBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAnC,YAAA,QAAAoC,QAAA,CAAAC,GAAA;IACA,KAAApC,IAAA,QAAAmC,QAAA,CAAAC,GAAA;EAEA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,OAAA,aAAAA,OAAA;IACA;EACA;EACAC,UAAA;IACA/C,WAAA,EAAAA;EACA;EACAgD,QAAA,GACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,KAAA;MACA,IAAAC,MAAA;QACAC,UAAA;QACAC,IAAA,EAAAH,KAAA,CAAApB,WAAA,CAAAwB,WAAA;QACAC,SAAA;UAAA;UACAC,SAAA;UAAA;UACAC,QAAA;UAAA;UACAC,IAAA;UACA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAR,KAAA,CAAAxB,aAAA;MACAwB,KAAA,CAAAS,SAAA;QACA,IAAAC,SAAA,GAAAX,MAAA,CAAAY,QAAA,CAAA1B,IAAA,CAAA2B,QAAA,CAAAC,cAAA;QACAd,MAAA,CAAAe,KAAA;UACAC,GAAA;UACAC,MAAA;UACAf,MAAA,EAAAA;QACA,GAAAgB,IAAA,WAAAC,IAAA;UAAA,IAAApE,IAAA,GAAAoE,IAAA,CAAApE,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAAqE,IAAA;YAGA;YACA;YACA,IAAAC,MAAA;YACAtE,IAAA,CAAAA,IAAA,CAAAuE,KAAA,CAAAC,OAAA,WAAAC,IAAA,EAAAC,KAAA;cACA,IAAAC,OAAA;cACAA,OAAA,CAAAC,IAAA,GAAA5E,IAAA,CAAAA,IAAA,CAAA6E,MAAA,CAAAH,KAAA;cACAC,OAAA,CAAAG,IAAA;cACAH,OAAA,CAAA3E,IAAA,GAAAyE,IAAA;cACAH,MAAA,CAAAS,IAAA,CAAAJ,OAAA;YAEA;YAEA,IAAAK,MAAA;cACAC,OAAA;gBACAC,OAAA;gBACAC,WAAA;kBACAL,IAAA;kBACAM,UAAA;oBACAC,KAAA;kBACA;gBACA;cACA;cACAC,OAAA;gBACAC,OAAA;kBACA;kBACAC,SAAA;oBAAAC,IAAA;oBAAAX,IAAA;kBAAA;kBAAA;kBACA;kBACAY,WAAA;oBAAAD,IAAA;kBAAA;gBACA;cACA;cACAZ,MAAA;gBACA7E,IAAA,EAAAA,IAAA,CAAAA,IAAA,CAAA6E,MAAA;cACA;cACAc,KAAA,GACA;gBACAb,IAAA;gBACAF,IAAA;gBACA5E,IAAA,EAAAA,IAAA,CAAAA,IAAA,CAAA2F,KAAA;gBACAR,WAAA;kBACAL,IAAA;gBACA;cACA,EACA;cACAP,KAAA,GACA;gBACAO,IAAA;gBAAA;gBACAF,IAAA;gBAAA;gBACAgB,SAAA;kBACAC,SAAA;gBACA;cACA,EACA;cACAvB,MAAA,EAAAA,MAAA;YACA;YACA;YACAV,SAAA,CAAAkC,SAAA,CAAAd,MAAA;YACA;YACAe,MAAA,CAAAC,QAAA;cACApC,SAAA,CAAAqC,MAAA;YACA;YACA;;YAIA;YACA;YACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;YAEA;UACA;YACAhD,MAAA,CAAAiD,QAAA;cACAC,OAAA;cACArB,IAAA;cACAsB,QAAA;cACAC,OAAA,WAAAA,QAAA;gBACApD,MAAA,CAAAqD,MAAA;cACA;YACA;UACA;QACA;MACA;MACA,KAAA5E,aAAA,SAAAA,aAAA;MACA,KAAAiC,SAAA;QACA,IAAAC,SAAA,GAAAX,MAAA,CAAAY,QAAA,CAAA1B,IAAA,CAAA2B,QAAA,CAAAC,cAAA;QACAd,MAAA,CAAAe,KAAA;UACAC,GAAA;UACAC,MAAA;QACA,GAAAC,IAAA,WAAAoC,KAAA;UAAA,IAAAvG,IAAA,GAAAuG,KAAA,CAAAvG,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAAqE,IAAA;YACA,IAAAmC,GAAA,GAAAxG,IAAA,CAAAA,IAAA;YACA,IAAA2F,KAAA;YACA,IAAApB,KAAA;YACA,IAAAkC,MAAA;YACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,GAAA,CAAAG,MAAA,EAAAD,CAAA;cACAf,KAAA,CAAAZ,IAAA,CAAAyB,GAAA,CAAAE,CAAA,EAAAE,KAAA;cACArC,KAAA,CAAAQ,IAAA,CAAAyB,GAAA,CAAAE,CAAA,EAAAG,KAAA;cACAJ,MAAA,CAAA1B,IAAA;gBACA+B,KAAA,EAAAN,GAAA,CAAAE,CAAA,EAAAG,KAAA;gBACAjC,IAAA,EAAA4B,GAAA,CAAAE,CAAA,EAAAE;cACA;cACA,IAAA5B,MAAA;cACAA,MAAA;gBACA+B,KAAA;kBACAC,IAAA;kBACAC,IAAA;gBACA;gBACAhC,OAAA;kBACAC,OAAA;kBACAW,SAAA;gBACA;gBACAvB,MAAA;kBACAQ,IAAA;kBACAoC,MAAA;kBACAC,MAAA;kBACAnH,IAAA,EAAAyG,MAAA;kBACAW,QAAA;oBACAC,SAAA;sBACAC,UAAA;sBACAC,aAAA;sBACAC,WAAA;oBACA;kBACA;gBACA;cACA;cACA;cACA5D,SAAA,CAAAkC,SAAA,CAAAd,MAAA;cACA;cACAe,MAAA,CAAAC,QAAA;gBACApC,SAAA,CAAAqC,MAAA;cACA;YACA;UACA;QACA;QACA;MACA;IACA;IACA5D,kBAAA,WAAAA,mBAAA;MACA,KAAAoF,wBAAA;MACA,KAAAC,0BAAA;MACA,KAAAC,2BAAA;MACA,KAAAC,0BAAA;MACA,KAAAC,sBAAA;IACA;IACAJ,wBAAA,WAAAA,yBAAA;MAAA,IAAAK,MAAA;MACA,KAAAnE,SAAA;QACAG,QAAA,CAAAiE,gBAAA,wCAAAvD,OAAA,WAAAwD,EAAA;UACA,IAAAC,SAAA;UACA,IAAAH,MAAA,CAAAlG,QAAA,CAAAsG,iBAAA,OACAD,SAAA;UACA,IAAAH,MAAA,CAAAlG,QAAA,CAAAsG,iBAAA,OAAAD,SAAA;UACAD,EAAA,CAAAG,KAAA,CAAAF,SAAA,GAAAA,SAAA;UACAD,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAAN,MAAA,CAAAlG,QAAA,CAAAyG,WAAA;UACAL,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAlG,QAAA,CAAAyG,WAAA;UACAL,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAyC,MAAA,CAAAlG,QAAA,CAAA2G,cAAA;UACAP,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAAV,MAAA,CAAAlG,QAAA,CAAA6G,aAAA;UACAT,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAZ,MAAA,CAAAlG,QAAA,CAAA+G,gBAAA;UACAX,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAd,MAAA,CAAAlG,QAAA,CAAAiH,gBAAA;UACAb,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAhB,MAAA,CAAAlG,QAAA,CAAAmH,gBAAA;UACAf,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAlB,MAAA,CAAAlG,QAAA,CAAAqH,iBAAA;UACAjB,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAApB,MAAA,CAAAlG,QAAA,CAAAuH,YAAA;QACA;QACA,IAAArB,MAAA,CAAAlG,QAAA,CAAAwH,UAAA;UACAtF,QAAA,CAAAiE,gBAAA,4CAAAvD,OAAA,WAAAwD,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAyC,MAAA,CAAAlG,QAAA,CAAAyH,eAAA;YACArB,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAAV,MAAA,CAAAlG,QAAA,CAAA0H,cAAA;YACAtB,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAlG,QAAA,CAAAyG,WAAA;UACA;QACA;QACAkB,UAAA;UACAzF,QAAA,CAAAiE,gBAAA,yCAAAvD,OAAA,WAAAwD,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAyC,MAAA,CAAAlG,QAAA,CAAA4H,cAAA;YACAxB,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAlG,QAAA,CAAAyG,WAAA;UACA;UACAvE,QAAA,CAAAiE,gBAAA,yCAAAvD,OAAA,WAAAwD,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAyC,MAAA,CAAAlG,QAAA,CAAA4H,cAAA;YACAxB,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAlG,QAAA,CAAAyG,WAAA;UACA;UACAvE,QAAA,CAAAiE,gBAAA,uCAAAvD,OAAA,WAAAwD,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAlG,QAAA,CAAAyG,WAAA;UACA;QACA;MACA;IACA;IACA;IACAV,2BAAA,WAAAA,4BAAA;MAAA,IAAA8B,MAAA;MACA,KAAA9F,SAAA;QACAG,QAAA,CAAAiE,gBAAA,2CAAAvD,OAAA,WAAAwD,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAAqB,MAAA,CAAA7H,QAAA,CAAA8H,eAAA;UACA1B,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAoE,MAAA,CAAA7H,QAAA,CAAA+H,kBAAA;UACA3B,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAAiB,MAAA,CAAA7H,QAAA,CAAAgI,iBAAA;UACA5B,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAe,MAAA,CAAA7H,QAAA,CAAAiI,oBAAA;UACA7B,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAa,MAAA,CAAA7H,QAAA,CAAAkI,oBAAA;UACA9B,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAW,MAAA,CAAA7H,QAAA,CAAAmI,oBAAA;UACA/B,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAS,MAAA,CAAA7H,QAAA,CAAAoI,qBAAA;UACAhC,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAAO,MAAA,CAAA7H,QAAA,CAAAqI,gBAAA;QACA;MACA;IACA;IACA;IACAvC,0BAAA,WAAAA,2BAAA;MAAA,IAAAwC,MAAA;MACA,KAAAvG,SAAA;QACAG,QAAA,CAAAiE,gBAAA,0CAAAvD,OAAA,WAAAwD,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAA8B,MAAA,CAAAtI,QAAA,CAAAuI,cAAA;UACAnC,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAA6E,MAAA,CAAAtI,QAAA,CAAAwI,oBAAA;UACApC,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAA0B,MAAA,CAAAtI,QAAA,CAAAyI,gBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAwB,MAAA,CAAAtI,QAAA,CAAA0I,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAsB,MAAA,CAAAtI,QAAA,CAAA2I,mBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAoB,MAAA,CAAAtI,QAAA,CAAA4I,mBAAA;UACAxC,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAkB,MAAA,CAAAtI,QAAA,CAAA6I,oBAAA;UACAzC,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAAgB,MAAA,CAAAtI,QAAA,CAAA8I,kBAAA;QACA;QACA5G,QAAA,CAAAiE,gBAAA,yCAAAvD,OAAA,WAAAwD,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAA8B,MAAA,CAAAtI,QAAA,CAAAuI,cAAA;UACAnC,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAA6E,MAAA,CAAAtI,QAAA,CAAA+I,oBAAA;UACA3C,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAA0B,MAAA,CAAAtI,QAAA,CAAAyI,gBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAwB,MAAA,CAAAtI,QAAA,CAAA0I,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAsB,MAAA,CAAAtI,QAAA,CAAA2I,mBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAoB,MAAA,CAAAtI,QAAA,CAAA4I,mBAAA;UACAxC,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAkB,MAAA,CAAAtI,QAAA,CAAA6I,oBAAA;UACAzC,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAAgB,MAAA,CAAAtI,QAAA,CAAAgJ,kBAAA;QACA;QACA9G,QAAA,CAAAiE,gBAAA,0CAAAvD,OAAA,WAAAwD,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAA8B,MAAA,CAAAtI,QAAA,CAAAuI,cAAA;UACAnC,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAA6E,MAAA,CAAAtI,QAAA,CAAAiJ,qBAAA;UACA7C,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAA0B,MAAA,CAAAtI,QAAA,CAAAyI,gBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAwB,MAAA,CAAAtI,QAAA,CAAA0I,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAsB,MAAA,CAAAtI,QAAA,CAAA2I,mBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAoB,MAAA,CAAAtI,QAAA,CAAA4I,mBAAA;UACAxC,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAkB,MAAA,CAAAtI,QAAA,CAAA6I,oBAAA;UACAzC,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAAgB,MAAA,CAAAtI,QAAA,CAAAkJ,mBAAA;QACA;MACA;IACA;IACA;IACAC,QAAA,WAAAA,SAAAC,KAAA;MAAA,IAAAC,GAAA,GAAAD,KAAA,CAAAC,GAAA;QAAAC,QAAA,GAAAF,KAAA,CAAAE,QAAA;MACA,IAAAA,QAAA;QACA,SAAAtJ,QAAA,CAAAuJ,WAAA;UACA;YAAA9F,KAAA,OAAAzD,QAAA,CAAAwJ;UAAA;QACA;MACA;QACA;MACA;IACA;IACAC,SAAA,WAAAA,UAAAC,KAAA;MAAA,IAAAL,GAAA,GAAAK,KAAA,CAAAL,GAAA;QAAAC,QAAA,GAAAI,KAAA,CAAAJ,QAAA;MACA,IAAAA,QAAA;QACA,SAAAtJ,QAAA,CAAAuJ,WAAA;UACA;YAAAjC,eAAA,OAAAtH,QAAA,CAAA2J;UAAA;QACA;MACA;QACA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAR,GAAA,GAAAQ,KAAA,CAAAR,GAAA;QAAAC,QAAA,GAAAO,KAAA,CAAAP,QAAA;MACA;QAAA7F,KAAA,OAAAzD,QAAA,CAAA8J;MAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA;MAAA,IAAAX,GAAA,GAAAW,KAAA,CAAAX,GAAA;QAAAC,QAAA,GAAAU,KAAA,CAAAV,QAAA;MACA;QAAAhC,eAAA,OAAAtH,QAAA,CAAAiK;MAAA;IACA;IACA;IACAjE,0BAAA,WAAAA,2BAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;IAAA,CACA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MACA,IAAAiE,GAAA;MACA,SAAAlK,QAAA,CAAAmK,SAAA,EAAAD,GAAA,CAAA/G,IAAA;MACA,SAAAnD,QAAA,CAAAoK,SAAA,EAAAF,GAAA,CAAA/G,IAAA;MACA,SAAAnD,QAAA,CAAAqK,YAAA;QACAH,GAAA,CAAA/G,IAAA;QACA,SAAAnD,QAAA,CAAAsK,SAAA,EAAAJ,GAAA,CAAA/G,IAAA;QACA+G,GAAA,CAAA/G,IAAA;MACA;MACA,SAAAnD,QAAA,CAAAuK,UAAA,EAAAL,GAAA,CAAA/G,IAAA;MACA,KAAAlD,OAAA,GAAAiK,GAAA,CAAAM,IAAA;MACA,KAAAxK,QAAA,CAAAyK,WAAA;IACA;IAEAlK,IAAA,WAAAA,KAAA,GACA;IACAmE,MAAA,WAAAA,OAAA;MACA,KAAApF,SAAA;MACA,KAAAkB,WAAA;IACA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAkK,MAAA;MACA,KAAAjL,eAAA;MACA,IAAA8B,MAAA;QACAoJ,IAAA,OAAArL,SAAA;QACAsL,KAAA,OAAArL,QAAA;QACAsL,IAAA;MACA;MAGA,SAAAxM,UAAA,CAAAQ,UAAA,eAAAR,UAAA,CAAAQ,UAAA,IAAAiM,SAAA;QACAvJ,MAAA,4BAAAlD,UAAA,CAAAQ,UAAA;MACA;MAEA,SAAAR,UAAA,CAAAS,WAAA,eAAAT,UAAA,CAAAS,WAAA,IAAAgM,SAAA;QACAvJ,MAAA,6BAAAlD,UAAA,CAAAS,WAAA;MACA;MAEA,SAAAT,UAAA,CAAAU,cAAA,eAAAV,UAAA,CAAAU,cAAA,IAAA+L,SAAA;QACAvJ,MAAA,gCAAAlD,UAAA,CAAAU,cAAA;MACA;MAEAwC,MAAA;;MAGA,KAAAa,KAAA;QACAC,GAAA;QACAC,MAAA;QACAf,MAAA,EAAAA;MACA,GAAAgB,IAAA,WAAAwI,KAAA;QAAA,IAAA3M,IAAA,GAAA2M,KAAA,CAAA3M,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAqE,IAAA;UACAiI,MAAA,CAAArL,QAAA,GAAAjB,IAAA,CAAAA,IAAA,CAAA4M,IAAA;UACAN,MAAA,CAAAlL,SAAA,GAAApB,IAAA,CAAAA,IAAA,CAAA6G,KAAA;QACA;UACAyF,MAAA,CAAArL,QAAA;UACAqL,MAAA,CAAAlL,SAAA;QACA;QACAkL,MAAA,CAAAjL,eAAA;MACA;;MAEA;MACA;IACA;IACA;IACAwL,gBAAA,WAAAA,iBAAAlK,GAAA;MACA,KAAAxB,QAAA,GAAAwB,GAAA;MACA,KAAAzB,SAAA;MACA,KAAAkB,WAAA;IACA;IACA;IACA0K,mBAAA,WAAAA,oBAAAnK,GAAA;MACA,KAAAzB,SAAA,GAAAyB,GAAA;MACA,KAAAP,WAAA;IACA;IACA;IACA2K,sBAAA,WAAAA,uBAAApK,GAAA;MACA,KAAArB,kBAAA,GAAAqB,GAAA;IACA;IACA;IACAqK,kBAAA,WAAAA,mBAAA1M,EAAA,EAAAwE,IAAA;MAAA,IAAAmI,MAAA;MACA,KAAA1L,QAAA;MACA,KAAAI,eAAA;MACA,KAAAuL,oBAAA;MACA,IAAApI,IAAA;QACAA,IAAA;MACA;MACA,KAAAnB,SAAA;QACAsJ,MAAA,CAAAE,KAAA,CAAAC,WAAA,CAAAjL,IAAA,CAAA7B,EAAA,EAAAwE,IAAA;MACA;IACA;IACA;IACAuI,QAAA,WAAAA,SAAAC,IAAA;MACAvH,MAAA,CAAAwH,IAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAAlN,EAAA;MAAA,IAAAmN,MAAA;MACA,IAAAC,GAAA,GAAApN,EAAA,IAAAqN,MAAA,CAAArN,EAAA,UAAAgB,kBAAA,CAAAsM,GAAA,WAAAnJ,IAAA;QACA,OAAAkJ,MAAA,CAAAlJ,IAAA,CAAAnE,EAAA;MACA;MAEA,KAAAuN,QAAA,6BAAAC,MAAA,CAAAxN,EAAA;QACAyN,iBAAA;QACAC,gBAAA;QACAlJ,IAAA;MACA,GAAAX,IAAA;QACAsJ,MAAA,CAAAzJ,KAAA;UACAC,GAAA;UACAC,MAAA;UACAlE,IAAA,EAAA0N;QACA,GAAAvJ,IAAA,WAAA8J,KAAA;UAAA,IAAAjO,IAAA,GAAAiO,KAAA,CAAAjO,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAAqE,IAAA;YACAoJ,MAAA,CAAAvH,QAAA;cACAC,OAAA;cACArB,IAAA;cACAsB,QAAA;cACAC,OAAA,WAAAA,QAAA;gBACAoH,MAAA,CAAAnH,MAAA;cACA;YACA;UACA;YACAmH,MAAA,CAAAvH,QAAA,CAAAgI,KAAA,CAAAlO,IAAA,CAAAmO,GAAA;UACA;QACA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA9N,EAAA;MACA;MACA,KAAA0D,KAAA;QACAC,GAAA,+BAAA3D,EAAA;QACA4D,MAAA;QACA;MACA,GAAAC,IAAA,WAAAkK,KAAA;QAAA,IAAArO,IAAA,GAAAqO,KAAA,CAAArO,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAqE,IAAA;UACAiK,KAAA;QACA;MACA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAvO,IAAA;MACA,IAAAkD,KAAA;MACAA,KAAA,CAAAc,KAAA;QACAC,GAAA,mCAAAjE,IAAA,CAAAsN,IAAA;QACApJ,MAAA;MACA,GAAAC,IAAA,WAAAqK,MAAA;QAAA,IAAAxO,IAAA,GAAAwO,MAAA,CAAAxO,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAqE,IAAA;UACAnB,KAAA,CAAAgD,QAAA;YACAC,OAAA;YACArB,IAAA;YACAsB,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAnD,KAAA,CAAAoD,MAAA;YACA;UACA;QACA;UACApD,KAAA,CAAAgD,QAAA,CAAAgI,KAAA,CAAAlO,IAAA,CAAAmO,GAAA;QACA;MACA;IAEA;IACA;IACAM,iBAAA,WAAAA,kBAAAzO,IAAA;MACA,KAAAkG,QAAA,CAAAgI,KAAA;IACA;EACA;AACA", "ignoreList": []}]}