{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\common\\BreadCrumbs.vue?vue&type=style&index=0&id=b290fa88&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\common\\BreadCrumbs.vue", "mtime": 1750583733629}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouYXBwLWJyZWFkY3J1bWIgew0KICBkaXNwbGF5OiBibG9jazsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBsaW5lLWhlaWdodDogNTBweDsNCg0KICAuYm94IHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGhlaWdodDogMTAwJTsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgfQ0KDQogIC5uby1yZWRpcmVjdCB7DQogICAgY29sb3I6ICM5N2E4YmU7DQogICAgY3Vyc29yOiB0ZXh0Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["BreadCrumbs.vue"], "names": [], "mappings": ";AA0FA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "BreadCrumbs.vue", "sourceRoot": "src/components/common", "sourcesContent": ["<template>\r\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"(�?�?�?\" style=\"height:50px;backgroundColor:rgba(224, 240, 233, 1);borderRadius:0px;padding:0px 20px 0px 20px;boxShadow:4px 4px 2px#FFB3A7;borderWidth:0px;borderStyle:dotted solid double dashed;borderColor:rgba(255, 179, 167, 1);\">\r\n    <transition-group name=\"breadcrumb\" class=\"box\" :style=\"2==1?'justifyContent:flex-start;':2==2?'justifyContent:center;':'justifyContent:flex-end;'\">\r\n      <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\r\n        <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.name }}</span>\r\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.name }}</a>\r\n      </el-breadcrumb-item>\r\n    </transition-group>\r\n  </el-breadcrumb>\r\n</template>\r\n\r\n<script>\r\nimport pathToRegexp from 'path-to-regexp'\r\nimport { generateTitle } from '@/utils/i18n'\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.getBreadcrumb()\r\n    }\r\n  },\r\n  created() {\r\n    this.getBreadcrumb()\r\n    this.breadcrumbStyleChange()\r\n  },\r\n  methods: {\r\n    generateTitle,\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let route = this.$route\r\n      let matched = route.matched.filter(item => item.meta)\r\n      const first = matched[0]\r\n      matched = [{ path: '/index' }].concat(matched)\r\n\r\n      this.levelList = matched.filter(item => item.meta)\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name\r\n      if (!name) {\r\n        return false\r\n      }\r\n      return name.trim().toLocaleLowerCase() === 'Index'.toLocaleLowerCase()\r\n    },\r\n    pathCompile(path) {\r\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\r\n      const { params } = this.$route\r\n      var toPath = pathToRegexp.compile(path)\r\n      return toPath(params)\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item\r\n      if (redirect) {\r\n        this.$router.push(redirect)\r\n        return\r\n      }\r\n      this.$router.push(path)\r\n    },\r\n    breadcrumbStyleChange(val) {\r\n      this.$nextTick(()=>{\r\n        document.querySelectorAll('.app-breadcrumb .el-breadcrumb__separator').forEach(el=>{\r\n          el.innerText = \"(�?�?�?\"\r\n          el.style.color = \"rgba(43, 115, 176, 1)\"\r\n        })\r\n        document.querySelectorAll('.app-breadcrumb .el-breadcrumb__inner a').forEach(el=>{\r\n          el.style.color = \"rgba(77, 84, 222, 1)\"\r\n        })\r\n        document.querySelectorAll('.app-breadcrumb .el-breadcrumb__inner .no-redirect').forEach(el=>{\r\n          el.style.color = \"rgba(17, 18, 18, 1)\"\r\n        })\r\n\r\n        let str = \"vertical\"\r\n        if(\"vertical\" === str) {\r\n          let headHeight = \"60px\"\r\n          headHeight = parseInt(headHeight) + 10 + 'px'\r\n          document.querySelectorAll('.app-breadcrumb').forEach(el=>{\r\n            el.style.marginTop = headHeight\r\n          })\r\n        }\r\n\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-breadcrumb {\r\n  display: block;\r\n  font-size: 14px;\r\n  line-height: 50px;\r\n\r\n  .box {\r\n    display: flex;\r\n    width: 100%;\r\n    height: 100%;\r\n    justify-content: flex-start;\r\n    align-items: center;\r\n  }\r\n\r\n  .no-redirect {\r\n    color: #97a8be;\r\n    cursor: text;\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}