{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexHeader.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexHeader.vue", "mtime": 1750583733736}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["IndexHeader.vue"], "names": [], "mappings": ";AA4BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IndexHeader.vue", "sourceRoot": "src/components/index", "sourcesContent": ["<template>\r\n    <!-- <el-header>\r\n        <el-menu background-color=\"#00c292\" text-color=\"#FFFFFF\" active-text-color=\"#FFFFFF\" mode=\"horizontal\">\r\n            <div class=\"fl title\">{{this.$project.projectName}}</div>\r\n            <div class=\"fr logout\" style=\"display:flex;\">\r\n                <el-menu-item index=\"3\">\r\n                    <div>{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</div>\r\n                </el-menu-item>\r\n                <el-menu-item @click=\"onLogout\" index=\"2\">\r\n                    <div>退出登�?/div>\r\n                </el-menu-item>\r\n            </div>\r\n        </el-menu>\r\n    </el-header> -->\r\n    <div class=\"navbar\" :style=\"{backgroundColor:heads.headBgColor,height:heads.headHeight,boxShadow:heads.headBoxShadow,lineHeight:heads.headHeight}\">\r\n        <div class=\"title-menu\" :style=\"{justifyContent:heads.headTitleStyle=='1'?'flex-start':'center'}\">\r\n            <el-image v-if=\"heads.headTitleImg\" class=\"title-img\" :style=\"{width:heads.headTitleImgWidth,height:heads.headTitleImgHeight,boxShadow:heads.headTitleImgBoxShadow,borderRadius:heads.headTitleImgBorderRadius}\" :src=\"heads.headTitleImgUrl\" fit=\"cover\"></el-image>\r\n            <div class=\"title-name\" :style=\"{color:heads.headFontColor,fontSize:heads.headFontSize}\">{{this.$project.projectName}}</div>\r\n        </div>\r\n        <div class=\"right-menu\">\r\n            <div class=\"user-info\" :style=\"{color:heads.headUserInfoFontColor,fontSize:heads.headUserInfoFontSize}\">{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</div>\r\n\t\t\t<div class=\"logout\" :style=\"{color:heads.headLogoutFontColor,fontSize:heads.headLogoutFontSize}\" @click=\"onIndexTap\">退出到前台</div>\r\n            <div class=\"logout\" :style=\"{color:heads.headLogoutFontColor,fontSize:heads.headLogoutFontSize}\" @click=\"onLogout\">退出登�?/div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        data() {\r\n            return {\r\n                dialogVisible: false,\r\n                ruleForm: {},\r\n                user: {},\r\n                heads: {\"headLogoutFontHoverColor\":\"#fff\",\"headFontSize\":\"20px\",\"headUserInfoFontColor\":\"#333\",\"headBoxShadow\":\"0 1px 6px #444\",\"headTitleImgHeight\":\"44px\",\"headLogoutFontHoverBgColor\":\"#333\",\"headFontColor\":\"#000\",\"headTitleImg\":false,\"headHeight\":\"60px\",\"headTitleImgBorderRadius\":\"22px\",\"headTitleImgUrl\":\"http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg\",\"headBgColor\":\"#E0F0E9\",\"headTitleImgBoxShadow\":\"0 1px 6px #444\",\"headLogoutFontColor\":\"#333\",\"headUserInfoFontSize\":\"16px\",\"headTitleImgWidth\":\"44px\",\"headTitleStyle\":\"1\",\"headLogoutFontSize\":\"16px\"},\r\n            };\r\n        },\r\n        created() {\r\n            this.setHeaderStyle()\r\n        },\r\n        mounted() {\r\n            let sessionTable = this.$storage.get(\"sessionTable\")\r\n            this.$http({\r\n                url: sessionTable + '/session',\r\n                method: \"get\"\r\n            }).then(({\r\n                         data\r\n                     }) => {\r\n                if (data && data.code === 0) {\r\n                    this.user = data.data;\r\n                } else {\r\n                    let message = this.$message\r\n                    message.error(data.msg);\r\n                }\r\n            });\r\n        },\r\n        methods: {\r\n            onLogout() {\r\n                let storage = this.$storage\r\n                let router = this.$router\r\n                storage.remove(\"Token\");\r\n                router.replace({\r\n                    name: \"login\"\r\n                });\r\n            },\r\n            onIndexTap(){\r\n                window.location.href = `${this.$base.indexUrl}`\r\n            },\r\n            setHeaderStyle() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.navbar .right-menu .logout').forEach(el=>{\r\n                        el.addEventListener(\"mouseenter\", e => {\r\n                            e.stopPropagation()\r\n                            el.style.backgroundColor = this.heads.headLogoutFontHoverBgColor\r\n                            el.style.color = this.heads.headLogoutFontHoverColor\r\n                        })\r\n                        el.addEventListener(\"mouseleave\", e => {\r\n                            e.stopPropagation()\r\n                            el.style.backgroundColor = \"transparent\"\r\n                            el.style.color = this.heads.headLogoutFontColor\r\n                        })\r\n                    })\r\n                })\r\n            },\r\n        }\r\n    };\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n    .navbar {\r\n        height: 60px;\r\n        line-height: 60px;\r\n        width: 100%;\r\n        padding: 0 34px;\r\n        box-sizing: border-box;\r\n        background-color: #ff00ff;\r\n        position: relative;\r\n        z-index: 111;\r\n\r\n    .right-menu {\r\n        position: absolute;\r\n        right: 34px;\r\n        top: 0;\r\n        height: 100%;\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        align-items: center;\r\n        z-index: 111;\r\n\r\n    .user-info {\r\n        font-size: 16px;\r\n        color: red;\r\n        padding: 0 12px;\r\n    }\r\n\r\n    .logout {\r\n        font-size: 16px;\r\n        color: red;\r\n        padding: 0 12px;\r\n        cursor: pointer;\r\n    }\r\n\r\n    }\r\n\r\n    .title-menu {\r\n        display: flex;\r\n        justify-content: flex-start;\r\n        align-items: center;\r\n        width: 100%;\r\n        height: 100%;\r\n\r\n    .title-img {\r\n        width: 44px;\r\n        height: 44px;\r\n        border-radius: 22px;\r\n        box-shadow: 0 1px 6px #444;\r\n        margin-right: 16px;\r\n    }\r\n\r\n    .title-name {\r\n        font-size: 24px;\r\n        color: #fff;\r\n        font-weight: 700;\r\n    }\r\n    }\r\n    }\r\n    // .el-header .fr {\r\n       // \tfloat: right;\r\n       // }\r\n\r\n    // .el-header .fl {\r\n       // \tfloat: left;\r\n       // }\r\n\r\n    // .el-header {\r\n       // \twidth: 100%;\r\n       // \tcolor: #333;\r\n       // \ttext-align: center;\r\n       // \tline-height: 60px;\r\n       // \tpadding: 0;\r\n       // \tz-index: 99;\r\n       // }\r\n\r\n    // .logo {\r\n       // \twidth: 60px;\r\n       // \theight: 60px;\r\n       // \tmargin-left: 70px;\r\n       // }\r\n\r\n    // .avator {\r\n       // \twidth: 40px;\r\n       // \theight: 40px;\r\n       // \tbackground: #ffffff;\r\n       // \tborder-radius: 50%;\r\n       // }\r\n\r\n    // .title {\r\n       // \tcolor: #ffffff;\r\n       // \tfont-size: 20px;\r\n       // \tfont-weight: bold;\r\n       // \tmargin-left: 20px;\r\n       // }\r\n</style>\r\n\r\n"]}]}