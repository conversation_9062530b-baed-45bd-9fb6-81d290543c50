{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexMain.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexMain.vue", "mtime": 1642386767437}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCBtZW51IGZyb20gIkAvdXRpbHMvbWVudSI7CmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbWVudUxpc3Q6IFtdLAogICAgICByb2xlOiAiIiwKICAgICAgY3VycmVudEluZGV4OiAtMiwKICAgICAgaXRlbU1lbnU6IFtdLAogICAgICB0aXRsZTogJycKICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdmFyIG1lbnVzID0gbWVudS5saXN0KCk7CiAgICB0aGlzLm1lbnVMaXN0ID0gbWVudXM7CiAgICB0aGlzLnJvbGUgPSB0aGlzLiRzdG9yYWdlLmdldCgicm9sZSIpOwogIH0sCiAgbWV0aG9kczogewogICAgbWVudUhhbmRsZXI6IGZ1bmN0aW9uIG1lbnVIYW5kbGVyKG1lbnUpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIG5hbWU6IG1lbnUudGFibGVOYW1lCiAgICAgIH0pOwogICAgICB0aGlzLnRpdGxlID0gbWVudS5tZW51OwogICAgfSwKICAgIHRpdGxlQ2hhbmdlOiBmdW5jdGlvbiB0aXRsZUNoYW5nZShpbmRleCwgbWVudXMpIHsKICAgICAgdGhpcy5jdXJyZW50SW5kZXggPSBpbmRleDsKICAgICAgdGhpcy5pdGVtTWVudSA9IG1lbnVzOwogICAgICBjb25zb2xlLmxvZyhtZW51cyk7CiAgICB9LAogICAgaG9tZUNoYW5nZTogZnVuY3Rpb24gaG9tZUNoYW5nZShpbmRleCkgewogICAgICB0aGlzLml0ZW1NZW51ID0gW107CiAgICAgIHRoaXMudGl0bGUgPSAiIjsKICAgICAgdGhpcy5jdXJyZW50SW5kZXggPSBpbmRleDsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIG5hbWU6ICdob21lJwogICAgICB9KTsKICAgIH0sCiAgICBjZW50ZXJDaGFuZ2U6IGZ1bmN0aW9uIGNlbnRlckNoYW5nZShpbmRleCkgewogICAgICB0aGlzLml0ZW1NZW51ID0gW3sKICAgICAgICAiYnV0dG9ucyI6IFsi5paw5aKeIiwgIuafpeeciyIsICLkv67mlLkiLCAi5Yig6ZmkIl0sCiAgICAgICAgIm1lbnUiOiAi5L+u5pS55a+G56CBIiwKICAgICAgICAidGFibGVOYW1lIjogInVwZGF0ZVBhc3N3b3JkIgogICAgICB9LCB7CiAgICAgICAgImJ1dHRvbnMiOiBbIuaWsOWiniIsICLmn6XnnIsiLCAi5L+u5pS5IiwgIuWIoOmZpCJdLAogICAgICAgICJtZW51IjogIuS4quS6uuS/oeaBryIsCiAgICAgICAgInRhYmxlTmFtZSI6ICJjZW50ZXIiCiAgICAgIH1dOwogICAgICB0aGlzLnRpdGxlID0gIiI7CiAgICAgIHRoaXMuY3VycmVudEluZGV4ID0gaW5kZXg7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBuYW1lOiAnaG9tZScKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["menu", "data", "menuList", "role", "currentIndex", "itemMenu", "title", "mounted", "menus", "list", "$storage", "get", "methods", "menu<PERSON><PERSON><PERSON>", "$router", "push", "name", "tableName", "titleChange", "index", "console", "log", "homeChange", "centerChange"], "sources": ["src/components/index/IndexMain.vue"], "sourcesContent": ["<template>\r\n\t<el-main>\r\n\t\t<bread-crumbs :title=\"title\" class=\"bread-crumbs\"></bread-crumbs>\r\n\t\t<router-view class=\"router-view\"></router-view>\r\n\t</el-main>\r\n</template>\r\n<script>\r\n\timport menu from \"@/utils/menu\";\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmenuList: [],\r\n\t\t\t\trole: \"\",\r\n\t\t\t\tcurrentIndex: -2,\r\n\t\t\t\titemMenu: [],\r\n\t\t\t\ttitle: ''\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tlet menus = menu.list();\r\n\t\t\tthis.menuList = menus;\r\n\t\t\tthis.role = this.$storage.get(\"role\");\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tmenuHandler(menu) {\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tname: menu.tableName\r\n\t\t\t\t});\r\n\t\t\t\tthis.title = menu.menu;\r\n\t\t\t},\r\n\t\t\ttitleChange(index, menus) {\r\n\t\t\t\tthis.currentIndex = index\r\n\t\t\t\tthis.itemMenu = menus;\r\n\t\t\t\tconsole.log(menus);\r\n\t\t\t},\r\n\t\t\thomeChange(index) {\r\n\t\t\t\tthis.itemMenu = [];\r\n\t\t\t\tthis.title = \"\"\r\n\t\t\t\tthis.currentIndex = index\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tname: 'home'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcenterChange(index) {\r\n\t\t\t\tthis.itemMenu = [{\r\n\t\t\t\t\t\"buttons\": [\"新增\", \"查看\", \"修改\", \"删除\"],\r\n\t\t\t\t\t\"menu\": \"修改密码\",\r\n\t\t\t\t\t\"tableName\": \"updatePassword\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\t\"buttons\": [\"新增\", \"查看\", \"修改\", \"删除\"],\r\n\t\t\t\t\t\"menu\": \"个人信息\",\r\n\t\t\t\t\t\"tableName\": \"center\"\r\n\t\t\t\t}];\r\n\t\t\t\tthis.title = \"\"\r\n\t\t\t\tthis.currentIndex = index\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tname: 'home'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\ta {\r\n\t\ttext-decoration: none;\r\n\t\tcolor: #555;\r\n\t}\r\n\r\n\ta:hover {\r\n\t\tbackground: #00c292;\r\n\t}\r\n\r\n\t.nav-list {\r\n\t\twidth: 100%;\r\n\t\tmargin: 0 auto;\r\n\t\ttext-align: left;\r\n\t\tmargin-top: 20px;\r\n\r\n\t\t.nav-title {\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tfont-size: 15px;\r\n\t\t\tcolor: #333;\r\n\t\t\tpadding: 15px 25px;\r\n\t\t\tborder: none;\r\n\t\t}\r\n\r\n\t\t.nav-title.active {\r\n\t\t\tcolor: #555;\r\n\t\t\tcursor: default;\r\n\t\t\tbackground-color: #fff;\r\n\t\t}\r\n\t}\r\n\r\n\t.nav-item {\r\n\t\tmargin-top: 20px;\r\n\t\tbackground: #FFFFFF;\r\n\t\tpadding: 15px 0;\r\n\r\n\t\t.menu {\r\n\t\t\tpadding: 15px 25px;\r\n\t\t}\r\n\t}\r\n\r\n\t.el-main {\r\n\t\tbackground-color: #F6F8FA;\r\n\t\tpadding: 0 24px;\r\n\t\t// padding-top: 60px;\r\n\t}\r\n\r\n\t.router-view {\r\n\t\tpadding: 10px;\r\n\t\tmargin-top: 10px;\r\n\t\tbackground: #FFFFFF;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.bread-crumbs {\r\n\t\twidth: 100%;\r\n\t\t// border-bottom: 1px solid #e9eef3;\r\n\t\t// border-top: 1px solid #e9eef3;\r\n\t\tmargin-top: 10px;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n</style>\r\n"], "mappings": ";AAOA,OAAAA,IAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,YAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,KAAA,GAAAR,IAAA,CAAAS,IAAA;IACA,KAAAP,QAAA,GAAAM,KAAA;IACA,KAAAL,IAAA,QAAAO,QAAA,CAAAC,GAAA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAAb,IAAA;MACA,KAAAc,OAAA,CAAAC,IAAA;QACAC,IAAA,EAAAhB,IAAA,CAAAiB;MACA;MACA,KAAAX,KAAA,GAAAN,IAAA,CAAAA,IAAA;IACA;IACAkB,WAAA,WAAAA,YAAAC,KAAA,EAAAX,KAAA;MACA,KAAAJ,YAAA,GAAAe,KAAA;MACA,KAAAd,QAAA,GAAAG,KAAA;MACAY,OAAA,CAAAC,GAAA,CAAAb,KAAA;IACA;IACAc,UAAA,WAAAA,WAAAH,KAAA;MACA,KAAAd,QAAA;MACA,KAAAC,KAAA;MACA,KAAAF,YAAA,GAAAe,KAAA;MACA,KAAAL,OAAA,CAAAC,IAAA;QACAC,IAAA;MACA;IACA;IACAO,YAAA,WAAAA,aAAAJ,KAAA;MACA,KAAAd,QAAA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;MACA,KAAAC,KAAA;MACA,KAAAF,YAAA,GAAAe,KAAA;MACA,KAAAL,OAAA,CAAAC,IAAA;QACAC,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}