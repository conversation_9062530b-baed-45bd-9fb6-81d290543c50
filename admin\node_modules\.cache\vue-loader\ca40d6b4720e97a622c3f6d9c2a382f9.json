{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdiCollection\\add-or-update.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdiCollection\\add-or-update.vue", "mtime": 1642387870184}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogICAgaW1wb3J0IHN0eWxlSnMgZnJvbSAiLi4vLi4vLi4vdXRpbHMvc3R5bGUuanMiOw0KICAgIC8vIOaVsOWtl++8jOmCruS7tu+8jOaJi+acuu+8jHVybO+8jOi6q+S7veivgeagoemqjA0KICAgIGltcG9ydCB7IGlzTnVtYmVyLGlzSW50TnVtZXIsaXNFbWFpbCxpc1Bob25lLCBpc01vYmlsZSxpc1VSTCxjaGVja0lkQ2FyZCB9IGZyb20gIkAvdXRpbHMvdmFsaWRhdGUiOw0KICAgIGV4cG9ydCBkZWZhdWx0IHsNCiAgICAgICAgZGF0YSgpIHsNCiAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgYWRkRWRpdEZvcm06bnVsbCwNCiAgICAgICAgICAgICAgICBpZDogJycsDQogICAgICAgICAgICAgICAgdHlwZTogJycsDQogICAgICAgICAgICAgICAgc2Vzc2lvblRhYmxlIDogIiIsLy/nmbvlvZXotKbmiLfmiYDlnKjooajlkI0NCiAgICAgICAgICAgICAgICByb2xlIDogIiIsLy/mnYPpmZANCiAgICAgICAgICAgICAgICBjaGFuZ2RpRm9ybToge30sDQogICAgICAgICAgICAgICAgeW9uZ2h1Rm9ybToge30sDQogICAgICAgICAgICAgICAgcm86ew0KICAgICAgICAgICAgICAgICAgICBjaGFuZ2RpSWQ6IGZhbHNlLA0KICAgICAgICAgICAgICAgICAgICB5b25naHVJZDogZmFsc2UsDQogICAgICAgICAgICAgICAgICAgIGNoYW5nZGlDb2xsZWN0aW9uVHlwZXM6IGZhbHNlLA0KICAgICAgICAgICAgICAgICAgICBpbnNlcnRUaW1lOiBmYWxzZSwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIHJ1bGVGb3JtOiB7DQogICAgICAgICAgICAgICAgICAgIGNoYW5nZGlJZDogJycsDQogICAgICAgICAgICAgICAgICAgIHlvbmdodUlkOiAnJywNCiAgICAgICAgICAgICAgICAgICAgY2hhbmdkaUNvbGxlY3Rpb25UeXBlczogJycsDQogICAgICAgICAgICAgICAgICAgIGluc2VydFRpbWU6ICcnLA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgY2hhbmdkaUNvbGxlY3Rpb25UeXBlc09wdGlvbnMgOiBbXSwNCiAgICAgICAgICAgICAgICBjaGFuZ2RpT3B0aW9ucyA6IFtdLA0KICAgICAgICAgICAgICAgIHlvbmdodU9wdGlvbnMgOiBbXSwNCiAgICAgICAgICAgICAgICBydWxlczogew0KICAgICAgICAgICAgICAgICAgIGNoYW5nZGlJZDogWw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+WcuuWcsOS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyAgcGF0dGVybjogL15bMS05XVswLTldKiQvLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICflj6rlhYHorrjovpPlhaXmlbTmlbAnLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICdibHVyJw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgICAgIHlvbmdodUlkOiBbDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn55So5oi35LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ICBwYXR0ZXJuOiAvXlsxLTldWzAtOV0qJC8sDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogJ+WPquWFgeiuuOi+k+WFpeaVtOaVsCcsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogJ2JsdXInDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICAgICAgICAgY2hhbmdkaUNvbGxlY3Rpb25UeXBlczogWw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+exu+Wei+S4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyAgcGF0dGVybjogL15bMS05XVswLTldKiQvLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICflj6rlhYHorrjovpPlhaXmlbTmlbAnLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICdibHVyJw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgICAgIGluc2VydFRpbWU6IFsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfmlLbol4/ml7bpl7TkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9Ow0KICAgICAgICB9LA0KICAgICAgICBwcm9wczogWyJwYXJlbnQiXSwNCiAgICAgICAgY29tcHV0ZWQ6IHsNCiAgICAgICAgfSwNCiAgICAgICAgY3JlYXRlZCgpIHsNCiAgICAgICAgICAgIC8v6I635Y+W5b2T5YmN55m75b2V55So5oi355qE5L+h5oGvDQogICAgICAgICAgICB0aGlzLnNlc3Npb25UYWJsZSA9IHRoaXMuJHN0b3JhZ2UuZ2V0KCJzZXNzaW9uVGFibGUiKTsNCiAgICAgICAgICAgIHRoaXMucm9sZSA9IHRoaXMuJHN0b3JhZ2UuZ2V0KCJyb2xlIik7DQoNCiAgICAgICAgICAgIGlmICh0aGlzLnJvbGUgIT0gIueuoeeQhuWRmCIpew0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy5hZGRFZGl0Rm9ybSA9IHN0eWxlSnMuYWRkU3R5bGUoKTsNCiAgICAgICAgICAgIHRoaXMuYWRkRWRpdFN0eWxlQ2hhbmdlKCkNCiAgICAgICAgICAgIHRoaXMuYWRkRWRpdFVwbG9hZFN0eWxlQ2hhbmdlKCkNCiAgICAgICAgICAgIC8v6I635Y+W5LiL5ouJ5qGG5L+h5oGvDQogICAgICAgICAgICAgICAgdGhpcy4kaHR0cCh7DQogICAgICAgICAgICAgICAgICAgIHVybDpgZGljdGlvbmFyeS9wYWdlP3BhZ2U9MSZsaW1pdD0xMDAmc29ydD0mb3JkZXI9JmRpY0NvZGU9Y2hhbmdkaV9jb2xsZWN0aW9uX3R5cGVzYCwNCiAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiAiZ2V0Ig0KICAgICAgICAgICAgICAgIH0pLnRoZW4oKHsgZGF0YSB9KSA9PiB7DQogICAgICAgICAgICAgICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jaGFuZ2RpQ29sbGVjdGlvblR5cGVzT3B0aW9ucyA9IGRhdGEuZGF0YS5saXN0Ow0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSk7DQoNCiAgICAgICAgIHRoaXMuJGh0dHAoew0KICAgICAgICAgICAgIHVybDogYGNoYW5nZGkvcGFnZT9wYWdlPTEmbGltaXQ9MTAwYCwNCiAgICAgICAgICAgICBtZXRob2Q6ICJnZXQiDQogICAgICAgICB9KS50aGVuKCh7IGRhdGEgfSkgPT4gew0KICAgICAgICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgew0KICAgICAgICAgICAgICAgIHRoaXMuY2hhbmdkaU9wdGlvbnMgPSBkYXRhLmRhdGEubGlzdDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgIH0pOw0KICAgICAgICAgdGhpcy4kaHR0cCh7DQogICAgICAgICAgICAgdXJsOiBgeW9uZ2h1L3BhZ2U/cGFnZT0xJmxpbWl0PTEwMGAsDQogICAgICAgICAgICAgbWV0aG9kOiAiZ2V0Ig0KICAgICAgICAgfSkudGhlbigoeyBkYXRhIH0pID0+IHsNCiAgICAgICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmNvZGUgPT09IDApIHsNCiAgICAgICAgICAgICAgICB0aGlzLnlvbmdodU9wdGlvbnMgPSBkYXRhLmRhdGEubGlzdDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgIH0pOw0KDQogICAgICAgIH0sDQogICAgICAgIG1vdW50ZWQoKSB7DQogICAgICAgIH0sDQogICAgICAgIG1ldGhvZHM6IHsNCiAgICAgICAgICAgIC8vIOS4i+i9vQ0KICAgICAgICAgICAgZG93bmxvYWQoZmlsZSl7DQogICAgICAgICAgICAgICAgd2luZG93Lm9wZW4oYCR7ZmlsZX1gKQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8vIOWIneWni+WMlg0KICAgICAgICAgICAgaW5pdChpZCx0eXBlKSB7DQogICAgICAgICAgICAgICAgaWYgKGlkKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuaWQgPSBpZDsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy50eXBlID0gdHlwZTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgaWYodGhpcy50eXBlPT0naW5mbyd8fHRoaXMudHlwZT09J2Vsc2UnKXsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5pbmZvKGlkKTsNCiAgICAgICAgICAgICAgICB9ZWxzZSBpZih0aGlzLnR5cGU9PSdjcm9zcycpew0KICAgICAgICAgICAgICAgICAgICB2YXIgb2JqID0gdGhpcy4kc3RvcmFnZS5nZXRPYmooJ2Nyb3NzT2JqJyk7DQogICAgICAgICAgICAgICAgICAgIGZvciAodmFyIG8gaW4gb2JqKXsNCg0KICAgICAgICAgICAgICAgICAgICAgIGlmKG89PSdjaGFuZ2RpSWQnKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5jaGFuZ2RpSWQgPSBvYmpbb107DQogICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucm8uY2hhbmdkaUlkID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIGlmKG89PSd5b25naHVJZCcpew0KICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLnlvbmdodUlkID0gb2JqW29dOw0KICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJvLnlvbmdodUlkID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIGlmKG89PSdjaGFuZ2RpQ29sbGVjdGlvblR5cGVzJyl7DQogICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uY2hhbmdkaUNvbGxlY3Rpb25UeXBlcyA9IG9ialtvXTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5yby5jaGFuZ2RpQ29sbGVjdGlvblR5cGVzID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIGlmKG89PSdpbnNlcnRUaW1lJyl7DQogICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uaW5zZXJ0VGltZSA9IG9ialtvXTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5yby5pbnNlcnRUaW1lID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIC8vIOiOt+WPlueUqOaIt+S/oeaBrw0KICAgICAgICAgICAgICAgIHRoaXMuJGh0dHAoew0KICAgICAgICAgICAgICAgICAgICB1cmw6YCR7dGhpcy4kc3RvcmFnZS5nZXQoInNlc3Npb25UYWJsZSIpfS9zZXNzaW9uYCwNCiAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiAiZ2V0Ig0KICAgICAgICAgICAgICAgIH0pLnRoZW4oKHsgZGF0YSB9KSA9PiB7DQogICAgICAgICAgICAgICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgdmFyIGpzb24gPSBkYXRhLmRhdGE7DQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGRhdGEubXNnKTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGNoYW5nZGlDaGFuZ2UoaWQpew0KICAgICAgICAgICAgICAgIHRoaXMuJGh0dHAoew0KICAgICAgICAgICAgICAgICAgICB1cmw6IGBjaGFuZ2RpL2luZm8vYCtpZCwNCiAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiAiZ2V0Ig0KICAgICAgICAgICAgICAgIH0pLnRoZW4oKHsgZGF0YSB9KSA9PiB7DQogICAgICAgICAgICAgICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jaGFuZ2RpRm9ybSA9IGRhdGEuZGF0YTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHlvbmdodUNoYW5nZShpZCl7DQogICAgICAgICAgICAgICAgdGhpcy4kaHR0cCh7DQogICAgICAgICAgICAgICAgICAgIHVybDogYHlvbmdodS9pbmZvL2AraWQsDQogICAgICAgICAgICAgICAgICAgIG1ldGhvZDogImdldCINCiAgICAgICAgICAgICAgICB9KS50aGVuKCh7IGRhdGEgfSkgPT4gew0KICAgICAgICAgICAgICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmNvZGUgPT09IDApIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMueW9uZ2h1Rm9ybSA9IGRhdGEuZGF0YTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8vIOWkmue6p+iBlOWKqOWPguaVsA0KICAgICAgICAgICAgaW5mbyhpZCkgew0KICAgICAgICAgICAgICAgIHRoaXMuJGh0dHAoew0KICAgICAgICAgICAgICAgICAgICB1cmw6IGBjaGFuZ2RpQ29sbGVjdGlvbi9pbmZvLyR7aWR9YCwNCiAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiAnZ2V0Jw0KICAgICAgICAgICAgICAgIH0pLnRoZW4oKHsgZGF0YSB9KSA9PiB7DQogICAgICAgICAgICAgICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybSA9IGRhdGEuZGF0YTsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY2hhbmdkaUNoYW5nZShkYXRhLmRhdGEuY2hhbmdkaUlkKQ0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy55b25naHVDaGFuZ2UoZGF0YS5kYXRhLnlvbmdodUlkKQ0KICAgICAgICAgICAgICAgICAgICAgICAgLy/op6PlhrPliY3lj7DkuIrkvKDlm77niYflkI7lj7DkuI3mmL7npLrnmoTpl67popgNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCByZWc9bmV3IFJlZ0V4cCgnLi4vLi4vLi4vdXBsb2FkJywnZycpLy9n5Luj6KGo5YWo6YOoDQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGRhdGEubXNnKTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8vIOaPkOS6pA0KICAgICAgICAgICAgb25TdWJtaXQoKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kcmVmc1sicnVsZUZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgICAgICAgICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kaHR0cCh7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdXJsOmBjaGFuZ2RpQ29sbGVjdGlvbi8keyF0aGlzLnJ1bGVGb3JtLmlkID8gInNhdmUiIDogInVwZGF0ZSJ9YCwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXRob2Q6ICJwb3N0IiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiB0aGlzLnJ1bGVGb3JtDQogICAgICAgICAgICAgICAgICAgICAgICB9KS50aGVuKCh7IGRhdGEgfSkgPT4gew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmk43kvZzmiJDlip8iLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHVyYXRpb246IDE1MDAsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsb3NlOiAoKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5wYXJlbnQuc2hvd0ZsYWcgPSB0cnVlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucGFyZW50LmFkZE9yVXBkYXRlRmxhZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucGFyZW50LmNoYW5nZGlDb2xsZWN0aW9uQ3Jvc3NBZGRPclVwZGF0ZUZsYWcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnBhcmVudC5zZWFyY2goKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnBhcmVudC5jb250ZW50U3R5bGVDaGFuZ2UoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihkYXRhLm1zZyk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvLyDojrflj5Z1dWlkDQogICAgICAgICAgICBnZXRVVUlEICgpIHsNCiAgICAgICAgICAgICAgICByZXR1cm4gbmV3IERhdGUoKS5nZXRUaW1lKCk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy8g6L+U5ZueDQogICAgICAgICAgICBiYWNrKCkgew0KICAgICAgICAgICAgICAgIHRoaXMucGFyZW50LnNob3dGbGFnID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICB0aGlzLnBhcmVudC5hZGRPclVwZGF0ZUZsYWcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICB0aGlzLnBhcmVudC5jaGFuZ2RpQ29sbGVjdGlvbkNyb3NzQWRkT3JVcGRhdGVGbGFnID0gZmFsc2U7DQogICAgICAgICAgICAgICAgdGhpcy5wYXJlbnQuY29udGVudFN0eWxlQ2hhbmdlKCk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/lm77niYcNCg0KICAgICAgICAgICAgYWRkRWRpdFN0eWxlQ2hhbmdlKCkgew0KICAgICAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpPT57DQogICAgICAgICAgICAgICAgICAgIC8vIGlucHV0DQogICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy5hZGRFZGl0LWJsb2NrIC5pbnB1dCAuZWwtaW5wdXRfX2lubmVyJykuZm9yRWFjaChlbD0+ew0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuaGVpZ2h0ID0gdGhpcy5hZGRFZGl0Rm9ybS5pbnB1dEhlaWdodA0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuY29sb3IgPSB0aGlzLmFkZEVkaXRGb3JtLmlucHV0Rm9udENvbG9yDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5mb250U2l6ZSA9IHRoaXMuYWRkRWRpdEZvcm0uaW5wdXRGb250U2l6ZQ0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuYm9yZGVyV2lkdGggPSB0aGlzLmFkZEVkaXRGb3JtLmlucHV0Qm9yZGVyV2lkdGgNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlclN0eWxlID0gdGhpcy5hZGRFZGl0Rm9ybS5pbnB1dEJvcmRlclN0eWxlDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5ib3JkZXJDb2xvciA9IHRoaXMuYWRkRWRpdEZvcm0uaW5wdXRCb3JkZXJDb2xvcg0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuYm9yZGVyUmFkaXVzID0gdGhpcy5hZGRFZGl0Rm9ybS5pbnB1dEJvcmRlclJhZGl1cw0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuYmFja2dyb3VuZENvbG9yID0gdGhpcy5hZGRFZGl0Rm9ybS5pbnB1dEJnQ29sb3INCiAgICAgICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnLmFkZEVkaXQtYmxvY2sgLmlucHV0IC5lbC1mb3JtLWl0ZW1fX2xhYmVsJykuZm9yRWFjaChlbD0+ew0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUubGluZUhlaWdodCA9IHRoaXMuYWRkRWRpdEZvcm0uaW5wdXRIZWlnaHQNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmNvbG9yID0gdGhpcy5hZGRFZGl0Rm9ybS5pbnB1dExhYmxlQ29sb3INCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmZvbnRTaXplID0gdGhpcy5hZGRFZGl0Rm9ybS5pbnB1dExhYmxlRm9udFNpemUNCiAgICAgICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAgICAgLy8gc2VsZWN0DQogICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy5hZGRFZGl0LWJsb2NrIC5zZWxlY3QgLmVsLWlucHV0X19pbm5lcicpLmZvckVhY2goZWw9PnsNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmhlaWdodCA9IHRoaXMuYWRkRWRpdEZvcm0uc2VsZWN0SGVpZ2h0DQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5jb2xvciA9IHRoaXMuYWRkRWRpdEZvcm0uc2VsZWN0Rm9udENvbG9yDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5mb250U2l6ZSA9IHRoaXMuYWRkRWRpdEZvcm0uc2VsZWN0Rm9udFNpemUNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlcldpZHRoID0gdGhpcy5hZGRFZGl0Rm9ybS5zZWxlY3RCb3JkZXJXaWR0aA0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuYm9yZGVyU3R5bGUgPSB0aGlzLmFkZEVkaXRGb3JtLnNlbGVjdEJvcmRlclN0eWxlDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5ib3JkZXJDb2xvciA9IHRoaXMuYWRkRWRpdEZvcm0uc2VsZWN0Qm9yZGVyQ29sb3INCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlclJhZGl1cyA9IHRoaXMuYWRkRWRpdEZvcm0uc2VsZWN0Qm9yZGVyUmFkaXVzDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5iYWNrZ3JvdW5kQ29sb3IgPSB0aGlzLmFkZEVkaXRGb3JtLnNlbGVjdEJnQ29sb3INCiAgICAgICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnLmFkZEVkaXQtYmxvY2sgLnNlbGVjdCAuZWwtZm9ybS1pdGVtX19sYWJlbCcpLmZvckVhY2goZWw9PnsNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmxpbmVIZWlnaHQgPSB0aGlzLmFkZEVkaXRGb3JtLnNlbGVjdEhlaWdodA0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuY29sb3IgPSB0aGlzLmFkZEVkaXRGb3JtLnNlbGVjdExhYmxlQ29sb3INCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmZvbnRTaXplID0gdGhpcy5hZGRFZGl0Rm9ybS5zZWxlY3RMYWJsZUZvbnRTaXplDQogICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy5hZGRFZGl0LWJsb2NrIC5zZWxlY3QgLmVsLXNlbGVjdF9fY2FyZXQnKS5mb3JFYWNoKGVsPT57DQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5jb2xvciA9IHRoaXMuYWRkRWRpdEZvcm0uc2VsZWN0SWNvbkZvbnRDb2xvcg0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuZm9udFNpemUgPSB0aGlzLmFkZEVkaXRGb3JtLnNlbGVjdEljb25Gb250U2l6ZQ0KICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgICAvLyBkYXRlDQogICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy5hZGRFZGl0LWJsb2NrIC5kYXRlIC5lbC1pbnB1dF9faW5uZXInKS5mb3JFYWNoKGVsPT57DQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5oZWlnaHQgPSB0aGlzLmFkZEVkaXRGb3JtLmRhdGVIZWlnaHQNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmNvbG9yID0gdGhpcy5hZGRFZGl0Rm9ybS5kYXRlRm9udENvbG9yDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5mb250U2l6ZSA9IHRoaXMuYWRkRWRpdEZvcm0uZGF0ZUZvbnRTaXplDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5ib3JkZXJXaWR0aCA9IHRoaXMuYWRkRWRpdEZvcm0uZGF0ZUJvcmRlcldpZHRoDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5ib3JkZXJTdHlsZSA9IHRoaXMuYWRkRWRpdEZvcm0uZGF0ZUJvcmRlclN0eWxlDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5ib3JkZXJDb2xvciA9IHRoaXMuYWRkRWRpdEZvcm0uZGF0ZUJvcmRlckNvbG9yDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5ib3JkZXJSYWRpdXMgPSB0aGlzLmFkZEVkaXRGb3JtLmRhdGVCb3JkZXJSYWRpdXMNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJhY2tncm91bmRDb2xvciA9IHRoaXMuYWRkRWRpdEZvcm0uZGF0ZUJnQ29sb3INCiAgICAgICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnLmFkZEVkaXQtYmxvY2sgLmRhdGUgLmVsLWZvcm0taXRlbV9fbGFiZWwnKS5mb3JFYWNoKGVsPT57DQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5saW5lSGVpZ2h0ID0gdGhpcy5hZGRFZGl0Rm9ybS5kYXRlSGVpZ2h0DQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5jb2xvciA9IHRoaXMuYWRkRWRpdEZvcm0uZGF0ZUxhYmxlQ29sb3INCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmZvbnRTaXplID0gdGhpcy5hZGRFZGl0Rm9ybS5kYXRlTGFibGVGb250U2l6ZQ0KICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcuYWRkRWRpdC1ibG9jayAuZGF0ZSAuZWwtaW5wdXRfX2ljb24nKS5mb3JFYWNoKGVsPT57DQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5jb2xvciA9IHRoaXMuYWRkRWRpdEZvcm0uZGF0ZUljb25Gb250Q29sb3INCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmZvbnRTaXplID0gdGhpcy5hZGRFZGl0Rm9ybS5kYXRlSWNvbkZvbnRTaXplDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5saW5lSGVpZ2h0ID0gdGhpcy5hZGRFZGl0Rm9ybS5kYXRlSGVpZ2h0DQogICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgICAgIC8vIHVwbG9hZA0KICAgICAgICAgICAgICAgICAgICBsZXQgaWNvbkxpbmVIZWlnaHQgPSBwYXJzZUludCh0aGlzLmFkZEVkaXRGb3JtLnVwbG9hZEhlaWdodCkgLSBwYXJzZUludCh0aGlzLmFkZEVkaXRGb3JtLnVwbG9hZEJvcmRlcldpZHRoKSAqIDIgKyAncHgnDQogICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy5hZGRFZGl0LWJsb2NrIC51cGxvYWQgLmVsLXVwbG9hZC0tcGljdHVyZS1jYXJkJykuZm9yRWFjaChlbD0+ew0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUud2lkdGggPSB0aGlzLmFkZEVkaXRGb3JtLnVwbG9hZEhlaWdodA0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuaGVpZ2h0ID0gdGhpcy5hZGRFZGl0Rm9ybS51cGxvYWRIZWlnaHQNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlcldpZHRoID0gdGhpcy5hZGRFZGl0Rm9ybS51cGxvYWRCb3JkZXJXaWR0aA0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuYm9yZGVyU3R5bGUgPSB0aGlzLmFkZEVkaXRGb3JtLnVwbG9hZEJvcmRlclN0eWxlDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5ib3JkZXJDb2xvciA9IHRoaXMuYWRkRWRpdEZvcm0udXBsb2FkQm9yZGVyQ29sb3INCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlclJhZGl1cyA9IHRoaXMuYWRkRWRpdEZvcm0udXBsb2FkQm9yZGVyUmFkaXVzDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5iYWNrZ3JvdW5kQ29sb3IgPSB0aGlzLmFkZEVkaXRGb3JtLnVwbG9hZEJnQ29sb3INCiAgICAgICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnLmFkZEVkaXQtYmxvY2sgLnVwbG9hZCAuZWwtZm9ybS1pdGVtX19sYWJlbCcpLmZvckVhY2goZWw9PnsNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmxpbmVIZWlnaHQgPSB0aGlzLmFkZEVkaXRGb3JtLnVwbG9hZEhlaWdodA0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuY29sb3IgPSB0aGlzLmFkZEVkaXRGb3JtLnVwbG9hZExhYmxlQ29sb3INCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmZvbnRTaXplID0gdGhpcy5hZGRFZGl0Rm9ybS51cGxvYWRMYWJsZUZvbnRTaXplDQogICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy5hZGRFZGl0LWJsb2NrIC51cGxvYWQgLmVsLWljb24tcGx1cycpLmZvckVhY2goZWw9PnsNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmNvbG9yID0gdGhpcy5hZGRFZGl0Rm9ybS51cGxvYWRJY29uRm9udENvbG9yDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5mb250U2l6ZSA9IHRoaXMuYWRkRWRpdEZvcm0udXBsb2FkSWNvbkZvbnRTaXplDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5saW5lSGVpZ2h0ID0gaWNvbkxpbmVIZWlnaHQNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmRpc3BsYXkgPSAnYmxvY2snDQogICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgICAgIC8vIOWkmuaWh+acrOi+k+WFpeahhg0KICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcuYWRkRWRpdC1ibG9jayAudGV4dGFyZWEgLmVsLXRleHRhcmVhX19pbm5lcicpLmZvckVhY2goZWw9PnsNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmhlaWdodCA9IHRoaXMuYWRkRWRpdEZvcm0udGV4dGFyZWFIZWlnaHQNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmNvbG9yID0gdGhpcy5hZGRFZGl0Rm9ybS50ZXh0YXJlYUZvbnRDb2xvcg0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuZm9udFNpemUgPSB0aGlzLmFkZEVkaXRGb3JtLnRleHRhcmVhRm9udFNpemUNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlcldpZHRoID0gdGhpcy5hZGRFZGl0Rm9ybS50ZXh0YXJlYUJvcmRlcldpZHRoDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5ib3JkZXJTdHlsZSA9IHRoaXMuYWRkRWRpdEZvcm0udGV4dGFyZWFCb3JkZXJTdHlsZQ0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuYm9yZGVyQ29sb3IgPSB0aGlzLmFkZEVkaXRGb3JtLnRleHRhcmVhQm9yZGVyQ29sb3INCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlclJhZGl1cyA9IHRoaXMuYWRkRWRpdEZvcm0udGV4dGFyZWFCb3JkZXJSYWRpdXMNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJhY2tncm91bmRDb2xvciA9IHRoaXMuYWRkRWRpdEZvcm0udGV4dGFyZWFCZ0NvbG9yDQogICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy5hZGRFZGl0LWJsb2NrIC50ZXh0YXJlYSAuZWwtZm9ybS1pdGVtX19sYWJlbCcpLmZvckVhY2goZWw9PnsNCiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGVsLnN0eWxlLmxpbmVIZWlnaHQgPSB0aGlzLmFkZEVkaXRGb3JtLnRleHRhcmVhSGVpZ2h0DQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5jb2xvciA9IHRoaXMuYWRkRWRpdEZvcm0udGV4dGFyZWFMYWJsZUNvbG9yDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5mb250U2l6ZSA9IHRoaXMuYWRkRWRpdEZvcm0udGV4dGFyZWFMYWJsZUZvbnRTaXplDQogICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgICAgIC8vIOS/neWtmA0KICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcuYWRkRWRpdC1ibG9jayAuYnRuIC5idG4tc3VjY2VzcycpLmZvckVhY2goZWw9PnsNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLndpZHRoID0gdGhpcy5hZGRFZGl0Rm9ybS5idG5TYXZlV2lkdGgNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmhlaWdodCA9IHRoaXMuYWRkRWRpdEZvcm0uYnRuU2F2ZUhlaWdodA0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuY29sb3IgPSB0aGlzLmFkZEVkaXRGb3JtLmJ0blNhdmVGb250Q29sb3INCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmZvbnRTaXplID0gdGhpcy5hZGRFZGl0Rm9ybS5idG5TYXZlRm9udFNpemUNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlcldpZHRoID0gdGhpcy5hZGRFZGl0Rm9ybS5idG5TYXZlQm9yZGVyV2lkdGgNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlclN0eWxlID0gdGhpcy5hZGRFZGl0Rm9ybS5idG5TYXZlQm9yZGVyU3R5bGUNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlckNvbG9yID0gdGhpcy5hZGRFZGl0Rm9ybS5idG5TYXZlQm9yZGVyQ29sb3INCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlclJhZGl1cyA9IHRoaXMuYWRkRWRpdEZvcm0uYnRuU2F2ZUJvcmRlclJhZGl1cw0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuYmFja2dyb3VuZENvbG9yID0gdGhpcy5hZGRFZGl0Rm9ybS5idG5TYXZlQmdDb2xvcg0KICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgICAvLyDov5Tlm54NCiAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnLmFkZEVkaXQtYmxvY2sgLmJ0biAuYnRuLWNsb3NlJykuZm9yRWFjaChlbD0+ew0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUud2lkdGggPSB0aGlzLmFkZEVkaXRGb3JtLmJ0bkNhbmNlbFdpZHRoDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5oZWlnaHQgPSB0aGlzLmFkZEVkaXRGb3JtLmJ0bkNhbmNlbEhlaWdodA0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuY29sb3IgPSB0aGlzLmFkZEVkaXRGb3JtLmJ0bkNhbmNlbEZvbnRDb2xvcg0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuZm9udFNpemUgPSB0aGlzLmFkZEVkaXRGb3JtLmJ0bkNhbmNlbEZvbnRTaXplDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5ib3JkZXJXaWR0aCA9IHRoaXMuYWRkRWRpdEZvcm0uYnRuQ2FuY2VsQm9yZGVyV2lkdGgNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlclN0eWxlID0gdGhpcy5hZGRFZGl0Rm9ybS5idG5DYW5jZWxCb3JkZXJTdHlsZQ0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuYm9yZGVyQ29sb3IgPSB0aGlzLmFkZEVkaXRGb3JtLmJ0bkNhbmNlbEJvcmRlckNvbG9yDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5ib3JkZXJSYWRpdXMgPSB0aGlzLmFkZEVkaXRGb3JtLmJ0bkNhbmNlbEJvcmRlclJhZGl1cw0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuYmFja2dyb3VuZENvbG9yID0gdGhpcy5hZGRFZGl0Rm9ybS5idG5DYW5jZWxCZ0NvbG9yDQogICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBhZGRFZGl0VXBsb2FkU3R5bGVDaGFuZ2UoKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCk9PnsNCiAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnLmFkZEVkaXQtYmxvY2sgLnVwbG9hZCAuZWwtdXBsb2FkLWxpc3QtLXBpY3R1cmUtY2FyZCAuZWwtdXBsb2FkLWxpc3RfX2l0ZW0nKS5mb3JFYWNoKGVsPT57DQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS53aWR0aCA9IHRoaXMuYWRkRWRpdEZvcm0udXBsb2FkSGVpZ2h0DQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5oZWlnaHQgPSB0aGlzLmFkZEVkaXRGb3JtLnVwbG9hZEhlaWdodA0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuYm9yZGVyV2lkdGggPSB0aGlzLmFkZEVkaXRGb3JtLnVwbG9hZEJvcmRlcldpZHRoDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5ib3JkZXJTdHlsZSA9IHRoaXMuYWRkRWRpdEZvcm0udXBsb2FkQm9yZGVyU3R5bGUNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlckNvbG9yID0gdGhpcy5hZGRFZGl0Rm9ybS51cGxvYWRCb3JkZXJDb2xvcg0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuYm9yZGVyUmFkaXVzID0gdGhpcy5hZGRFZGl0Rm9ybS51cGxvYWRCb3JkZXJSYWRpdXMNCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJhY2tncm91bmRDb2xvciA9IHRoaXMuYWRkRWRpdEZvcm0udXBsb2FkQmdDb2xvcg0KICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9LA0KICAgICAgICB9DQogICAgfTsNCg=="}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AAmLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/changdiCollection", "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"addEdit-block\">\r\n        <el-form\r\n                class=\"detail-form-content\"\r\n                ref=\"ruleForm\"\r\n                :model=\"ruleForm\"\r\n                :rules=\"rules\"\r\n                label-width=\"80px\"\r\n                :style=\"{backgroundColor:addEditForm.addEditBoxColor}\">\r\n            <el-row>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='changdi'\">\r\n                    <el-form-item class=\"select\" v-if=\"type!='info'\"  label=\"场地\" prop=\"changdiId\">\r\n                        <el-select v-model=\"ruleForm.changdiId\" filterable placeholder=\"请选择场地\" @change=\"changdiChange\">\r\n                            <el-option\r\n                                    v-for=\"(item,index) in changdiOptions\"\r\n                                    v-bind:key=\"item.id\"\r\n                                    :label=\"item.changdiName\"\r\n                                    :value=\"item.id\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"场地编号\" prop=\"changdiUuidNumber\">\r\n                        <el-input v-model=\"changdiForm.changdiUuidNumber\"\r\n                                  placeholder=\"场地编号\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"场地编号\" prop=\"changdiUuidNumber\">\r\n                            <el-input v-model=\"ruleForm.changdiUuidNumber\"\r\n                                      placeholder=\"场地编号\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"场地名称\" prop=\"changdiName\">\r\n                        <el-input v-model=\"changdiForm.changdiName\"\r\n                                  placeholder=\"场地名称\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"场地名称\" prop=\"changdiName\">\r\n                            <el-input v-model=\"ruleForm.changdiName\"\r\n                                      placeholder=\"场地名称\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"24\" v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"upload\" v-if=\"type!='info' && !ro.changdiPhoto\" label=\"场地照片\" prop=\"changdiPhoto\">\r\n                        <img style=\"margin-right:20px;\" v-bind:key=\"index\" v-for=\"(item,index) in (changdiForm.changdiPhoto || '').split(',')\" :src=\"item\" width=\"100\" height=\"100\">\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item v-if=\"ruleForm.changdiPhoto\" label=\"场地照片\" prop=\"changdiPhoto\">\r\n                            <img style=\"margin-right:20px;\" v-bind:key=\"index\" v-for=\"(item,index) in (ruleForm.changdiPhoto || '').split(',')\" :src=\"item\" width=\"100\" height=\"100\">\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"场地类型\" prop=\"changdiValue\">\r\n                        <el-input v-model=\"changdiForm.changdiValue\"\r\n                                  placeholder=\"场地类型\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"场地类型\" prop=\"changdiValue\">\r\n                            <el-input v-model=\"ruleForm.changdiValue\"\r\n                                      placeholder=\"场地类型\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='yonghu'\">\r\n                    <el-form-item class=\"select\" v-if=\"type!='info'\"  label=\"用户\" prop=\"yonghuId\">\r\n                        <el-select v-model=\"ruleForm.yonghuId\" filterable placeholder=\"请选择用户\" @change=\"yonghuChange\">\r\n                            <el-option\r\n                                    v-for=\"(item,index) in yonghuOptions\"\r\n                                    v-bind:key=\"item.id\"\r\n                                    :label=\"item.yonghuName\"\r\n                                    :value=\"item.id\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='yonghu' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"用户姓名\" prop=\"yonghuName\">\r\n                        <el-input v-model=\"yonghuForm.yonghuName\"\r\n                                  placeholder=\"用户姓名\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"用户姓名\" prop=\"yonghuName\">\r\n                            <el-input v-model=\"ruleForm.yonghuName\"\r\n                                      placeholder=\"用户姓名\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='yonghu' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"用户手机号\" prop=\"yonghuPhone\">\r\n                        <el-input v-model=\"yonghuForm.yonghuPhone\"\r\n                                  placeholder=\"用户手机号\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"用户手机号\" prop=\"yonghuPhone\">\r\n                            <el-input v-model=\"ruleForm.yonghuPhone\"\r\n                                      placeholder=\"用户手机号\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='yonghu' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"用户身份证号\" prop=\"yonghuIdNumber\">\r\n                        <el-input v-model=\"yonghuForm.yonghuIdNumber\"\r\n                                  placeholder=\"用户身份证号\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"用户身份证号\" prop=\"yonghuIdNumber\">\r\n                            <el-input v-model=\"ruleForm.yonghuIdNumber\"\r\n                                      placeholder=\"用户身份证号\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"24\" v-if=\"sessionTable !='yonghu' \">\r\n                    <el-form-item class=\"upload\" v-if=\"type!='info' && !ro.yonghuPhoto\" label=\"用户头像\" prop=\"yonghuPhoto\">\r\n                        <img style=\"margin-right:20px;\" v-bind:key=\"index\" v-for=\"(item,index) in (yonghuForm.yonghuPhoto || '').split(',')\" :src=\"item\" width=\"100\" height=\"100\">\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item v-if=\"ruleForm.yonghuPhoto\" label=\"用户头像\" prop=\"yonghuPhoto\">\r\n                            <img style=\"margin-right:20px;\" v-bind:key=\"index\" v-for=\"(item,index) in (ruleForm.yonghuPhoto || '').split(',')\" :src=\"item\" width=\"100\" height=\"100\">\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='yonghu' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"电子邮箱\" prop=\"yonghuEmail\">\r\n                        <el-input v-model=\"yonghuForm.yonghuEmail\"\r\n                                  placeholder=\"电子邮箱\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"电子邮箱\" prop=\"yonghuEmail\">\r\n                            <el-input v-model=\"ruleForm.yonghuEmail\"\r\n                                      placeholder=\"电子邮箱\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <input id=\"updateId\" name=\"id\" type=\"hidden\">\r\n            <input id=\"changdiId\" name=\"changdiId\" type=\"hidden\">\r\n            <input id=\"yonghuId\" name=\"yonghuId\" type=\"hidden\">\r\n                <el-col :span=\"12\">\r\n                    <el-form-item class=\"select\" v-if=\"type!='info'\"  label=\"类型\" prop=\"changdiCollectionTypes\">\r\n                        <el-select v-model=\"ruleForm.changdiCollectionTypes\" placeholder=\"请选择类型\">\r\n                            <el-option\r\n                                v-for=\"(item,index) in changdiCollectionTypesOptions\"\r\n                                v-bind:key=\"item.codeIndex\"\r\n                                :label=\"item.indexName\"\r\n                                :value=\"item.codeIndex\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"类型\" prop=\"changdiCollectionValue\">\r\n                        <el-input v-model=\"ruleForm.changdiCollectionValue\"\r\n                            placeholder=\"类型\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n            </el-row>\r\n            <el-form-item class=\"btn\">\r\n                <el-button v-if=\"type!='info'\" type=\"primary\" class=\"btn-success\" @click=\"onSubmit\">提交</el-button>\r\n                <el-button v-if=\"type!='info'\" class=\"btn-close\" @click=\"back()\">取消</el-button>\r\n                <el-button v-if=\"type=='info'\" class=\"btn-close\" @click=\"back()\">返回</el-button>\r\n            </el-form-item>\r\n        </el-form>\r\n    </div>\r\n</template>\r\n<script>\r\n    import styleJs from \"../../../utils/style.js\";\r\n    // 数字，邮件，手机，url，身份证校验\r\n    import { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                addEditForm:null,\r\n                id: '',\r\n                type: '',\r\n                sessionTable : \"\",//登录账户所在表名\r\n                role : \"\",//权限\r\n                changdiForm: {},\r\n                yonghuForm: {},\r\n                ro:{\r\n                    changdiId: false,\r\n                    yonghuId: false,\r\n                    changdiCollectionTypes: false,\r\n                    insertTime: false,\r\n                },\r\n                ruleForm: {\r\n                    changdiId: '',\r\n                    yonghuId: '',\r\n                    changdiCollectionTypes: '',\r\n                    insertTime: '',\r\n                },\r\n                changdiCollectionTypesOptions : [],\r\n                changdiOptions : [],\r\n                yonghuOptions : [],\r\n                rules: {\r\n                   changdiId: [\r\n                              { required: true, message: '场地不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   yonghuId: [\r\n                              { required: true, message: '用户不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiCollectionTypes: [\r\n                              { required: true, message: '类型不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   insertTime: [\r\n                              { required: true, message: '收藏时间不能为空', trigger: 'blur' },\r\n                          ],\r\n                }\r\n            };\r\n        },\r\n        props: [\"parent\"],\r\n        computed: {\r\n        },\r\n        created() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n            if (this.role != \"管理员\"){\r\n            }\r\n            this.addEditForm = styleJs.addStyle();\r\n            this.addEditStyleChange()\r\n            this.addEditUploadStyleChange()\r\n            //获取下拉框信息\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=changdi_collection_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.changdiCollectionTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n\r\n         this.$http({\r\n             url: `changdi/page?page=1&limit=100`,\r\n             method: \"get\"\r\n         }).then(({ data }) => {\r\n             if (data && data.code === 0) {\r\n                this.changdiOptions = data.data.list;\r\n            }\r\n         });\r\n         this.$http({\r\n             url: `yonghu/page?page=1&limit=100`,\r\n             method: \"get\"\r\n         }).then(({ data }) => {\r\n             if (data && data.code === 0) {\r\n                this.yonghuOptions = data.data.list;\r\n            }\r\n         });\r\n\r\n        },\r\n        mounted() {\r\n        },\r\n        methods: {\r\n            // 下载\r\n            download(file){\r\n                window.open(`${file}`)\r\n            },\r\n            // 初始化\r\n            init(id,type) {\r\n                if (id) {\r\n                    this.id = id;\r\n                    this.type = type;\r\n                }\r\n                if(this.type=='info'||this.type=='else'){\r\n                    this.info(id);\r\n                }else if(this.type=='cross'){\r\n                    var obj = this.$storage.getObj('crossObj');\r\n                    for (var o in obj){\r\n\r\n                      if(o=='changdiId'){\r\n                          this.ruleForm.changdiId = obj[o];\r\n                          this.ro.changdiId = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='yonghuId'){\r\n                          this.ruleForm.yonghuId = obj[o];\r\n                          this.ro.yonghuId = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiCollectionTypes'){\r\n                          this.ruleForm.changdiCollectionTypes = obj[o];\r\n                          this.ro.changdiCollectionTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='insertTime'){\r\n                          this.ruleForm.insertTime = obj[o];\r\n                          this.ro.insertTime = true;\r\n                          continue;\r\n                      }\r\n                    }\r\n                }\r\n                // 获取用户信息\r\n                this.$http({\r\n                    url:`${this.$storage.get(\"sessionTable\")}/session`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        var json = data.data;\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            changdiChange(id){\r\n                this.$http({\r\n                    url: `changdi/info/`+id,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.changdiForm = data.data;\r\n                    }\r\n                });\r\n            },\r\n            yonghuChange(id){\r\n                this.$http({\r\n                    url: `yonghu/info/`+id,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.yonghuForm = data.data;\r\n                    }\r\n                });\r\n            },\r\n            // 多级联动参数\r\n            info(id) {\r\n                this.$http({\r\n                    url: `changdiCollection/info/${id}`,\r\n                    method: 'get'\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.ruleForm = data.data;\r\n                        this.changdiChange(data.data.changdiId)\r\n                        this.yonghuChange(data.data.yonghuId)\r\n                        //解决前台上传图片后台不显示的问题\r\n                        let reg=new RegExp('../../../upload','g')//g代表全部\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            // 提交\r\n            onSubmit() {\r\n                this.$refs[\"ruleForm\"].validate(valid => {\r\n                    if (valid) {\r\n                        this.$http({\r\n                            url:`changdiCollection/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n                            method: \"post\",\r\n                            data: this.ruleForm\r\n                        }).then(({ data }) => {\r\n                            if (data && data.code === 0) {\r\n                                this.$message({\r\n                                    message: \"操作成功\",\r\n                                    type: \"success\",\r\n                                    duration: 1500,\r\n                                    onClose: () => {\r\n                                        this.parent.showFlag = true;\r\n                                        this.parent.addOrUpdateFlag = false;\r\n                                        this.parent.changdiCollectionCrossAddOrUpdateFlag = false;\r\n                                        this.parent.search();\r\n                                        this.parent.contentStyleChange();\r\n                                    }\r\n                                });\r\n                            } else {\r\n                                this.$message.error(data.msg);\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            },\r\n            // 获取uuid\r\n            getUUID () {\r\n                return new Date().getTime();\r\n            },\r\n            // 返回\r\n            back() {\r\n                this.parent.showFlag = true;\r\n                this.parent.addOrUpdateFlag = false;\r\n                this.parent.changdiCollectionCrossAddOrUpdateFlag = false;\r\n                this.parent.contentStyleChange();\r\n            },\r\n            //图片\r\n\r\n            addEditStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    // input\r\n                    document.querySelectorAll('.addEdit-block .input .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputFontColor\r\n                        el.style.fontSize = this.addEditForm.inputFontSize\r\n                        el.style.borderWidth = this.addEditForm.inputBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.inputBorderStyle\r\n                        el.style.borderColor = this.addEditForm.inputBorderColor\r\n                        el.style.borderRadius = this.addEditForm.inputBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.inputBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .input .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputLableColor\r\n                        el.style.fontSize = this.addEditForm.inputLableFontSize\r\n                    })\r\n                    // select\r\n                    document.querySelectorAll('.addEdit-block .select .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectFontColor\r\n                        el.style.fontSize = this.addEditForm.selectFontSize\r\n                        el.style.borderWidth = this.addEditForm.selectBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.selectBorderStyle\r\n                        el.style.borderColor = this.addEditForm.selectBorderColor\r\n                        el.style.borderRadius = this.addEditForm.selectBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.selectBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectLableColor\r\n                        el.style.fontSize = this.addEditForm.selectLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-select__caret').forEach(el=>{\r\n                        el.style.color = this.addEditForm.selectIconFontColor\r\n                        el.style.fontSize = this.addEditForm.selectIconFontSize\r\n                    })\r\n                    // date\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateFontColor\r\n                        el.style.fontSize = this.addEditForm.dateFontSize\r\n                        el.style.borderWidth = this.addEditForm.dateBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.dateBorderStyle\r\n                        el.style.borderColor = this.addEditForm.dateBorderColor\r\n                        el.style.borderRadius = this.addEditForm.dateBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.dateBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateLableColor\r\n                        el.style.fontSize = this.addEditForm.dateLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__icon').forEach(el=>{\r\n                        el.style.color = this.addEditForm.dateIconFontColor\r\n                        el.style.fontSize = this.addEditForm.dateIconFontSize\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                    })\r\n                    // upload\r\n                    let iconLineHeight = parseInt(this.addEditForm.uploadHeight) - parseInt(this.addEditForm.uploadBorderWidth) * 2 + 'px'\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload--picture-card').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.uploadHeight\r\n                        el.style.color = this.addEditForm.uploadLableColor\r\n                        el.style.fontSize = this.addEditForm.uploadLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-icon-plus').forEach(el=>{\r\n                        el.style.color = this.addEditForm.uploadIconFontColor\r\n                        el.style.fontSize = this.addEditForm.uploadIconFontSize\r\n                        el.style.lineHeight = iconLineHeight\r\n                        el.style.display = 'block'\r\n                    })\r\n                    // 多文本输入框\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-textarea__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaFontColor\r\n                        el.style.fontSize = this.addEditForm.textareaFontSize\r\n                        el.style.borderWidth = this.addEditForm.textareaBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.textareaBorderStyle\r\n                        el.style.borderColor = this.addEditForm.textareaBorderColor\r\n                        el.style.borderRadius = this.addEditForm.textareaBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.textareaBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-form-item__label').forEach(el=>{\r\n                        // el.style.lineHeight = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaLableColor\r\n                        el.style.fontSize = this.addEditForm.textareaLableFontSize\r\n                    })\r\n                    // 保存\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-success').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnSaveWidth\r\n                        el.style.height = this.addEditForm.btnSaveHeight\r\n                        el.style.color = this.addEditForm.btnSaveFontColor\r\n                        el.style.fontSize = this.addEditForm.btnSaveFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnSaveBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnSaveBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnSaveBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnSaveBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnSaveBgColor\r\n                    })\r\n                    // 返回\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-close').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnCancelWidth\r\n                        el.style.height = this.addEditForm.btnCancelHeight\r\n                        el.style.color = this.addEditForm.btnCancelFontColor\r\n                        el.style.fontSize = this.addEditForm.btnCancelFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnCancelBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnCancelBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnCancelBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnCancelBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnCancelBgColor\r\n                    })\r\n                })\r\n            },\r\n            addEditUploadStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload-list--picture-card .el-upload-list__item').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                })\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\">\r\n.editor{\r\n  height: 500px;\r\n\r\n  & /deep/ .ql-container {\r\n\t  height: 310px;\r\n  }\r\n}\r\n.amap-wrapper {\r\n  width: 100%;\r\n  height: 500px;\r\n}\r\n.search-box {\r\n  position: absolute;\r\n}\r\n.addEdit-block {\r\n\tmargin: -10px;\r\n}\r\n.detail-form-content {\r\n\tpadding: 12px;\r\n}\r\n.btn .el-button {\r\n  padding: 0;\r\n}</style>\r\n\r\n"]}]}