{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\common\\BreadCrumbs.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\common\\BreadCrumbs.vue", "mtime": 1750583733629}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BreadCrumbs.vue"], "names": [], "mappings": ";AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "BreadCrumbs.vue", "sourceRoot": "src/components/common", "sourcesContent": ["<template>\r\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"(�?�?�?\" style=\"height:50px;backgroundColor:rgba(224, 240, 233, 1);borderRadius:0px;padding:0px 20px 0px 20px;boxShadow:4px 4px 2px#FFB3A7;borderWidth:0px;borderStyle:dotted solid double dashed;borderColor:rgba(255, 179, 167, 1);\">\r\n    <transition-group name=\"breadcrumb\" class=\"box\" :style=\"2==1?'justifyContent:flex-start;':2==2?'justifyContent:center;':'justifyContent:flex-end;'\">\r\n      <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\r\n        <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.name }}</span>\r\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.name }}</a>\r\n      </el-breadcrumb-item>\r\n    </transition-group>\r\n  </el-breadcrumb>\r\n</template>\r\n\r\n<script>\r\nimport pathToRegexp from 'path-to-regexp'\r\nimport { generateTitle } from '@/utils/i18n'\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.getBreadcrumb()\r\n    }\r\n  },\r\n  created() {\r\n    this.getBreadcrumb()\r\n    this.breadcrumbStyleChange()\r\n  },\r\n  methods: {\r\n    generateTitle,\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let route = this.$route\r\n      let matched = route.matched.filter(item => item.meta)\r\n      const first = matched[0]\r\n      matched = [{ path: '/index' }].concat(matched)\r\n\r\n      this.levelList = matched.filter(item => item.meta)\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name\r\n      if (!name) {\r\n        return false\r\n      }\r\n      return name.trim().toLocaleLowerCase() === 'Index'.toLocaleLowerCase()\r\n    },\r\n    pathCompile(path) {\r\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\r\n      const { params } = this.$route\r\n      var toPath = pathToRegexp.compile(path)\r\n      return toPath(params)\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item\r\n      if (redirect) {\r\n        this.$router.push(redirect)\r\n        return\r\n      }\r\n      this.$router.push(path)\r\n    },\r\n    breadcrumbStyleChange(val) {\r\n      this.$nextTick(()=>{\r\n        document.querySelectorAll('.app-breadcrumb .el-breadcrumb__separator').forEach(el=>{\r\n          el.innerText = \"(�?�?�?\"\r\n          el.style.color = \"rgba(43, 115, 176, 1)\"\r\n        })\r\n        document.querySelectorAll('.app-breadcrumb .el-breadcrumb__inner a').forEach(el=>{\r\n          el.style.color = \"rgba(77, 84, 222, 1)\"\r\n        })\r\n        document.querySelectorAll('.app-breadcrumb .el-breadcrumb__inner .no-redirect').forEach(el=>{\r\n          el.style.color = \"rgba(17, 18, 18, 1)\"\r\n        })\r\n\r\n        let str = \"vertical\"\r\n        if(\"vertical\" === str) {\r\n          let headHeight = \"60px\"\r\n          headHeight = parseInt(headHeight) + 10 + 'px'\r\n          document.querySelectorAll('.app-breadcrumb').forEach(el=>{\r\n            el.style.marginTop = headHeight\r\n          })\r\n        }\r\n\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-breadcrumb {\r\n  display: block;\r\n  font-size: 14px;\r\n  line-height: 50px;\r\n\r\n  .box {\r\n    display: flex;\r\n    width: 100%;\r\n    height: 100%;\r\n    justify-content: flex-start;\r\n    align-items: center;\r\n  }\r\n\r\n  .no-redirect {\r\n    color: #97a8be;\r\n    cursor: text;\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}