{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\index.vue", "mtime": 1642386765416}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IEluZGV4SGVhZGVyIGZyb20gJ0AvY29tcG9uZW50cy9pbmRleC9JbmRleEhlYWRlcic7CmltcG9ydCBJbmRleEFzaWRlIGZyb20gJ0AvY29tcG9uZW50cy9pbmRleC9JbmRleEFzaWRlU3RhdGljJzsKaW1wb3J0IEluZGV4TWFpbiBmcm9tICdAL2NvbXBvbmVudHMvaW5kZXgvSW5kZXhNYWluJzsKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIEluZGV4SGVhZGVyOiBJbmRleEhlYWRlciwKICAgIEluZGV4QXNpZGU6IEluZGV4QXNpZGUsCiAgICBJbmRleE1haW46IEluZGV4TWFpbgogIH0KfTs="}, {"version": 3, "names": ["IndexHeader", "IndexAside", "IndexMain", "components"], "sources": ["src/views/index.vue"], "sourcesContent": ["<template>\r\n\t<el-container>\r\n\t\t<index-header></index-header>\r\n\t\t<el-container>\r\n\t\t\t<index-aside></index-aside>\r\n\t\t\t<index-main></index-main>\r\n\t\t</el-container>\r\n\t</el-container>\r\n</template>\r\n<script>\r\n\timport IndexHeader from '@/components/index/IndexHeader'\r\n\timport IndexAside from '@/components/index/IndexAsideStatic'\r\n\timport IndexMain from '@/components/index/IndexMain'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tIndexHeader,\r\n\t\t\tIndexAside,\r\n\t\t\tIndexMain\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t// 铺满全屏\r\n\t.el-container {\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t}\r\n</style>\r\n"], "mappings": "AAUA,OAAAA,WAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA;EACAC,UAAA;IACAH,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAC,SAAA,EAAAA;EACA;AACA", "ignoreList": []}]}