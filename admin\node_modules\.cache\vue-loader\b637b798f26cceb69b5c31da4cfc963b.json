{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\SvgIcon\\index.vue?vue&type=style&index=0&id=c8a70580&scoped=true&lang=css", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\SvgIcon\\index.vue", "mtime": 1642386765189}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouc3ZnLWljb24gew0KICB3aWR0aDogMWVtOw0KICBoZWlnaHQ6IDFlbTsNCiAgdmVydGljYWwtYWxpZ246IC0wLjE1ZW07DQogIGZpbGw6IGN1cnJlbnRDb2xvcjsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAmCA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/SvgIcon", "sourcesContent": ["<template>\r\n  <svg :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\r\n    <use :xlink:href=\"iconName\" />\r\n  </svg>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SvgIcon',\r\n  props: {\r\n    iconClass: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    className: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  computed: {\r\n    iconName() {\r\n      return `#icon-${this.iconClass}`\r\n    },\r\n    svgClass() {\r\n      if (this.className) {\r\n        return 'svg-icon ' + this.className\r\n      } else {\r\n        return 'svg-icon'\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.svg-icon {\r\n  width: 1em;\r\n  height: 1em;\r\n  vertical-align: -0.15em;\r\n  fill: currentColor;\r\n  overflow: hidden;\r\n}\r\n</style>\r\n"]}]}