{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdi\\add-or-update.vue?vue&type=style&index=0&id=4c93c9e0&lang=scss", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdi\\add-or-update.vue", "mtime": 1750585607108}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZWRpdG9yew0KICBoZWlnaHQ6IDUwMHB4Ow0KDQogICYgOjp2LWRlZXAgLnFsLWNvbnRhaW5lciB7DQoJICBoZWlnaHQ6IDMxMHB4Ow0KICB9DQp9DQouYW1hcC13cmFwcGVyIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogNTAwcHg7DQp9DQouc2VhcmNoLWJveCB7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCn0NCi5hZGRFZGl0LWJsb2NrIHsNCgltYXJnaW46IC0xMHB4Ow0KfQ0KLmRldGFpbC1mb3JtLWNvbnRlbnQgew0KCXBhZGRpbmc6IDEycHg7DQp9DQouYnRuIC5lbC1idXR0b24gew0KICBwYWRkaW5nOiAwOw0KfQ=="}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AA2oBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/changdi", "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"addEdit-block\">\r\n        <el-form\r\n                class=\"detail-form-content\"\r\n                ref=\"ruleForm\"\r\n                :model=\"ruleForm\"\r\n                :rules=\"rules\"\r\n                label-width=\"80px\"\r\n                :style=\"{backgroundColor:addEditForm.addEditBoxColor}\">\r\n            <el-row>\r\n                <input id=\"updateId\" name=\"id\" type=\"hidden\">\r\n               <el-col :span=\"12\">\r\n                   <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"场地编号\" prop=\"changdiUuidNumber\">\r\n                       <el-input v-model=\"ruleForm.changdiUuidNumber\"\r\n                                 placeholder=\"场地编号\" clearable  :readonly=\"ro.changdiUuidNumber\"></el-input>\r\n                   </el-form-item>\r\n                   <div v-else>\r\n                       <el-form-item class=\"input\" label=\"场地编号\" prop=\"changdiUuidNumber\">\r\n                           <el-input v-model=\"ruleForm.changdiUuidNumber\"\r\n                                     placeholder=\"场地编号\" readonly></el-input>\r\n                       </el-form-item>\r\n                   </div>\r\n               </el-col>\r\n               <el-col :span=\"12\">\r\n                   <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"场地名称\" prop=\"changdiName\">\r\n                       <el-input v-model=\"ruleForm.changdiName\"\r\n                                 placeholder=\"场地名称\" clearable  :readonly=\"ro.changdiName\"></el-input>\r\n                   </el-form-item>\r\n                   <div v-else>\r\n                       <el-form-item class=\"input\" label=\"场地名称\" prop=\"changdiName\">\r\n                           <el-input v-model=\"ruleForm.changdiName\"\r\n                                     placeholder=\"场地名称\" readonly></el-input>\r\n                       </el-form-item>\r\n                   </div>\r\n               </el-col>\r\n                <el-col :span=\"24\">\r\n                    <el-form-item class=\"upload\" v-if=\"type!='info' && !ro.changdiPhoto\" label=\"场地照片\" prop=\"changdiPhoto\">\r\n                        <file-upload\r\n                            tip=\"点击上传场地照片\"\r\n                            action=\"file/upload\"\r\n                            :limit=\"3\"\r\n                            :multiple=\"true\"\r\n                            :fileUrls=\"ruleForm.changdiPhoto?ruleForm.changdiPhoto:''\"\r\n                            @change=\"changdiPhotoUploadChange\"\r\n                        ></file-upload>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item v-if=\"ruleForm.changdiPhoto\" label=\"场地照片\" prop=\"changdiPhoto\">\r\n                            <img style=\"margin-right:20px;\" v-bind:key=\"index\" v-for=\"(item,index) in (ruleForm.changdiPhoto || '').split(',')\" :src=\"item\" width=\"100\" height=\"100\">\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                    <el-form-item class=\"select\" v-if=\"type!='info'\"  label=\"场地类型\" prop=\"changdiTypes\">\r\n                        <el-select v-model=\"ruleForm.changdiTypes\" placeholder=\"请选择场地类型\">\r\n                            <el-option\r\n                                v-for=\"(item,index) in changdiTypesOptions\"\r\n                                v-bind:key=\"item.codeIndex\"\r\n                                :label=\"item.indexName\"\r\n                                :value=\"item.codeIndex\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"场地类型\" prop=\"changdiValue\">\r\n                        <el-input v-model=\"ruleForm.changdiValue\"\r\n                            placeholder=\"场地类型\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n               <el-col :span=\"12\">\r\n                   <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"场地原价\" prop=\"changdiOldMoney\">\r\n                       <el-input v-model=\"ruleForm.changdiOldMoney\"\r\n                                 placeholder=\"场地原价\" clearable  :readonly=\"ro.changdiOldMoney\"></el-input>\r\n                   </el-form-item>\r\n                   <div v-else>\r\n                       <el-form-item class=\"input\" label=\"场地原价\" prop=\"changdiOldMoney\">\r\n                           <el-input v-model=\"ruleForm.changdiOldMoney\"\r\n                                     placeholder=\"场地原价\" readonly></el-input>\r\n                       </el-form-item>\r\n                   </div>\r\n               </el-col>\r\n               <el-col :span=\"12\">\r\n                   <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"场地现价\" prop=\"changdiNewMoney\">\r\n                       <el-input v-model=\"ruleForm.changdiNewMoney\"\r\n                                 placeholder=\"场地现价\" clearable  :readonly=\"ro.changdiNewMoney\"></el-input>\r\n                   </el-form-item>\r\n                   <div v-else>\r\n                       <el-form-item class=\"input\" label=\"场地现价\" prop=\"changdiNewMoney\">\r\n                           <el-input v-model=\"ruleForm.changdiNewMoney\"\r\n                                     placeholder=\"场地现价\" readonly></el-input>\r\n                       </el-form-item>\r\n                   </div>\r\n               </el-col>\r\n               <el-col :span=\"12\">\r\n                   <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"时间段\" prop=\"shijianduan\">\r\n                       <el-input v-model=\"ruleForm.shijianduan\"\r\n                                 placeholder=\"时间段\" clearable  :readonly=\"ro.shijianduan\"></el-input>\r\n                   </el-form-item>\r\n                   <div v-else>\r\n                       <el-form-item class=\"input\" label=\"时间段\" prop=\"shijianduan\">\r\n                           <el-input v-model=\"ruleForm.shijianduan\"\r\n                                     placeholder=\"时间段\" readonly></el-input>\r\n                       </el-form-item>\r\n                   </div>\r\n               </el-col>\r\n               <!--<el-col :span=\"12\">\r\n                   <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"人数\" prop=\"shijianduanRen\">\r\n                       <el-input v-model=\"ruleForm.shijianduanRen\"\r\n                                 placeholder=\"人数\" clearable  :readonly=\"ro.shijianduanRen\"></el-input>\r\n                   </el-form-item>\r\n                   <div v-else>\r\n                       <el-form-item class=\"input\" label=\"人数\" prop=\"shijianduanRen\">\r\n                           <el-input v-model=\"ruleForm.shijianduanRen\"\r\n                                     placeholder=\"人数\" readonly></el-input>\r\n                       </el-form-item>\r\n                   </div>\r\n               </el-col>-->\r\n                <el-col :span=\"12\">\r\n                    <el-form-item class=\"select\" v-if=\"type!='info'\"  label=\"半全场\" prop=\"banquanTypes\">\r\n                        <el-select v-model=\"ruleForm.banquanTypes\" placeholder=\"请选择半全场\">\r\n                            <el-option\r\n                                v-for=\"(item,index) in banquanTypesOptions\"\r\n                                v-bind:key=\"item.codeIndex\"\r\n                                :label=\"item.indexName\"\r\n                                :value=\"item.codeIndex\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"半全场\" prop=\"banquanValue\">\r\n                        <el-input v-model=\"ruleForm.banquanValue\"\r\n                            placeholder=\"半全场\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n               <el-col :span=\"12\">\r\n                   <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"推荐吃饭地点\" prop=\"tuijian\">\r\n                       <el-input v-model=\"ruleForm.tuijian\"\r\n                                 placeholder=\"推荐吃饭地点\" clearable  :readonly=\"ro.tuijian\"></el-input>\r\n                   </el-form-item>\r\n                   <div v-else>\r\n                       <el-form-item class=\"input\" label=\"推荐吃饭地点\" prop=\"tuijian\">\r\n                           <el-input v-model=\"ruleForm.tuijian\"\r\n                                     placeholder=\"推荐吃饭地点\" readonly></el-input>\r\n                       </el-form-item>\r\n                   </div>\r\n               </el-col>\r\n                <el-col :span=\"24\">\r\n                    <el-form-item v-if=\"type!='info'\"  label=\"场地简介\" prop=\"changdiContent\">\r\n                        <editor style=\"min-width: 200px; max-width: 600px;\"\r\n                                v-model=\"ruleForm.changdiContent\"\r\n                                class=\"editor\"\r\n                                action=\"file/upload\">\r\n                        </editor>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item v-if=\"ruleForm.changdiContent\" label=\"场地简介\" prop=\"changdiContent\">\r\n                            <span v-html=\"ruleForm.changdiContent\"></span>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n            </el-row>\r\n            <el-form-item class=\"btn\">\r\n                <el-button v-if=\"type!='info'\" type=\"primary\" class=\"btn-success\" @click=\"onSubmit\">提交</el-button>\r\n                <el-button v-if=\"type!='info'\" class=\"btn-close\" @click=\"back()\">取消</el-button>\r\n                <el-button v-if=\"type=='info'\" class=\"btn-close\" @click=\"back()\">返回</el-button>\r\n            </el-form-item>\r\n        </el-form>\r\n    </div>\r\n</template>\r\n<script>\r\n    import styleJs from \"../../../utils/style.js\";\r\n    // 数字，邮件，手机，url，身份证校验\r\n    import { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                addEditForm:null,\r\n                id: '',\r\n                type: '',\r\n                sessionTable : \"\",//登录账户所在表名\r\n                role : \"\",//权限\r\n                ro:{\r\n                    changdiUuidNumber: false,\r\n                    changdiName: false,\r\n                    changdiPhoto: false,\r\n                    changdiTypes: false,\r\n                    changdiOldMoney: false,\r\n                    changdiNewMoney: false,\r\n                    shijianduan: false,\r\n                    shijianduanRen: false,\r\n                    changdiClicknum: false,\r\n                    banquanTypes: false,\r\n                    shangxiaTypes: false,\r\n                    tuijian: false,\r\n                    changdiDelete: false,\r\n                    changdiContent: false,\r\n                },\r\n                ruleForm: {\r\n                    changdiUuidNumber: new Date().getTime(),\r\n                    changdiName: '',\r\n                    changdiPhoto: '',\r\n                    changdiTypes: '',\r\n                    changdiOldMoney: '',\r\n                    changdiNewMoney: '',\r\n                    shijianduan: '8-10,10-12,14-16,16-18',\r\n                    shijianduanRen: '',\r\n                    changdiClicknum: '',\r\n                    banquanTypes: '',\r\n                    shangxiaTypes: '',\r\n                    tuijian: '',\r\n                    changdiDelete: '',\r\n                    changdiContent: '',\r\n                },\r\n                changdiTypesOptions : [],\r\n                banquanTypesOptions : [],\r\n                shangxiaTypesOptions : [],\r\n                rules: {\r\n                   changdiUuidNumber: [\r\n                              { required: true, message: '场地编号不能为空', trigger: 'blur' },\r\n                          ],\r\n                   changdiName: [\r\n                              { required: true, message: '场地名称不能为空', trigger: 'blur' },\r\n                          ],\r\n                   changdiPhoto: [\r\n                              { required: true, message: '场地照片不能为空', trigger: 'blur' },\r\n                          ],\r\n                   changdiTypes: [\r\n                              { required: true, message: '场地类型不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiOldMoney: [\r\n                              { required: true, message: '场地原价不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[0-9]{0,6}(\\.[0-9]{1,2})?$/,\r\n                                  message: '只允许输入整数6位,小数2位的数字',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiNewMoney: [\r\n                              { required: true, message: '场地现价不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[0-9]{0,6}(\\.[0-9]{1,2})?$/,\r\n                                  message: '只允许输入整数6位,小数2位的数字',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   shijianduan: [\r\n                              { required: true, message: '时间段不能为空', trigger: 'blur' },\r\n                          ],\r\n                   shijianduanRen: [\r\n                              { required: true, message: '人数不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiClicknum: [\r\n                              { required: true, message: '点击次数不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   banquanTypes: [\r\n                              { required: true, message: '半全场不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   shangxiaTypes: [\r\n                              { required: true, message: '是否上架不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   tuijian: [\r\n                              { required: true, message: '推荐吃饭地点不能为空', trigger: 'blur' },\r\n                          ],\r\n                   changdiDelete: [\r\n                              { required: true, message: '逻辑删除不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiContent: [\r\n                              { required: true, message: '场地简介不能为空', trigger: 'blur' },\r\n                          ],\r\n                }\r\n            };\r\n        },\r\n        props: [\"parent\"],\r\n        computed: {\r\n        },\r\n        created() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n            if (this.role != \"管理员\"){\r\n                this.ro.changdiOldMoney = true;\r\n                this.ro.changdiNewMoney = true;\r\n            }\r\n            this.addEditForm = styleJs.addStyle();\r\n            this.addEditStyleChange()\r\n            this.addEditUploadStyleChange()\r\n            //获取下拉框信息\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=changdi_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.changdiTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=banquan_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.banquanTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=shangxia_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.shangxiaTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n\r\n\r\n        },\r\n        mounted() {\r\n        },\r\n        methods: {\r\n            // 下载\r\n            download(file){\r\n                window.open(`${file}`)\r\n            },\r\n            // 初始化\r\n            init(id,type) {\r\n                if (id) {\r\n                    this.id = id;\r\n                    this.type = type;\r\n                }\r\n                if(this.type=='info'||this.type=='else'){\r\n                    this.info(id);\r\n                }else if(this.type=='cross'){\r\n                    var obj = this.$storage.getObj('crossObj');\r\n                    for (var o in obj){\r\n\r\n                      if(o=='changdiUuidNumber'){\r\n                          this.ruleForm.changdiUuidNumber = obj[o];\r\n                          this.ro.changdiUuidNumber = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiName'){\r\n                          this.ruleForm.changdiName = obj[o];\r\n                          this.ro.changdiName = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiPhoto'){\r\n                          this.ruleForm.changdiPhoto = obj[o];\r\n                          this.ro.changdiPhoto = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiTypes'){\r\n                          this.ruleForm.changdiTypes = obj[o];\r\n                          this.ro.changdiTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiOldMoney'){\r\n                          this.ruleForm.changdiOldMoney = obj[o];\r\n                          this.ro.changdiOldMoney = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiNewMoney'){\r\n                          this.ruleForm.changdiNewMoney = obj[o];\r\n                          this.ro.changdiNewMoney = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='shijianduan'){\r\n                          this.ruleForm.shijianduan = obj[o];\r\n                          this.ro.shijianduan = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='shijianduanRen'){\r\n                          this.ruleForm.shijianduanRen = obj[o];\r\n                          this.ro.shijianduanRen = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiClicknum'){\r\n                          this.ruleForm.changdiClicknum = obj[o];\r\n                          this.ro.changdiClicknum = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='banquanTypes'){\r\n                          this.ruleForm.banquanTypes = obj[o];\r\n                          this.ro.banquanTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='shangxiaTypes'){\r\n                          this.ruleForm.shangxiaTypes = obj[o];\r\n                          this.ro.shangxiaTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='tuijian'){\r\n                          this.ruleForm.tuijian = obj[o];\r\n                          this.ro.tuijian = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiDelete'){\r\n                          this.ruleForm.changdiDelete = obj[o];\r\n                          this.ro.changdiDelete = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiContent'){\r\n                          this.ruleForm.changdiContent = obj[o];\r\n                          this.ro.changdiContent = true;\r\n                          continue;\r\n                      }\r\n                    }\r\n                }\r\n                // 获取用户信息\r\n                this.$http({\r\n                    url:`${this.$storage.get(\"sessionTable\")}/session`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        var json = data.data;\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            // 多级联动参数\r\n            info(id) {\r\n                this.$http({\r\n                    url: `changdi/info/${id}`,\r\n                    method: 'get'\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.ruleForm = data.data;\r\n                        //解决前台上传图片后台不显示的问题\r\n                        let reg=new RegExp('../../../upload','g')//g代表全部\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            // 提交\r\n            onSubmit() {\r\n                this.$refs[\"ruleForm\"].validate(valid => {\r\n                    if (valid) {\r\n                        this.$http({\r\n                            url:`changdi/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n                            method: \"post\",\r\n                            data: this.ruleForm\r\n                        }).then(({ data }) => {\r\n                            if (data && data.code === 0) {\r\n                                this.$message({\r\n                                    message: \"操作成功\",\r\n                                    type: \"success\",\r\n                                    duration: 1500,\r\n                                    onClose: () => {\r\n                                        this.parent.showFlag = true;\r\n                                        this.parent.addOrUpdateFlag = false;\r\n                                        this.parent.changdiCrossAddOrUpdateFlag = false;\r\n                                        this.parent.search();\r\n                                        this.parent.contentStyleChange();\r\n                                    }\r\n                                });\r\n                            } else {\r\n                                this.$message.error(data.msg);\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            },\r\n            // 获取uuid\r\n            getUUID () {\r\n                return new Date().getTime();\r\n            },\r\n            // 返回\r\n            back() {\r\n                this.parent.showFlag = true;\r\n                this.parent.addOrUpdateFlag = false;\r\n                this.parent.changdiCrossAddOrUpdateFlag = false;\r\n                this.parent.contentStyleChange();\r\n            },\r\n            //图片\r\n            changdiPhotoUploadChange(fileUrls){\r\n                this.ruleForm.changdiPhoto = fileUrls;\r\n                this.addEditUploadStyleChange()\r\n            },\r\n\r\n            addEditStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    // input\r\n                    document.querySelectorAll('.addEdit-block .input .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputFontColor\r\n                        el.style.fontSize = this.addEditForm.inputFontSize\r\n                        el.style.borderWidth = this.addEditForm.inputBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.inputBorderStyle\r\n                        el.style.borderColor = this.addEditForm.inputBorderColor\r\n                        el.style.borderRadius = this.addEditForm.inputBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.inputBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .input .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputLableColor\r\n                        el.style.fontSize = this.addEditForm.inputLableFontSize\r\n                    })\r\n                    // select\r\n                    document.querySelectorAll('.addEdit-block .select .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectFontColor\r\n                        el.style.fontSize = this.addEditForm.selectFontSize\r\n                        el.style.borderWidth = this.addEditForm.selectBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.selectBorderStyle\r\n                        el.style.borderColor = this.addEditForm.selectBorderColor\r\n                        el.style.borderRadius = this.addEditForm.selectBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.selectBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectLableColor\r\n                        el.style.fontSize = this.addEditForm.selectLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-select__caret').forEach(el=>{\r\n                        el.style.color = this.addEditForm.selectIconFontColor\r\n                        el.style.fontSize = this.addEditForm.selectIconFontSize\r\n                    })\r\n                    // date\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateFontColor\r\n                        el.style.fontSize = this.addEditForm.dateFontSize\r\n                        el.style.borderWidth = this.addEditForm.dateBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.dateBorderStyle\r\n                        el.style.borderColor = this.addEditForm.dateBorderColor\r\n                        el.style.borderRadius = this.addEditForm.dateBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.dateBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateLableColor\r\n                        el.style.fontSize = this.addEditForm.dateLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__icon').forEach(el=>{\r\n                        el.style.color = this.addEditForm.dateIconFontColor\r\n                        el.style.fontSize = this.addEditForm.dateIconFontSize\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                    })\r\n                    // upload\r\n                    let iconLineHeight = parseInt(this.addEditForm.uploadHeight) - parseInt(this.addEditForm.uploadBorderWidth) * 2 + 'px'\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload--picture-card').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.uploadHeight\r\n                        el.style.color = this.addEditForm.uploadLableColor\r\n                        el.style.fontSize = this.addEditForm.uploadLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-icon-plus').forEach(el=>{\r\n                        el.style.color = this.addEditForm.uploadIconFontColor\r\n                        el.style.fontSize = this.addEditForm.uploadIconFontSize\r\n                        el.style.lineHeight = iconLineHeight\r\n                        el.style.display = 'block'\r\n                    })\r\n                    // 多文本输入框\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-textarea__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaFontColor\r\n                        el.style.fontSize = this.addEditForm.textareaFontSize\r\n                        el.style.borderWidth = this.addEditForm.textareaBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.textareaBorderStyle\r\n                        el.style.borderColor = this.addEditForm.textareaBorderColor\r\n                        el.style.borderRadius = this.addEditForm.textareaBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.textareaBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-form-item__label').forEach(el=>{\r\n                        // el.style.lineHeight = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaLableColor\r\n                        el.style.fontSize = this.addEditForm.textareaLableFontSize\r\n                    })\r\n                    // 保存\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-success').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnSaveWidth\r\n                        el.style.height = this.addEditForm.btnSaveHeight\r\n                        el.style.color = this.addEditForm.btnSaveFontColor\r\n                        el.style.fontSize = this.addEditForm.btnSaveFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnSaveBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnSaveBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnSaveBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnSaveBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnSaveBgColor\r\n                    })\r\n                    // 返回\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-close').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnCancelWidth\r\n                        el.style.height = this.addEditForm.btnCancelHeight\r\n                        el.style.color = this.addEditForm.btnCancelFontColor\r\n                        el.style.fontSize = this.addEditForm.btnCancelFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnCancelBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnCancelBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnCancelBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnCancelBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnCancelBgColor\r\n                    })\r\n                })\r\n            },\r\n            addEditUploadStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload-list--picture-card .el-upload-list__item').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                })\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\">\r\n.editor{\r\n  height: 500px;\r\n\r\n  & ::v-deep .ql-container {\r\n\t  height: 310px;\r\n  }\r\n}\r\n.amap-wrapper {\r\n  width: 100%;\r\n  height: 500px;\r\n}\r\n.search-box {\r\n  position: absolute;\r\n}\r\n.addEdit-block {\r\n\tmargin: -10px;\r\n}\r\n.detail-form-content {\r\n\tpadding: 12px;\r\n}\r\n.btn .el-button {\r\n  padding: 0;\r\n}</style>\r\n\r\n"]}]}