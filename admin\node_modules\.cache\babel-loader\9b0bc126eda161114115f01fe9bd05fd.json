{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\src\\icons\\index.js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\icons\\index.js", "mtime": 1642386765196}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiOwppbXBvcnQgVnVlIGZyb20gJ3Z1ZSc7CmltcG9ydCBTdmdJY29uIGZyb20gJ0AvY29tcG9uZW50cy9TdmdJY29uJzsgLy8gc3ZnIGNvbXBvbmVudAoKLy8gcmVnaXN0ZXIgZ2xvYmFsbHkKVnVlLmNvbXBvbmVudCgnc3ZnLWljb24nLCBTdmdJY29uKTsKdmFyIHJlcSA9IHJlcXVpcmUuY29udGV4dCgnLi9zdmcvc3ZnJywgZmFsc2UsIC9cLnN2ZyQvKTsKdmFyIHJlcXVpcmVBbGwgPSBmdW5jdGlvbiByZXF1aXJlQWxsKHJlcXVpcmVDb250ZXh0KSB7CiAgcmV0dXJuIHJlcXVpcmVDb250ZXh0LmtleXMoKS5tYXAocmVxdWlyZUNvbnRleHQpOwp9OwpyZXF1aXJlQWxsKHJlcSk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "SvgIcon", "component", "req", "require", "context", "requireAll", "requireContext", "keys", "map"], "sources": ["D:/1/tiyuguan/admin/src/icons/index.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport SvgIcon from '@/components/SvgIcon'// svg component\r\n\r\n// register globally\r\nVue.component('svg-icon', SvgIcon)\r\n\r\nconst req = require.context('./svg/svg', false, /\\.svg$/)\r\nconst requireAll = requireContext => requireContext.keys().map(requireContext)\r\nrequireAll(req)\r\n"], "mappings": ";;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,OAAO,MAAM,sBAAsB;;AAE1C;AACAD,GAAG,CAACE,SAAS,CAAC,UAAU,EAAED,OAAO,CAAC;AAElC,IAAME,GAAG,GAAGC,OAAO,CAACC,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,QAAQ,CAAC;AACzD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAGC,cAAc;EAAA,OAAIA,cAAc,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAACF,cAAc,CAAC;AAAA;AAC9ED,UAAU,CAACH,GAAG,CAAC", "ignoreList": []}]}