{"remainingRequest": "D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\src\\utils\\utils.js", "dependencies": [{"path": "D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\src\\utils\\utils.js", "mtime": 1642386765411}, {"path": "D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["storage", "menu", "isAuth", "tableName", "key", "role", "get", "menus", "list", "i", "length", "<PERSON><PERSON><PERSON>", "j", "backMenu", "k", "child", "buttons", "join", "indexOf", "getCurDateTime", "currentTime", "Date", "year", "getFullYear", "month", "getMonth", "day", "getDate", "hour", "getHours", "minute", "getMinutes", "second", "getSeconds", "getCurDate"], "sources": ["D:/1/springboot和vue体育馆预约系统黄粉/admin/src/utils/utils.js"], "sourcesContent": ["import storage from './storage';\r\nimport menu from './menu';\r\n/**\r\n * 是否有权限\r\n * @param {*} key\r\n */\r\nexport function isAuth(tableName,key) {\r\n    let role = storage.get(\"role\");\r\n    if(!role){\r\n        role = '管理员';\r\n    }\r\n    let menus = menu.list();\r\n    for(let i=0;i<menus.length;i++){\r\n        if(menus[i].roleName==role){\r\n            for(let j=0;j<menus[i].backMenu.length;j++){\r\n                for(let k=0;k<menus[i].backMenu[j].child.length;k++){\r\n                    if(tableName==menus[i].backMenu[j].child[k].tableName){\r\n                        let buttons = menus[i].backMenu[j].child[k].buttons.join(',');\r\n                        return buttons.indexOf(key) !== -1 || false\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    // for(let i=0;i<menus.length;i++){\r\n    //     if(menus[i].roleName==role){\r\n    //         for(let j=0;j<menus[i].backMenu.length;j++){\r\n    //             if(menus[i].backMenu[j].tableName==tableName){\r\n    //                 let buttons = menus[i].backMenu[j].child[0].buttons.join(',');\r\n    //                 return buttons.indexOf(key) !== -1 || false\r\n    //             }\r\n    //         }\r\n    //     }\r\n    // }\r\n    return false;\r\n}\r\n\r\n/**\r\n *  * 获取当前时间（yyyy-MM-dd hh:mm:ss）\r\n *   */\r\nexport function getCurDateTime() {\r\n    let currentTime = new Date(),\r\n    year = currentTime.getFullYear(),\r\n    month = currentTime.getMonth() + 1 < 10 ? '0' + (currentTime.getMonth() + 1) : currentTime.getMonth() + 1,\r\n    day = currentTime.getDate() < 10 ? '0' + currentTime.getDate() : currentTime.getDate(),\r\n    hour = currentTime.getHours(),\r\n    minute = currentTime.getMinutes(),\r\n    second = currentTime.getSeconds();\r\n    return year + \"-\" + month + \"-\" + day + \" \" +hour +\":\" +minute+\":\"+second;\r\n}\r\n\r\n/**\r\n *  * 获取当前日期（yyyy-MM-dd）\r\n *   */\r\nexport function getCurDate() {\r\n    let currentTime = new Date(),\r\n    year = currentTime.getFullYear(),\r\n    month = currentTime.getMonth() + 1 < 10 ? '0' + (currentTime.getMonth() + 1) : currentTime.getMonth() + 1,\r\n    day = currentTime.getDate() < 10 ? '0' + currentTime.getDate() : currentTime.getDate();\r\n    return year + \"-\" + month + \"-\" + day;\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,OAAO,MAAM,WAAW;AAC/B,OAAOC,IAAI,MAAM,QAAQ;AACzB;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,SAAS,EAACC,GAAG,EAAE;EAClC,IAAIC,IAAI,GAAGL,OAAO,CAACM,GAAG,CAAC,MAAM,CAAC;EAC9B,IAAG,CAACD,IAAI,EAAC;IACLA,IAAI,GAAG,KAAK;EAChB;EACA,IAAIE,KAAK,GAAGN,IAAI,CAACO,IAAI,CAAC,CAAC;EACvB,KAAI,IAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACF,KAAK,CAACG,MAAM,EAACD,CAAC,EAAE,EAAC;IAC3B,IAAGF,KAAK,CAACE,CAAC,CAAC,CAACE,QAAQ,IAAEN,IAAI,EAAC;MACvB,KAAI,IAAIO,CAAC,GAAC,CAAC,EAACA,CAAC,GAACL,KAAK,CAACE,CAAC,CAAC,CAACI,QAAQ,CAACH,MAAM,EAACE,CAAC,EAAE,EAAC;QACvC,KAAI,IAAIE,CAAC,GAAC,CAAC,EAACA,CAAC,GAACP,KAAK,CAACE,CAAC,CAAC,CAACI,QAAQ,CAACD,CAAC,CAAC,CAACG,KAAK,CAACL,MAAM,EAACI,CAAC,EAAE,EAAC;UAChD,IAAGX,SAAS,IAAEI,KAAK,CAACE,CAAC,CAAC,CAACI,QAAQ,CAACD,CAAC,CAAC,CAACG,KAAK,CAACD,CAAC,CAAC,CAACX,SAAS,EAAC;YAClD,IAAIa,OAAO,GAAGT,KAAK,CAACE,CAAC,CAAC,CAACI,QAAQ,CAACD,CAAC,CAAC,CAACG,KAAK,CAACD,CAAC,CAAC,CAACE,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;YAC7D,OAAOD,OAAO,CAACE,OAAO,CAACd,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK;UAC/C;QACJ;MACJ;IACJ;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAO,KAAK;AAChB;;AAEA;AACA;AACA;AACA,OAAO,SAASe,cAAcA,CAAA,EAAG;EAC7B,IAAIC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC;IAC5BC,IAAI,GAAGF,WAAW,CAACG,WAAW,CAAC,CAAC;IAChCC,KAAK,GAAGJ,WAAW,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,IAAIL,WAAW,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGL,WAAW,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC;IACzGC,GAAG,GAAGN,WAAW,CAACO,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGP,WAAW,CAACO,OAAO,CAAC,CAAC,GAAGP,WAAW,CAACO,OAAO,CAAC,CAAC;IACtFC,IAAI,GAAGR,WAAW,CAACS,QAAQ,CAAC,CAAC;IAC7BC,MAAM,GAAGV,WAAW,CAACW,UAAU,CAAC,CAAC;IACjCC,MAAM,GAAGZ,WAAW,CAACa,UAAU,CAAC,CAAC;EACjC,OAAOX,IAAI,GAAG,GAAG,GAAGE,KAAK,GAAG,GAAG,GAAGE,GAAG,GAAG,GAAG,GAAEE,IAAI,GAAE,GAAG,GAAEE,MAAM,GAAC,GAAG,GAACE,MAAM;AAC7E;;AAEA;AACA;AACA;AACA,OAAO,SAASE,UAAUA,CAAA,EAAG;EACzB,IAAId,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC;IAC5BC,IAAI,GAAGF,WAAW,CAACG,WAAW,CAAC,CAAC;IAChCC,KAAK,GAAGJ,WAAW,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,IAAIL,WAAW,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGL,WAAW,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC;IACzGC,GAAG,GAAGN,WAAW,CAACO,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGP,WAAW,CAACO,OAAO,CAAC,CAAC,GAAGP,WAAW,CAACO,OAAO,CAAC,CAAC;EACtF,OAAOL,IAAI,GAAG,GAAG,GAAGE,KAAK,GAAG,GAAG,GAAGE,GAAG;AACzC", "ignoreList": []}]}