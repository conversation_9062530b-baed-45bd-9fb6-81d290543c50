{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\dictionary\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\dictionary\\list.vue", "mtime": 1642386766982}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";AAm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file": "list.vue", "sourceRoot": "src/views/modules/dictionary", "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"main-content\">\r\n\r\n        <!-- 条件查询 -->\r\n        <div v-if=\"showFlag\">\r\n            <el-form :inline=\"true\" :model=\"searchForm\" class=\"form-content\">\r\n                <el-row :gutter=\"20\" class=\"slt\" :style=\"{justifyContent:contents.searchBoxPosition=='1'?'flex-start':contents.searchBoxPosition=='2'?'center':'flex-end'}\">\r\n                                         \r\n                     <el-form-item :label=\"contents.inputTitle == 1 ? '编码名字' : ''\">\r\n                         <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.indexName\" placeholder=\"编码名字\" clearable></el-input>\r\n                     </el-form-item>\r\n                        \r\n\r\n\r\n                    <el-form-item>\r\n                        <el-button type=\"success\" @click=\"search()\">查询<i class=\"el-icon-search el-icon--right\"/></el-button>\r\n                    </el-form-item>\r\n                </el-row>\r\n                <el-row class=\"ad\" :style=\"{justifyContent:contents.btnAdAllBoxPosition=='1'?'flex-start':contents.btnAdAllBoxPosition=='2'?'center':'flex-end'}\">\r\n                    <el-form-item>\r\n                        <el-button\r\n                                v-if=\"isAuth('dictionary','新增')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-plus\"\r\n                                @click=\"addOrUpdateHandler()\"\r\n                        >新增</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('dictionary','删除')\"\r\n                                :disabled=\"dataListSelections.length <= 0\"\r\n                                type=\"danger\"\r\n                                icon=\"el-icon-delete\"\r\n                                @click=\"deleteHandler()\"\r\n                        >删除</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('dictionary','报表')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-pie-chart\"\r\n                                @click=\"chartDialog()\"\r\n                        >报表</el-button>\r\n                        &nbsp;\r\n                        <a style=\"text-decoration:none\" class=\"el-button el-button--success\"\r\n                           v-if=\"isAuth('dictionary','导入导出')\"\r\n                           icon=\"el-icon-download\"\r\n                           href=\"http://localhost:8080/tiyuguan/upload/dictionaryMuBan.xls\"\r\n                        >批量导入字典数据模板</a>\r\n                        &nbsp;\r\n                        <el-upload\r\n                                v-if=\"isAuth('dictionary','导入导出')\"\r\n                                style=\"display: inline-block\"\r\n                                action=\"tiyuguan/file/upload\"\r\n                                :on-success=\"dictionaryUploadSuccess\"\r\n                                :on-error=\"dictionaryUploadError\"\r\n                                :show-file-list = false>\r\n                            <el-button\r\n                                    v-if=\"isAuth('dictionary','导入导出')\"\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-upload2\"\r\n                            >批量导入字典数据</el-button>\r\n                        </el-upload>\r\n                        &nbsp;\r\n                        <!-- 导出excel -->\r\n                        <download-excel v-if=\"isAuth('dictionary','导入导出')\" style=\"display: inline-block\" class = \"export-excel-wrapper\" :data = \"dataList\" :fields = \"json_fields\" name = \"dictionary.xls\">\r\n                            <!-- 导出excel -->\r\n                            <el-button\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-download\"\r\n                            >导出</el-button>\r\n                        </download-excel>\r\n                        &nbsp;\r\n                    </el-form-item>\r\n                </el-row>\r\n            </el-form>\r\n            <div class=\"table-content\">\r\n                <el-table class=\"tables\" :size=\"contents.tableSize\" :show-header=\"contents.tableShowHeader\"\r\n                          :header-row-style=\"headerRowStyle\" :header-cell-style=\"headerCellStyle\"\r\n                          :border=\"contents.tableBorder\"\r\n                          :fit=\"contents.tableFit\"\r\n                          :stripe=\"contents.tableStripe\"\r\n                          :row-style=\"rowStyle\"\r\n                          :cell-style=\"cellStyle\"\r\n                          :style=\"{width: '100%',fontSize:contents.tableContentFontSize,color:contents.tableContentFontColor}\"\r\n                          v-if=\"isAuth('dictionary','查看')\"\r\n                          :data=\"dataList\"\r\n                          v-loading=\"dataListLoading\"\r\n                          @selection-change=\"selectionChangeHandler\">\r\n                    <el-table-column  v-if=\"contents.tableSelection\"\r\n                                      type=\"selection\"\r\n                                      header-align=\"center\"\r\n                                      align=\"center\"\r\n                                      width=\"50\">\r\n                    </el-table-column>\r\n                    <el-table-column label=\"索引\" v-if=\"contents.tableIndex\" type=\"index\" width=\"50\" />\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"dicCode\"\r\n                                   header-align=\"center\"\r\n                                   label=\"字段\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.dicCode}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"dicName\"\r\n                                   header-align=\"center\"\r\n                                   label=\"字段名\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.dicName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"codeIndex\"\r\n                                      header-align=\"center\"\r\n                                      label=\"编码\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.codeIndex}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"indexName\"\r\n                                   header-align=\"center\"\r\n                                   label=\"编码名字\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.indexName}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"beizhu\"\r\n                                   header-align=\"center\"\r\n                                   label=\"备注\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.beizhu}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column width=\"300\" :align=\"contents.tableAlign\"\r\n                                     header-align=\"center\"\r\n                                     label=\"操作\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button v-if=\"isAuth('dictionary','查看')\" type=\"success\" icon=\"el-icon-tickets\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id,'info')\">详情</el-button>\r\n                            <el-button v-if=\"isAuth('dictionary','修改')\" type=\"primary\" icon=\"el-icon-edit\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id)\">修改</el-button>\r\n                            <el-button v-if=\"isAuth('dictionary','删除')\" type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteHandler(scope.row.id)\">删除</el-button>\r\n\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n                <el-pagination\r\n                        clsss=\"pages\"\r\n                        :layout=\"layouts\"\r\n                        @size-change=\"sizeChangeHandle\"\r\n                        @current-change=\"currentChangeHandle\"\r\n                        :current-page=\"pageIndex\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"Number(contents.pageEachNum)\"\r\n                        :total=\"totalPage\"\r\n                        :small=\"contents.pageStyle\"\r\n                        class=\"pagination-content\"\r\n                        :background=\"contents.pageBtnBG\"\r\n                        :style=\"{textAlign:contents.pagePosition==1?'left':contents.pagePosition==2?'center':'right'}\"\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <!-- 添加/修改页面  将父组件的search方法传递给子组件-->\r\n        <add-or-update v-if=\"addOrUpdateFlag\" :parent=\"this\" ref=\"addOrUpdate\"></add-or-update>\r\n\r\n\r\n\r\n        <el-dialog title=\"统计报表\" :visible.sync=\"chartVisiable\" width=\"800\">\r\n            <el-date-picker\r\n                    v-model=\"echartsDate\"\r\n                    type=\"year\"\r\n                    placeholder=\"选择年\">\r\n            </el-date-picker>\r\n            <el-button @click=\"chartDialog()\">查询</el-button>\r\n            <div id=\"statistic\" style=\"width:100%;height:600px;\"></div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"chartVisiable = false\">返回</el-button>\r\n\t\t\t</span>\r\n        </el-dialog>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import AddOrUpdate from \"./add-or-update\";\r\n    import styleJs from \"../../../utils/style.js\";\r\n    export default {\r\n        data() {\r\n        return {\r\n            searchForm: {\r\n                key: \"\"\r\n            },\r\n            sessionTable : \"\",//登录账户所在表名\r\n            role : \"\",//权限\r\n    //级联表下拉框搜索条件\r\n    //当前表下拉框搜索条件\r\n            form:{\r\n                id : null,\r\n                dicCode : null,\r\n                dicName : null,\r\n                codeIndex : null,\r\n                indexName : null,\r\n                superId : null,\r\n                beizhu : null,\r\n                createTime : null,\r\n            },\r\n            dataList: [],\r\n            pageIndex: 1,\r\n            pageSize: 10,\r\n            totalPage: 0,\r\n            dataListLoading: false,\r\n            dataListSelections: [],\r\n            showFlag: true,\r\n            sfshVisiable: false,\r\n            shForm: {},\r\n            chartVisiable: false,\r\n            addOrUpdateFlag:false,\r\n            contents:null,\r\n            layouts: '',\r\n\r\n            echartsDate: new Date(),//echarts的时间查询字段\r\n\r\n            //导出excel\r\n            json_fields: {\r\n                //级联表字段\r\n                //本表字段\r\n                     '字段': \"dicCode\",\r\n                     '字段名': \"dicName\",\r\n                     '编码': \"codeIndex\",\r\n                     '编码名字': \"indexName\",\r\n                     '备注': \"beizhu\",\r\n            },\r\n\r\n            };\r\n        },\r\n        created() {\r\n            this.contents = styleJs.listStyle();\r\n            this.init();\r\n            this.getDataList();\r\n            this.contentStyleChange()\r\n        },\r\n        mounted() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n        },\r\n        filters: {\r\n            htmlfilter: function (val) {\r\n                return val.replace(/<[^>]*>/g).replace(/undefined/g,'');\r\n            }\r\n        },\r\n        components: {\r\n            AddOrUpdate,\r\n        },\r\n        computed: {\r\n        },\r\n        methods: {\r\n            chartDialog() {\r\n                let _this = this;\r\n                let params = {\r\n                    dateFormat :\"%Y\"\r\n                    ,riqi :_this.echartsDate.getFullYear()\r\n                    ,thisTable : {//当前表\r\n                        tableName :\"shangdian_shouyin\"//当前表表名\r\n                        ,sumColum : 'shangdian_shouyin_true_price' //求和字段\r\n                        ,date : 'insert_time'//分组日期字段\r\n                        // ,string : 'name,leixing'//分组字符串字段\r\n                        // ,types : 'shangdian_shouyin_types'//分组下拉框字段\r\n                    }\r\n                    // ,joinTable : {//级联表（可以不存在）\r\n                    //     tableName :\"yuangong\"//级联表表名\r\n                    //     // ,date : 'insert_time'//分组日期字段\r\n                    //     ,string : 'yuangong_name'//分组字符串字段\r\n                    //     // ,types : 'insertTime'//分组下拉框字段\r\n                    // }\r\n                }\r\n                _this.chartVisiable = true;\r\n                _this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"barSum\",\r\n                        method: \"get\",\r\n                        params: params\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n\r\n\r\n                            //柱状图 求和 已成功使用\r\n                            //start\r\n                            let series = [];//具体数据值\r\n                            data.data.yAxis.forEach(function (item,index) {\r\n                                let tempMap = {};\r\n                                tempMap.name=data.data.legend[index];\r\n                                tempMap.type='bar';\r\n                                tempMap.data=item;\r\n                                series.push(tempMap);\r\n\r\n                            })\r\n\r\n                            var option = {\r\n                                tooltip: {\r\n                                    trigger: 'axis',\r\n                                    axisPointer: {\r\n                                        type: 'cross',\r\n                                        crossStyle: {\r\n                                            color: '#999'\r\n                                        }\r\n                                    }\r\n                                },\r\n                                toolbox: {\r\n                                    feature: {\r\n                                        // dataView: { show: true, readOnly: false },  // 数据查看\r\n                                        magicType: { show: true, type: ['line', 'bar'] },//切换图形展示方式\r\n                                        // restore: { show: true }, // 刷新\r\n                                        saveAsImage: { show: true }//保存\r\n                                    }\r\n                                },\r\n                                legend: {\r\n                                    data: data.data.legend//标题  可以点击导致某一列数据消失\r\n                                },\r\n                                xAxis: [\r\n                                    {\r\n                                        type: 'category',\r\n                                        name: '月份',\r\n                                        data: data.data.xAxis,\r\n                                        axisPointer: {\r\n                                            type: 'shadow'\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                yAxis: [\r\n                                    {\r\n                                        type: 'value',//不能改\r\n                                        name: '元',//y轴单位\r\n                                        axisLabel: {\r\n                                            formatter: '{value} 元' // 后缀\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                series:series//具体数据\r\n                            };\r\n                            // 使用刚指定的配置项和数据显示图表。\r\n                            statistic.setOption(option);\r\n                            //根据窗口的大小变动图表\r\n                            window.onresize = function () {\r\n                                statistic.resize();\r\n                            };\r\n                            //end\r\n\r\n\r\n\r\n                            //饼状图 原先自带的 未修改过\r\n                            //start\r\n                            /*let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表。\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图表\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }*/\r\n\r\n                            //end\r\n                        }else {\r\n                            this.$message({\r\n                                message: \"报表未查询到数据\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n                this.chartVisiable = !this.chartVisiable;\r\n                this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"group/xinzitongji/xinzi\",\r\n                        method: \"get\",\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表。\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图表\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }\r\n                        }\r\n                    });\r\n                // xcolumn ycolumn\r\n                });\r\n            },\r\n            contentStyleChange() {\r\n                this.contentSearchStyleChange()\r\n                this.contentBtnAdAllStyleChange()\r\n                this.contentSearchBtnStyleChange()\r\n                this.contentTableBtnStyleChange()\r\n                this.contentPageStyleChange()\r\n            },\r\n            contentSearchStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-input__inner').forEach(el => {\r\n                        let textAlign = 'left'\r\n                        if(this.contents.inputFontPosition == 2)\r\n                            textAlign = 'center'\r\n                            if (this.contents.inputFontPosition == 3) textAlign = 'right'\r\n                                el.style.textAlign = textAlign\r\n                            el.style.height = this.contents.inputHeight\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                            el.style.color = this.contents.inputFontColor\r\n                            el.style.fontSize = this.contents.inputFontSize\r\n                            el.style.borderWidth = this.contents.inputBorderWidth\r\n                            el.style.borderStyle = this.contents.inputBorderStyle\r\n                            el.style.borderColor = this.contents.inputBorderColor\r\n                            el.style.borderRadius = this.contents.inputBorderRadius\r\n                            el.style.backgroundColor = this.contents.inputBgColor\r\n                    })\r\n                    if (this.contents.inputTitle) {\r\n                        document.querySelectorAll('.form-content .slt .el-form-item__label').forEach(el => {\r\n                            el.style.color = this.contents.inputTitleColor\r\n                            el.style.fontSize = this.contents.inputTitleSize\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }\r\n                    setTimeout(() => {\r\n                        document.querySelectorAll('.form-content .slt .el-input__prefix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__suffix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__icon').forEach(el => {\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }, 10 )\r\n                })\r\n            },\r\n            // 搜索按钮\r\n            contentSearchBtnStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.searchBtnHeight\r\n                        el.style.color = this.contents.searchBtnFontColor\r\n                        el.style.fontSize = this.contents.searchBtnFontSize\r\n                        el.style.borderWidth = this.contents.searchBtnBorderWidth\r\n                        el.style.borderStyle = this.contents.searchBtnBorderStyle\r\n                        el.style.borderColor = this.contents.searchBtnBorderColor\r\n                        el.style.borderRadius = this.contents.searchBtnBorderRadius\r\n                        el.style.backgroundColor = this.contents.searchBtnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 新增、批量删除\r\n            contentBtnAdAllStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .ad .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllAddFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllAddBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--danger').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllDelFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllDelBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--warning').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllWarnFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllWarnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 表格\r\n            rowStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {color: this.contents.tableStripeFontColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            cellStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {backgroundColor: this.contents.tableStripeBgColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            headerRowStyle({row, rowIndex}) {\r\n                return {color: this.contents.tableHeaderFontColor}\r\n            },\r\n            headerCellStyle({row, rowIndex}) {\r\n                return {backgroundColor: this.contents.tableHeaderBgColor}\r\n            },\r\n            // 表格按钮\r\n            contentTableBtnStyleChange() {\r\n                // this.$nextTick(()=>{\r\n                //   setTimeout(()=>{\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--success').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDetailFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDetailBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--primary').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnEditFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnEditBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--danger').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDelFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDelBgColor\r\n                //     })\r\n\r\n                //   }, 50)\r\n                // })\r\n            },\r\n            // 分页\r\n            contentPageStyleChange() {\r\n                let arr = []\r\n                if (this.contents.pageTotal) arr.push('total')\r\n                if (this.contents.pageSizes) arr.push('sizes')\r\n                if (this.contents.pagePrevNext) {\r\n                    arr.push('prev')\r\n                    if (this.contents.pagePager) arr.push('pager')\r\n                    arr.push('next')\r\n                }\r\n                if (this.contents.pageJumper) arr.push('jumper')\r\n                this.layouts = arr.join()\r\n                this.contents.pageEachNum = 10\r\n            },\r\n\r\n            init() {\r\n            },\r\n            search() {\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 获取数据列表\r\n            getDataList() {\r\n                this.dataListLoading = true;\r\n                let params = {\r\n                    page: this.pageIndex,\r\n                    limit: this.pageSize,\r\n                    sort: 'id',\r\n                }\r\n\r\n                                         \r\n                if (this.searchForm.indexName!= '' && this.searchForm.indexName!= undefined) {\r\n                    params['indexName'] = '%' + this.searchForm.indexName + '%'\r\n                }\r\n                        \r\n                params['dictionaryDelete'] = 1// 逻辑删除字段 1 未删除 2 删除\r\n\r\n\r\n                this.$http({\r\n                    url: \"dictionary/page\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.dataList = data.data.list;\r\n                        this.totalPage = data.data.total;\r\n                    }else{\r\n                        this.dataList = [];\r\n                        this.totalPage = 0;\r\n                    }\r\n                    this.dataListLoading = false;\r\n                });\r\n\r\n                //查询级联表搜索条件所有列表\r\n                //查询当前表搜索条件所有列表\r\n            },\r\n            //每页数\r\n            sizeChangeHandle(val) {\r\n                this.pageSize = val;\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 当前页\r\n            currentChangeHandle(val) {\r\n                this.pageIndex = val;\r\n                this.getDataList();\r\n            },\r\n            // 多选\r\n            selectionChangeHandler(val) {\r\n                this.dataListSelections = val;\r\n            },\r\n            // 添加/修改\r\n            addOrUpdateHandler(id, type) {\r\n                this.showFlag = false;\r\n                this.addOrUpdateFlag = true;\r\n                this.crossAddOrUpdateFlag = false;\r\n                if (type != 'info') {\r\n                    type = 'else';\r\n                }\r\n                this.$nextTick(() => {\r\n                    this.$refs.addOrUpdate.init(id, type);\r\n                });\r\n            },\r\n            // 下载\r\n            download(file) {\r\n                window.open(\" ${file} \")\r\n            },\r\n            // 删除\r\n            deleteHandler(id) {\r\n                var ids = id ? [Number(id)] : this.dataListSelections.map(item => {\r\n                    return Number(item.id);\r\n                });\r\n\r\n                this.$confirm(`确定进行[${id ? \"删除\" : \"批量删除\"}]操作?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: \"dictionary/delete\",\r\n                        method: \"post\",\r\n                        data: ids\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            this.$message({\r\n                                message: \"操作成功\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }else{\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n            // 导入功能上传文件成功后调用导入方法\r\n            dictionaryUploadSuccess(data){\r\n                let _this = this;\r\n                _this.$http({\r\n                    url: \"dictionary/batchInsert?fileName=\" + data.file,\r\n                    method: \"get\"\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        _this.$message({\r\n                            message: \"导入字典数据成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.search();\r\n                            }\r\n                        });\r\n                    }else{\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n\r\n            },\r\n            // 导入功能上传文件失败后调用导入方法\r\n            dictionaryUploadError(data){\r\n                this.$message.error('上传失败');\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.slt {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .ad {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .pages {\r\n    & /deep/ el-pagination__sizes{\r\n      & /deep/ el-input__inner {\r\n        height: 22px;\r\n        line-height: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .el-button+.el-button {\r\n    margin:0;\r\n  }\r\n\r\n  .tables {\r\n\t& /deep/ .el-button--success {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(121, 244, 164, 0.54);\r\n\t}\r\n\r\n\t& /deep/ .el-button--primary {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(169, 115, 232, 0.65);\r\n\t}\r\n\r\n\t& /deep/ .el-button--danger {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(241, 120, 186, 1);\r\n\t}\r\n\r\n    & /deep/ .el-button {\r\n      margin: 4px;\r\n    }\r\n  }\r\n</style>\r\n\r\n\r\n"]}]}