{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\update-password.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\update-password.vue", "mtime": 1642386765426}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "dialogVisible", "ruleForm", "user", "rules", "password", "required", "message", "trigger", "newpassword", "repassword", "mounted", "_this", "$http", "url", "concat", "$storage", "get", "method", "then", "_ref", "code", "$message", "error", "msg", "methods", "onLogout", "remove", "$router", "replace", "name", "onUpdateHandler", "_this2", "$refs", "validate", "valid", "mima", "_ref2", "type", "duration", "onClose"], "sources": ["src/views/update-password.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      class=\"detail-form-content\"\r\n      ref=\"ruleForm\"\r\n      :rules=\"rules\"\r\n      :model=\"ruleForm\"\r\n      label-width=\"80px\"\r\n    >\r\n      <el-form-item label=\"原密码\" prop=\"password\">\r\n        <el-input v-model=\"ruleForm.password\" show-password></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"新密码\" prop=\"newpassword\">\r\n        <el-input type=\"password\" v-model=\"ruleForm.newpassword\" show-password></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"确认密码\" prop=\"repassword\">\r\n        <el-input type=\"password\" v-model=\"ruleForm.repassword\" show-password></el-input>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"onUpdateHandler\">确 定</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      ruleForm: {},\r\n      user: {},\r\n      rules: {\r\n        password: [\r\n          {\r\n            required: true,\r\n            message: \"密码不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n        newpassword: [\r\n          {\r\n            required: true,\r\n            message: \"新密码不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n        repassword: [\r\n          {\r\n            required: true,\r\n            message: \"确认密码不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.$http({\r\n      url: `${this.$storage.get(\"sessionTable\")}/session`,\r\n      method: \"get\"\r\n    }).then(({ data }) => {\r\n      if (data && data.code === 0) {\r\n        this.user = data.data;\r\n      } else {\r\n        this.$message.error(data.msg);\r\n      }\r\n    });\r\n  },\r\n  methods: {\r\n    onLogout() {\r\n      this.$storage.remove(\"Token\");\r\n      this.$router.replace({ name: \"login\" });\r\n    },\r\n    // 修改密码\r\n    onUpdateHandler() {\r\n      this.$refs[\"ruleForm\"].validate(valid => {\r\n        if (valid) {\r\n          var password = \"\";\r\n          if (this.user.mima) {\r\n            password = this.user.mima;\r\n          } else if (this.user.password) {\r\n            password = this.user.password;\r\n          }\r\n          if (this.ruleForm.password != password) {\r\n            this.$message.error(\"原密码错误\");\r\n            return;\r\n          }\r\n          if (this.ruleForm.newpassword != this.ruleForm.repassword) {\r\n            this.$message.error(\"两次密码输入不一致\");\r\n            return;\r\n          }\r\n          this.user.password = this.ruleForm.newpassword;\r\n          this.user.mima = this.ruleForm.newpassword;\r\n          this.$http({\r\n            url: `${this.$storage.get(\"sessionTable\")}/update`,\r\n            method: \"post\",\r\n            data: this.user\r\n          }).then(({ data }) => {\r\n            if (data && data.code === 0) {\r\n              this.$message({\r\n                message: \"修改密码成功,下次登录系统生效\",\r\n                type: \"success\",\r\n                duration: 1500,\r\n                onClose: () => {\r\n                }\r\n              });\r\n            } else {\r\n              this.$message.error(data.msg);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n"], "mappings": ";;AAyBA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;QACAC,QAAA,GACA;UACAC,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,WAAA,GACA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAE,UAAA,GACA;UACAJ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,KAAA;MACAC,GAAA,KAAAC,MAAA,MAAAC,QAAA,CAAAC,GAAA;MACAC,MAAA;IACA,GAAAC,IAAA,WAAAC,IAAA;MAAA,IAAApB,IAAA,GAAAoB,IAAA,CAAApB,IAAA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAqB,IAAA;QACAT,KAAA,CAAAT,IAAA,GAAAH,IAAA,CAAAA,IAAA;MACA;QACAY,KAAA,CAAAU,QAAA,CAAAC,KAAA,CAAAvB,IAAA,CAAAwB,GAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAA;MACA,KAAAV,QAAA,CAAAW,MAAA;MACA,KAAAC,OAAA,CAAAC,OAAA;QAAAC,IAAA;MAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAA9B,QAAA;UACA,IAAA2B,MAAA,CAAA7B,IAAA,CAAAiC,IAAA;YACA/B,QAAA,GAAA2B,MAAA,CAAA7B,IAAA,CAAAiC,IAAA;UACA,WAAAJ,MAAA,CAAA7B,IAAA,CAAAE,QAAA;YACAA,QAAA,GAAA2B,MAAA,CAAA7B,IAAA,CAAAE,QAAA;UACA;UACA,IAAA2B,MAAA,CAAA9B,QAAA,CAAAG,QAAA,IAAAA,QAAA;YACA2B,MAAA,CAAAV,QAAA,CAAAC,KAAA;YACA;UACA;UACA,IAAAS,MAAA,CAAA9B,QAAA,CAAAO,WAAA,IAAAuB,MAAA,CAAA9B,QAAA,CAAAQ,UAAA;YACAsB,MAAA,CAAAV,QAAA,CAAAC,KAAA;YACA;UACA;UACAS,MAAA,CAAA7B,IAAA,CAAAE,QAAA,GAAA2B,MAAA,CAAA9B,QAAA,CAAAO,WAAA;UACAuB,MAAA,CAAA7B,IAAA,CAAAiC,IAAA,GAAAJ,MAAA,CAAA9B,QAAA,CAAAO,WAAA;UACAuB,MAAA,CAAAnB,KAAA;YACAC,GAAA,KAAAC,MAAA,CAAAiB,MAAA,CAAAhB,QAAA,CAAAC,GAAA;YACAC,MAAA;YACAlB,IAAA,EAAAgC,MAAA,CAAA7B;UACA,GAAAgB,IAAA,WAAAkB,KAAA;YAAA,IAAArC,IAAA,GAAAqC,KAAA,CAAArC,IAAA;YACA,IAAAA,IAAA,IAAAA,IAAA,CAAAqB,IAAA;cACAW,MAAA,CAAAV,QAAA;gBACAf,OAAA;gBACA+B,IAAA;gBACAC,QAAA;gBACAC,OAAA,WAAAA,QAAA,GACA;cACA;YACA;cACAR,MAAA,CAAAV,QAAA,CAAAC,KAAA,CAAAvB,IAAA,CAAAwB,GAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}