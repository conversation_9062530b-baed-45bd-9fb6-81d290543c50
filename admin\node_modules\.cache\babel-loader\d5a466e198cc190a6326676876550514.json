{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\src\\utils\\i18n.js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\utils\\i18n.js", "mtime": 1642386765405}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gdHJhbnNsYXRlIHJvdXRlci5tZXRhLnRpdGxlLCBiZSB1c2VkIGluIGJyZWFkY3J1bWIgc2lkZWJhciB0YWdzdmlldwpleHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVUaXRsZSh0aXRsZSkgewogIHZhciBoYXNLZXkgPSB0aGlzLiR0ZSgncm91dGUuJyArIHRpdGxlKTsKICBpZiAoaGFzS2V5KSB7CiAgICAvLyAkdCA6dGhpcyBtZXRob2QgZnJvbSB2dWUtaTE4biwgaW5qZWN0IGluIEAvbGFuZy9pbmRleC5qcwogICAgdmFyIHRyYW5zbGF0ZWRUaXRsZSA9IHRoaXMuJHQoJ3JvdXRlLicgKyB0aXRsZSk7CiAgICByZXR1cm4gdHJhbnNsYXRlZFRpdGxlOwogIH0KICByZXR1cm4gdGl0bGU7Cn0="}, {"version": 3, "names": ["generateTitle", "title", "<PERSON><PERSON><PERSON>", "$te", "translatedTitle", "$t"], "sources": ["D:/1/tiyuguan/admin/src/utils/i18n.js"], "sourcesContent": ["// translate router.meta.title, be used in breadcrumb sidebar tagsview\r\nexport function generateTitle(title) {\r\n  const hasKey = this.$te('route.' + title)\r\n\r\n  if (hasKey) {\r\n    // $t :this method from vue-i18n, inject in @/lang/index.js\r\n    const translatedTitle = this.$t('route.' + title)\r\n\r\n    return translatedTitle\r\n  }\r\n  return title\r\n}\r\n"], "mappings": "AAAA;AACA,OAAO,SAASA,aAAaA,CAACC,KAAK,EAAE;EACnC,IAAMC,MAAM,GAAG,IAAI,CAACC,GAAG,CAAC,QAAQ,GAAGF,KAAK,CAAC;EAEzC,IAAIC,MAAM,EAAE;IACV;IACA,IAAME,eAAe,GAAG,IAAI,CAACC,EAAE,CAAC,QAAQ,GAAGJ,KAAK,CAAC;IAEjD,OAAOG,eAAe;EACxB;EACA,OAAOH,KAAK;AACd", "ignoreList": []}]}