{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\src\\utils\\validate.js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\utils\\validate.js", "mtime": 1642386765412}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudGVzdC5qcyI7Ci8qKg0KICog6YKu566xDQogKiBAcGFyYW0geyp9IHMNCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNFbWFpbChzKSB7CiAgcmV0dXJuIC9eKFthLXpBLVowLTlfLV0pK0AoW2EtekEtWjAtOV8tXSkrKCguW2EtekEtWjAtOV8tXXsyLDN9KXsxLDJ9KSQvLnRlc3Qocyk7Cn0KCi8qKg0KICog5omL5py65Y+356CBDQogKiBAcGFyYW0geyp9IHMNCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNNb2JpbGUocykgewogIHJldHVybiAvXjFbMC05XXsxMH0kLy50ZXN0KHMpOwp9CgovKioNCiAqIOeUteivneWPt+eggQ0KICogQHBhcmFtIHsqfSBzDQogKi8KZXhwb3J0IGZ1bmN0aW9uIGlzUGhvbmUocykgewogIHJldHVybiAvXihbMC05XXszLDR9LSk/WzAtOV17Nyw4fSQvLnRlc3Qocyk7Cn0KCi8qKg0KICogVVJM5Zyw5Z2ADQogKiBAcGFyYW0geyp9IHMNCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNVUkwocykgewogIHJldHVybiAvXmh0dHBbc10/OlwvXC8uKi8udGVzdChzKTsKfQoKLyoqDQogKiDljLnphY3mlbDlrZfvvIzlj6/ku6XmmK/lsI/mlbDvvIzkuI3lj6/ku6XmmK/otJ/mlbAs5Y+v5Lul5Li656m6DQogKiBAcGFyYW0geyp9IHMgDQogKi8KZXhwb3J0IGZ1bmN0aW9uIGlzTnVtYmVyKHMpIHsKICByZXR1cm4gLyheLT9bKy1dPyhbMC05XSpcLj9bMC05XSt8WzAtOV0rXC4/WzAtOV0qKShbZUVdWystXT9bMC05XSspPyQpfCheJCkvLnRlc3Qocyk7Cn0KLyoqDQogKiDljLnphY3mlbTmlbDvvIzlj6/ku6XkuLrnqboNCiAqIEBwYXJhbSB7Kn0gcyANCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNJbnROdW1lcihzKSB7CiAgcmV0dXJuIC8oXi0/XGQrJCl8KF4kKS8udGVzdChzKTsKfQovKioNCiAqIOi6q+S7veivgeagoemqjA0KICovCmV4cG9ydCBmdW5jdGlvbiBjaGVja0lkQ2FyZChpZGNhcmQpIHsKICB2YXIgcmVnSWRDYXJkID0gLyheXGR7MTV9JCl8KF5cZHsxOH0kKXwoXlxkezE3fShcZHxYfHgpJCkvOwogIGlmICghcmVnSWRDYXJkLnRlc3QoaWRjYXJkKSkgewogICAgcmV0dXJuIGZhbHNlOwogIH0gZWxzZSB7CiAgICByZXR1cm4gdHJ1ZTsKICB9Cn0="}, {"version": 3, "names": ["isEmail", "s", "test", "isMobile", "isPhone", "isURL", "isNumber", "isIntNumer", "checkIdCard", "idcard", "regIdCard"], "sources": ["D:/1/tiyuguan/admin/src/utils/validate.js"], "sourcesContent": ["/**\r\n * 邮箱\r\n * @param {*} s\r\n */\r\nexport function isEmail (s) {\r\n  return /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s)\r\n}\r\n\r\n/**\r\n * 手机号码\r\n * @param {*} s\r\n */\r\nexport function isMobile (s) {\r\n  return /^1[0-9]{10}$/.test(s)\r\n}\r\n\r\n/**\r\n * 电话号码\r\n * @param {*} s\r\n */\r\nexport function isPhone (s) {\r\n  return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s)\r\n}\r\n\r\n/**\r\n * URL地址\r\n * @param {*} s\r\n */\r\nexport function isURL (s) {\r\n  return /^http[s]?:\\/\\/.*/.test(s)\r\n}\r\n\r\n/**\r\n * 匹配数字，可以是小数，不可以是负数,可以为空\r\n * @param {*} s \r\n */\r\nexport function isNumber(s){\r\n  return  /(^-?[+-]?([0-9]*\\.?[0-9]+|[0-9]+\\.?[0-9]*)([eE][+-]?[0-9]+)?$)|(^$)/.test(s);\r\n}\r\n/**\r\n * 匹配整数，可以为空\r\n * @param {*} s \r\n */\r\nexport function isIntNumer(s){\r\n  return  /(^-?\\d+$)|(^$)/.test(s);\r\n}\r\n/**\r\n * 身份证校验\r\n */\r\nexport function checkIdCard(idcard) {\r\n  const regIdCard = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n  if (!regIdCard.test(idcard)) {\r\n      return false;\r\n  } else {\r\n      return true;\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAAEC,CAAC,EAAE;EAC1B,OAAO,iEAAiE,CAACC,IAAI,CAACD,CAAC,CAAC;AAClF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,QAAQA,CAAEF,CAAC,EAAE;EAC3B,OAAO,cAAc,CAACC,IAAI,CAACD,CAAC,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASG,OAAOA,CAAEH,CAAC,EAAE;EAC1B,OAAO,4BAA4B,CAACC,IAAI,CAACD,CAAC,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASI,KAAKA,CAAEJ,CAAC,EAAE;EACxB,OAAO,kBAAkB,CAACC,IAAI,CAACD,CAAC,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASK,QAAQA,CAACL,CAAC,EAAC;EACzB,OAAQ,qEAAqE,CAACC,IAAI,CAACD,CAAC,CAAC;AACvF;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,UAAUA,CAACN,CAAC,EAAC;EAC3B,OAAQ,gBAAgB,CAACC,IAAI,CAACD,CAAC,CAAC;AAClC;AACA;AACA;AACA;AACA,OAAO,SAASO,WAAWA,CAACC,MAAM,EAAE;EAClC,IAAMC,SAAS,GAAG,0CAA0C;EAC5D,IAAI,CAACA,SAAS,CAACR,IAAI,CAACO,MAAM,CAAC,EAAE;IACzB,OAAO,KAAK;EAChB,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACF", "ignoreList": []}]}