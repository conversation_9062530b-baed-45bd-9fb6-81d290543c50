{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\common\\FileUpload.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\common\\FileUpload.vue", "mtime": 1750583733678}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["storage", "base", "data", "dialogVisible", "dialogImageUrl", "fileList", "fileUrlList", "myHeaders", "props", "mounted", "init", "get", "watch", "fileUrls", "val", "oldVal", "computed", "getActionUrl", "concat", "$base", "name", "action", "methods", "split", "fileArray", "for<PERSON>ach", "item", "index", "url", "file", "push", "setFileList", "handleBeforeUpload", "handleUploadSuccess", "res", "code", "length", "response", "$emit", "join", "$message", "error", "msg", "handleUploadErr", "err", "handleRemove", "handleUploadPreview", "handleExceed", "files", "warning", "fileUrlArray", "token"], "sources": ["src/components/common/FileUpload.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 上传文件组件 -->\r\n    <el-upload\r\n      ref=\"upload\"\r\n      :action=\"getActionUrl\"\r\n      list-type=\"picture-card\"\r\n      :multiple=\"multiple\"\r\n      :limit=\"limit\"\r\n      :headers=\"myHeaders\"\r\n      :file-list=\"fileList\"\r\n      :on-exceed=\"handleExceed\"\r\n      :on-preview=\"handleUploadPreview\"\r\n      :on-remove=\"handleRemove\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :on-error=\"handleUploadErr\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n    >\r\n      <i class=\"el-icon-plus\"></i>\r\n      <div slot=\"tip\" class=\"el-upload__tip\" style=\"color:#838fa1;\">{{tip}}</div>\r\n    </el-upload>\r\n    <el-dialog :visible.sync=\"dialogVisible\" size=\"tiny\" append-to-body>\r\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport storage from \"@/utils/storage\";\r\nimport base from \"@/utils/base\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 查看大图\r\n      dialogVisible: false,\r\n      // 查看大图\r\n      dialogImageUrl: \"\",\r\n      // 组件渲染图片的数组字段，有特殊格式要�?\n      fileList: [],\r\n      fileUrlList: [],\r\n      myHeaders:{}\r\n    };\r\n  },\r\n  props: [\"tip\", \"action\", \"limit\", \"multiple\", \"fileUrls\"],\r\n  mounted() {\r\n    this.init();\r\n    this.myHeaders= {\r\n      'Token':storage.get(\"Token\")\r\n    }\r\n  },\r\n  watch: {\r\n    fileUrls: function(val, oldVal) {\r\n      //   console.log(\"new: %s, old: %s\", val, oldVal);\r\n      this.init();\r\n    }\r\n  },\r\n  computed: {\r\n    // 计算属性的 getter\r\n    getActionUrl: function() {\r\n      // return base.url + this.action + \"?token=\" + storage.get(\"token\");\r\n      return `/${this.$base.name}/` + this.action;\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始�?\n    init() {\r\n      //   console.log(this.fileUrls);\r\n      if (this.fileUrls) {\r\n        this.fileUrlList = this.fileUrls.split(\",\");\r\n        let fileArray = [];\r\n        this.fileUrlList.forEach(function(item, index) {\r\n          var url = item;\r\n          var name = index;\r\n          var file = {\r\n            name: name,\r\n            url: url\r\n          };\r\n          fileArray.push(file);\r\n        });\r\n        this.setFileList(fileArray);\r\n      }\r\n    },\r\n    handleBeforeUpload(file) {\r\n\t\r\n    },\r\n    // 上传文件成功后执�?\n    handleUploadSuccess(res, file, fileList) {\r\n      if (res && res.code === 0) {\r\n        fileList[fileList.length - 1][\"url\"] =\r\n          this.$base.url + \"upload/\" + file.response.file;\r\n        this.setFileList(fileList);\r\n        this.$emit(\"change\", this.fileUrlList.join(\",\"));\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    // 图片上传失败\r\n    handleUploadErr(err, file, fileList) {\r\n      this.$message.error(\"文件上传失败\");\r\n    },\r\n    // 移除图片\r\n    handleRemove(file, fileList) {\r\n      this.setFileList(fileList);\r\n      this.$emit(\"change\", this.fileUrlList.join(\",\"));\r\n    },\r\n    // 查看大图\r\n    handleUploadPreview(file) {\r\n      this.dialogImageUrl = file.url;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 限制图片数量\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning(`最多上�?{this.limit}张图片`);\r\n    },\r\n    // 重新对fileList进行赋�?\n    setFileList(fileList) {\r\n      var fileArray = [];\r\n      var fileUrlArray = [];\r\n      // 有些图片不是公开的，所以需要携带token信息做权限校�?\n      var token = storage.get(\"token\");\r\n      fileList.forEach(function(item, index) {\r\n        var url = item.url.split(\"?\")[0];\r\n        var name = item.name;\r\n        var file = {\r\n          name: name,\r\n          url: url + \"?token=\" + token\r\n        };\r\n        fileArray.push(file);\r\n        fileUrlArray.push(url);\r\n      });\r\n      this.fileList = fileArray;\r\n      this.fileUrlList = fileUrlArray;\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n\r\n"], "mappings": ";;;;;;AA2BA,OAAAA,OAAA;AACA,OAAAC,IAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,aAAA;MACA;MACAC,cAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;IACA,KAAAH,SAAA;MACA,SAAAP,OAAA,CAAAW,GAAA;IACA;EACA;EACAC,KAAA;IACAC,QAAA,WAAAA,SAAAC,GAAA,EAAAC,MAAA;MACA;MACA,KAAAL,IAAA;IACA;EACA;EACAM,QAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA;MACA,WAAAC,MAAA,MAAAC,KAAA,CAAAC,IAAA,cAAAC,MAAA;IACA;EACA;EACAC,OAAA;IACA;IACAZ,IAAA,WAAAA,KAAA;MACA;MACA,SAAAG,QAAA;QACA,KAAAP,WAAA,QAAAO,QAAA,CAAAU,KAAA;QACA,IAAAC,SAAA;QACA,KAAAlB,WAAA,CAAAmB,OAAA,WAAAC,IAAA,EAAAC,KAAA;UACA,IAAAC,GAAA,GAAAF,IAAA;UACA,IAAAN,IAAA,GAAAO,KAAA;UACA,IAAAE,IAAA;YACAT,IAAA,EAAAA,IAAA;YACAQ,GAAA,EAAAA;UACA;UACAJ,SAAA,CAAAM,IAAA,CAAAD,IAAA;QACA;QACA,KAAAE,WAAA,CAAAP,SAAA;MACA;IACA;IACAQ,kBAAA,WAAAA,mBAAAH,IAAA,GAEA;IACA;IACAI,mBAAA,WAAAA,oBAAAC,GAAA,EAAAL,IAAA,EAAAxB,QAAA;MACA,IAAA6B,GAAA,IAAAA,GAAA,CAAAC,IAAA;QACA9B,QAAA,CAAAA,QAAA,CAAA+B,MAAA,eACA,KAAAjB,KAAA,CAAAS,GAAA,eAAAC,IAAA,CAAAQ,QAAA,CAAAR,IAAA;QACA,KAAAE,WAAA,CAAA1B,QAAA;QACA,KAAAiC,KAAA,gBAAAhC,WAAA,CAAAiC,IAAA;MACA;QACA,KAAAC,QAAA,CAAAC,KAAA,CAAAP,GAAA,CAAAQ,GAAA;MACA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAAC,GAAA,EAAAf,IAAA,EAAAxB,QAAA;MACA,KAAAmC,QAAA,CAAAC,KAAA;IACA;IACA;IACAI,YAAA,WAAAA,aAAAhB,IAAA,EAAAxB,QAAA;MACA,KAAA0B,WAAA,CAAA1B,QAAA;MACA,KAAAiC,KAAA,gBAAAhC,WAAA,CAAAiC,IAAA;IACA;IACA;IACAO,mBAAA,WAAAA,oBAAAjB,IAAA;MACA,KAAAzB,cAAA,GAAAyB,IAAA,CAAAD,GAAA;MACA,KAAAzB,aAAA;IACA;IACA;IACA4C,YAAA,WAAAA,aAAAC,KAAA,EAAA3C,QAAA;MACA,KAAAmC,QAAA,CAAAS,OAAA;IACA;IACA;IACAlB,WAAA,WAAAA,YAAA1B,QAAA;MACA,IAAAmB,SAAA;MACA,IAAA0B,YAAA;MACA;MACA,IAAAC,KAAA,GAAAnD,OAAA,CAAAW,GAAA;MACAN,QAAA,CAAAoB,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACA,IAAAC,GAAA,GAAAF,IAAA,CAAAE,GAAA,CAAAL,KAAA;QACA,IAAAH,IAAA,GAAAM,IAAA,CAAAN,IAAA;QACA,IAAAS,IAAA;UACAT,IAAA,EAAAA,IAAA;UACAQ,GAAA,EAAAA,GAAA,eAAAuB;QACA;QACA3B,SAAA,CAAAM,IAAA,CAAAD,IAAA;QACAqB,YAAA,CAAApB,IAAA,CAAAF,GAAA;MACA;MACA,KAAAvB,QAAA,GAAAmB,SAAA;MACA,KAAAlB,WAAA,GAAA4C,YAAA;IACA;EACA;AACA", "ignoreList": []}]}