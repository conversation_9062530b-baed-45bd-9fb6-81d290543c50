{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\register.vue?vue&type=style&index=0&id=77453986&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\register.vue", "mtime": 1750584224769}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";AAwWA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"container\">\r\n            <div class=\"login-form\" style=\"backgroundColor:rgba(183, 174, 174, 0.5);borderRadius:22px\">\r\n                <h1 class=\"h1\" style=\"color:#000;fontSize:28px;\">体育馆使用预约平台注�?/h1>\r\n                <el-form ref=\"rgsForm\" class=\"rgs-form\" :model=\"rgsForm\" label-width=\"120px\">\r\n                        <el-form-item label=\"账号\" class=\"input\">\r\n                            <el-input v-model=\"ruleForm.username\" autocomplete=\"off\" placeholder=\"账号\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"密码\" class=\"input\">\r\n                            <el-input type=\"password\" v-model=\"ruleForm.password\" autocomplete=\"off\" show-password/>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"重复密码\" class=\"input\">\r\n                            <el-input type=\"password\" v-model=\"ruleForm.repetitionPassword\" autocomplete=\"off\" show-password/>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"用户姓名\" class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuName\" autocomplete=\"off\" placeholder=\"用户姓名\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"用户手机�? class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuPhone\" autocomplete=\"off\" placeholder=\"用户手机�?  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"用户身份证号\" class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuIdNumber\" autocomplete=\"off\" placeholder=\"用户身份证号\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"电子邮箱\" class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuEmail\" autocomplete=\"off\" placeholder=\"电子邮箱\"  />\r\n                        </el-form-item>\r\n                        <div style=\"display: flex;flex-wrap: wrap;width: 100%;justify-content: center;\">\r\n                            <el-button class=\"btn\" type=\"primary\" @click=\"login()\">注册</el-button>\r\n                            <el-button class=\"btn close\" type=\"primary\" @click=\"close()\">取消</el-button>\r\n                        </div>\r\n                </el-form>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    export default {\r\n        data() {\r\n            return {\r\n                ruleForm: {\r\n                },\r\n                tableName:\"\",\r\n                rules: {},\r\n                sexTypesOptions : [],\r\n            };\r\n        },\r\n        mounted(){\r\n            let table = this.$storage.get(\"loginTable\");\r\n            this.tableName = table;\r\n        },\r\n        methods: {\r\n            // 获取uuid\r\n            getUUID () {\r\n                return new Date().getTime();\r\n            },\r\n            close(){\r\n                this.$router.push({ path: \"/login\" });\r\n            },\r\n            // 注册\r\n            login() {\r\n\r\n                            if((!this.ruleForm.username)){\r\n                                this.$message.error('账号不能为空');\r\n                                return\r\n                            }\r\n                            if((!this.ruleForm.password)){\r\n                                this.$message.error('密码不能为空');\r\n                                return\r\n                            }\r\n                            if((!this.ruleForm.repetitionPassword)){\r\n                                this.$message.error('重复密码不能为空');\r\n                                return\r\n                            }\r\n                            if(this.ruleForm.repetitionPassword != this.ruleForm.password){\r\n                                this.$message.error('密码和重复密码不一致');\r\n                                return\r\n                            }\r\n                            if((!this.ruleForm.yonghuName)&& 'yonghu'==this.tableName){\r\n                                this.$message.error('用户姓名不能为空');\r\n                                return\r\n                            }\r\n                             if('yonghu' == this.tableName && this.ruleForm.yonghuPhone&&(!this.$validate.isMobile(this.ruleForm.yonghuPhone))){\r\n                                 this.$message.error('手机应输入手机格式');\r\n                                 return\r\n                             }\r\n                            if((!this.ruleForm.yonghuIdNumber)&& 'yonghu'==this.tableName){\r\n                                this.$message.error('用户身份证号不能为空');\r\n                                return\r\n                            }\r\n                            if('yonghu' == this.tableName && this.ruleForm.yonghuEmail&&(!this.$validate.isEmail(this.ruleForm.yonghuEmail))){\r\n                                this.$message.error(\"邮箱应输入邮件格式\");\r\n                                return\r\n                            }\r\n                this.$http({\r\n                    url: `${this.tableName}/register`,\r\n                    method: \"post\",\r\n                    data:this.ruleForm\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                    this.$message({\r\n                        message: \"注册成功,请登录后在个人中心页面补充个人数据\",\r\n                        type: \"success\",\r\n                        duration: 1500,\r\n                        onClose: () => {\r\n                        this.$router.replace({ path: \"/login\" });\r\n                }\r\n                });\r\n                } else {\r\n                    this.$message.error(data.msg);\r\n                }\r\n            });\r\n            }\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n    .el-radio__input.is-checked .el-radio__inner {\r\n        border-color: #00c292;\r\n        background: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked .el-radio__inner {\r\n        border-color: #00c292;\r\n        background: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked .el-radio__inner {\r\n        border-color: #00c292;\r\n        background: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked+.el-radio__label {\r\n        color: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked+.el-radio__label {\r\n        color: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked+.el-radio__label {\r\n        color: #00c292;\r\n    }\r\n\r\n    .h1 {\r\n        margin-top: 10px;\r\n    }\r\n\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n\r\n    // .container {\r\n       //    min-height: 100vh;\r\n       //    text-align: center;\r\n       //    // background-color: #00c292;\r\n       //    padding-top: 20vh;\r\n       //    background-image: url(../assets/img/bg.jpg);\r\n       //    background-size: 100% 100%;\r\n       //    opacity: 0.9;\r\n       //  }\r\n\r\n    // .login-form:before {\r\n       // \tvertical-align: middle;\r\n       // \tdisplay: inline-block;\r\n       // }\r\n\r\n    // .login-form {\r\n       // \tmax-width: 500px;\r\n       // \tpadding: 20px 0;\r\n       // \twidth: 80%;\r\n       // \tposition: relative;\r\n       // \tmargin: 0 auto;\r\n\r\n    // \t.label {\r\n          // \t\tmin-width: 60px;\r\n          // \t}\r\n\r\n    // \t.input-group {\r\n          // \t\tmax-width: 500px;\r\n          // \t\tpadding: 20px 0;\r\n          // \t\twidth: 80%;\r\n          // \t\tposition: relative;\r\n          // \t\tmargin: 0 auto;\r\n          // \t\tdisplay: flex;\r\n          // \t\talign-items: center;\r\n\r\n    // \t\t.input-container {\r\n              // \t\t\tdisplay: inline-block;\r\n              // \t\t\twidth: 100%;\r\n              // \t\t\ttext-align: left;\r\n              // \t\t\tmargin-left: 10px;\r\n              // \t\t}\r\n\r\n    // \t\t.icon {\r\n              // \t\t\twidth: 30px;\r\n              // \t\t\theight: 30px;\r\n              // \t\t}\r\n\r\n    // \t\t.input {\r\n              // \t\t\tposition: relative;\r\n              // \t\t\tz-index: 2;\r\n              // \t\t\tfloat: left;\r\n              // \t\t\twidth: 100%;\r\n              // \t\t\tmargin-bottom: 0;\r\n              // \t\t\tbox-shadow: none;\r\n              // \t\t\tborder-top: 0px solid #ccc;\r\n              // \t\t\tborder-left: 0px solid #ccc;\r\n              // \t\t\tborder-right: 0px solid #ccc;\r\n              // \t\t\tborder-bottom: 1px solid #ccc;\r\n              // \t\t\tpadding: 0px;\r\n              // \t\t\tresize: none;\r\n              // \t\t\tborder-radius: 0px;\r\n              // \t\t\tdisplay: block;\r\n              // \t\t\twidth: 100%;\r\n              // \t\t\theight: 34px;\r\n              // \t\t\tpadding: 6px 12px;\r\n              // \t\t\tfont-size: 14px;\r\n              // \t\t\tline-height: 1.42857143;\r\n              // \t\t\tcolor: #555;\r\n              // \t\t\tbackground-color: #fff;\r\n              // \t\t}\r\n\r\n    // \t}\r\n    // }\r\n\r\n    .nk-navigation {\r\n        margin-top: 15px;\r\n\r\n    a {\r\n        display: inline-block;\r\n        color: #fff;\r\n        background: rgba(255, 255, 255, .2);\r\n        width: 100px;\r\n        height: 50px;\r\n        border-radius: 30px;\r\n        text-align: center;\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        justify-content: center;\r\n        padding: 0 20px;\r\n    }\r\n\r\n    .icon {\r\n        margin-left: 10px;\r\n        width: 30px;\r\n        height: 30px;\r\n    }\r\n    }\r\n\r\n    .register-container {\r\n        margin-top: 10px;\r\n\r\n    a {\r\n        display: inline-block;\r\n        color: #fff;\r\n        max-width: 500px;\r\n        height: 50px;\r\n        border-radius: 30px;\r\n        text-align: center;\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        justify-content: center;\r\n        padding: 0 20px;\r\n\r\n    div {\r\n        margin-left: 10px;\r\n    }\r\n    }\r\n    }\r\n\r\n    .container {\r\n        background-image: url(\"/tiyuguan/img/back-img-bg.jpg\");\r\n        height: 100vh;\r\n        background-position: center center;\r\n        background-size: cover;\r\n        background-repeat: no-repeat;\r\n\r\n    .login-form {\r\n        right: 50%;\r\n        top: 50%;\r\n        height: auto;\r\n        transform: translate3d(50%, -50%, 0);\r\n        border-radius: 10px;\r\n        background-color: rgba(255,255,255,.5);\r\n        width: 420px;\r\n        padding: 30px 30px 40px 30px;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n\r\n    .h1 {\r\n        margin: 0;\r\n        text-align: center;\r\n        line-height: 54px;\r\n        font-size: 24px;\r\n        color: #000;\r\n    }\r\n\r\n    .rgs-form {\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        align-items: center;\r\n\r\n    .input {\r\n        width: 100%;\r\n\r\n    & ::v-deep .el-form-item__label {\r\n          line-height: 40px;\r\n          color: rgba(17, 16, 16, 1);\r\n          font-size: #606266;\r\n      }\r\n\r\n    & ::v-deep .el-input__inner {\r\n          height: 40px;\r\n          color: rgba(23, 24, 26, 1);\r\n          font-size: 14px;\r\n          border-width: 1px;\r\n          border-style: solid;\r\n          border-color: #606266;\r\n          border-radius: 22px;\r\n          background-color: #fff;\r\n      }\r\n    }\r\n\r\n    .btn {\r\n        margin: 0 10px;\r\n        width: 88px;\r\n        height: 44px;\r\n        color: #fff;\r\n        font-size: 14px;\r\n        border-width: 1px;\r\n        border-style: solid;\r\n        border-color: #409EFF;\r\n        border-radius: 22px;\r\n        background-color: #409EFF;\r\n    }\r\n\r\n    .close {\r\n        margin: 0 10px;\r\n        width: 88px;\r\n        height: 44px;\r\n        color: #409EFF;\r\n        font-size: 14px;\r\n        border-width: 1px;\r\n        border-style: solid;\r\n        border-color: #409EFF;\r\n        border-radius: 22px;\r\n        background-color: #FFF;\r\n    }\r\n\r\n    }\r\n    }\r\n    }\r\n</style>\r\n\r\n"]}]}