{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\common\\Editor.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\common\\Editor.vue", "mtime": 1750583733658}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Editor.vue"], "names": [], "mappings": ";AA0BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Editor.vue", "sourceRoot": "src/components/common", "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 图片上传组件辅助-->\r\n    <el-upload\r\n      class=\"avatar-uploader\"\r\n      :action=\"getActionUrl\"\r\n      name=\"file\"\r\n      :headers=\"header\"\r\n      :show-file-list=\"false\"\r\n      :on-success=\"uploadSuccess\"\r\n      :on-error=\"uploadError\"\r\n      :before-upload=\"beforeUpload\"\r\n    ></el-upload>\r\n\r\n    <quill-editor\r\n      class=\"editor\"\r\n      v-model=\"content\"\r\n      ref=\"myQuillEditor\"\r\n      :options=\"editorOption\"\r\n      @blur=\"onEditorBlur($event)\"\r\n      @focus=\"onEditorFocus($event)\"\r\n      @change=\"onEditorChange($event)\"\r\n    ></quill-editor>\r\n  </div>\r\n</template>\r\n<script>\r\n// 工具栏配�?\nconst toolbarOptions = [\r\n  [\"bold\", \"italic\", \"underline\", \"strike\"], // 加粗 斜体 下划�?删除�?\n  [\"blockquote\", \"code-block\"], // 引用  代码�?\n  [{ header: 1 }, { header: 2 }], // 1�? 级标�?\n  [{ list: \"ordered\" }, { list: \"bullet\" }], // 有序、无序列�?\n  [{ script: \"sub\" }, { script: \"super\" }], // 上标/下标\r\n  [{ indent: \"-1\" }, { indent: \"+1\" }], // 缩进\r\n  // [{'direction': 'rtl'}],                         // 文本方向\r\n  [{ size: [\"small\", false, \"large\", \"huge\"] }], // 字体大小\r\n  [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题\r\n  [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜�?\n  [{ font: [] }], // 字体种类\r\n  [{ align: [] }], // 对齐方式\r\n  [\"clean\"], // 清除文本格式\r\n  [\"link\", \"image\", \"video\"] // 链接、图片、视�?\n];\r\n\r\nimport { quillEditor } from \"vue-quill-editor\";\r\nimport \"quill/dist/quill.core.css\";\r\nimport \"quill/dist/quill.snow.css\";\r\nimport \"quill/dist/quill.bubble.css\";\r\n\r\nexport default {\r\n  props: {\r\n    /*编辑器的内容*/\r\n    value: {\r\n      type: String\r\n    },\r\n    action: {\r\n      type: String\r\n    },\r\n    /*图片大小*/\r\n    maxSize: {\r\n      type: Number,\r\n      default: 4000 //kb\r\n    }\r\n  },\r\n\r\n  components: {\r\n    quillEditor\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      content: this.value,\r\n      quillUpdateImg: false, // 根据图片上传状态来确定是否显示loading动画，刚开始是false,不显�?\n      editorOption: {\r\n        placeholder: \"\",\r\n        theme: \"snow\", // or 'bubble'\r\n        modules: {\r\n          toolbar: {\r\n            container: toolbarOptions,\r\n            // container: \"#toolbar\",\r\n            handlers: {\r\n              image: function(value) {\r\n                if (value) {\r\n                  // 触发input框选择图片文件\r\n                  document.querySelector(\".avatar-uploader input\").click();\r\n                } else {\r\n                  this.quill.format(\"image\", false);\r\n                }\r\n              }\r\n              // link: function(value) {\r\n              //   if (value) {\r\n              //     var href = prompt('请输入url');\r\n              //     this.quill.format(\"link\", href);\r\n              //   } else {\r\n              //     this.quill.format(\"link\", false);\r\n              //   }\r\n              // },\r\n            }\r\n          }\r\n        }\r\n      },\r\n      // serverUrl: `${base.url}sys/storage/uploadSwiper?token=${storage.get('token')}`, // 这里写你要上传的图片服务器地址\r\n      header: {\r\n        // token: sessionStorage.token\r\n       'Token': this.$storage.get(\"Token\")\r\n      } // 有的图片服务器要求请求头需要有token\r\n    };\r\n  },\r\n  computed: {\r\n    // 计算属性的 getter\r\n    getActionUrl: function() {\r\n      // return this.$base.url + this.action + \"?token=\" + this.$storage.get(\"token\");\r\n      //   this.value\r\n        this.setContent(this.value);\r\n      return `/${this.$base.name}/` + this.action;\r\n    }\r\n  },\r\n  methods: {\r\n    onEditorBlur() {\r\n      //失去焦点事件\r\n    },\r\n    setContent(value) {\r\n        this.content =value;\r\n    },\r\n    onEditorFocus() {\r\n      //获得焦点事件\r\n    },\r\n    onEditorChange() {\r\n      // console.log(this.content);\r\n      // 内容改变事件\r\n      this.$emit(\"input\", this.content);\r\n    },\r\n    // 富文本图片上传前\r\n    beforeUpload() {\r\n      // 显示loading动画\r\n      this.quillUpdateImg = true;\r\n    },\r\n\r\n    uploadSuccess(res, file) {\r\n      // res为图片服务器返回的数�?\n      // 获取富文本组件实�?\n      let quill = this.$refs.myQuillEditor.quill;\r\n      // 如果上传成功\r\n      if (res.code === 0) {\r\n        // 获取光标所在位�?\n        let length = quill.getSelection().index;\r\n        // 插入图片  res.url为服务器返回的图片地址\r\n        quill.insertEmbed(length, \"image\", this.$base.url+ \"upload/\" +res.file);\r\n        // 调整光标到最�?\n        quill.setSelection(length + 1);\r\n      } else {\r\n        this.$message.error(\"图片插入失败\");\r\n      }\r\n      // loading动画消失\r\n      this.quillUpdateImg = false;\r\n    },\r\n    // 富文本图片上传失�?\n    uploadError() {\r\n      // loading动画消失\r\n      this.quillUpdateImg = false;\r\n      this.$message.error(\"图片插入失败\");\r\n    }\r\n  }\r\n};\r\n</script> \r\n\r\n<style>\r\n.editor {\r\n  line-height: normal !important;\r\n}\r\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\r\n  content: \"请输入链接地址:\";\r\n}\r\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\r\n  border-right: 0px;\r\n  content: \"保存\";\r\n  padding-right: 0px;\r\n}\r\n\r\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\r\n  content: \"请输入视频地址:\";\r\n}\r\n.ql-container {\r\n\theight: 400px;\r\n}\r\n\r\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\r\n  content: \"14px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\r\n  content: \"10px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\r\n  content: \"18px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\r\n  content: \"32px\";\r\n}\r\n\r\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\r\n  content: \"文本\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\r\n  content: \"标题1\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\r\n  content: \"标题2\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\r\n  content: \"标题3\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\r\n  content: \"标题4\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\r\n  content: \"标题5\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\r\n  content: \"标题6\";\r\n}\r\n\r\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\r\n  content: \"标准字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\r\n  content: \"衬线字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\r\n  content: \"等宽字体\";\r\n}\r\n</style>\r\n"]}]}