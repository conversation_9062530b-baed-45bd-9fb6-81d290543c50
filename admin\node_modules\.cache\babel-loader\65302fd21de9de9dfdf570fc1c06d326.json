{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\common\\BreadCrumbs.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\common\\BreadCrumbs.vue", "mtime": 1642386767434}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["pathToRegexp", "generateTitle", "data", "levelList", "watch", "$route", "getBreadcrumb", "created", "breadcrumbStyleChange", "methods", "route", "matched", "filter", "item", "meta", "first", "path", "concat", "isDashboard", "name", "trim", "toLocaleLowerCase", "pathCompile", "params", "to<PERSON><PERSON>", "compile", "handleLink", "redirect", "$router", "push", "val", "$nextTick", "document", "querySelectorAll", "for<PERSON>ach", "el", "innerText", "style", "color", "str", "headHeight", "parseInt", "marginTop"], "sources": ["src/components/common/BreadCrumbs.vue"], "sourcesContent": ["<template>\r\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"(●'◡'●)\" style=\"height:50px;backgroundColor:rgba(224, 240, 233, 1);borderRadius:0px;padding:0px 20px 0px 20px;boxShadow:4px 4px 2px#FFB3A7;borderWidth:0px;borderStyle:dotted solid double dashed;borderColor:rgba(255, 179, 167, 1);\">\r\n    <transition-group name=\"breadcrumb\" class=\"box\" :style=\"2==1?'justifyContent:flex-start;':2==2?'justifyContent:center;':'justifyContent:flex-end;'\">\r\n      <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\r\n        <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.name }}</span>\r\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.name }}</a>\r\n      </el-breadcrumb-item>\r\n    </transition-group>\r\n  </el-breadcrumb>\r\n</template>\r\n\r\n<script>\r\nimport pathToRegexp from 'path-to-regexp'\r\nimport { generateTitle } from '@/utils/i18n'\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.getBreadcrumb()\r\n    }\r\n  },\r\n  created() {\r\n    this.getBreadcrumb()\r\n    this.breadcrumbStyleChange()\r\n  },\r\n  methods: {\r\n    generateTitle,\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let route = this.$route\r\n      let matched = route.matched.filter(item => item.meta)\r\n      const first = matched[0]\r\n      matched = [{ path: '/index' }].concat(matched)\r\n\r\n      this.levelList = matched.filter(item => item.meta)\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name\r\n      if (!name) {\r\n        return false\r\n      }\r\n      return name.trim().toLocaleLowerCase() === 'Index'.toLocaleLowerCase()\r\n    },\r\n    pathCompile(path) {\r\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\r\n      const { params } = this.$route\r\n      var toPath = pathToRegexp.compile(path)\r\n      return toPath(params)\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item\r\n      if (redirect) {\r\n        this.$router.push(redirect)\r\n        return\r\n      }\r\n      this.$router.push(path)\r\n    },\r\n    breadcrumbStyleChange(val) {\r\n      this.$nextTick(()=>{\r\n        document.querySelectorAll('.app-breadcrumb .el-breadcrumb__separator').forEach(el=>{\r\n          el.innerText = \"(●'◡'●)\"\r\n          el.style.color = \"rgba(43, 115, 176, 1)\"\r\n        })\r\n        document.querySelectorAll('.app-breadcrumb .el-breadcrumb__inner a').forEach(el=>{\r\n          el.style.color = \"rgba(77, 84, 222, 1)\"\r\n        })\r\n        document.querySelectorAll('.app-breadcrumb .el-breadcrumb__inner .no-redirect').forEach(el=>{\r\n          el.style.color = \"rgba(17, 18, 18, 1)\"\r\n        })\r\n\r\n        let str = \"vertical\"\r\n        if(\"vertical\" === str) {\r\n          let headHeight = \"60px\"\r\n          headHeight = parseInt(headHeight) + 10 + 'px'\r\n          document.querySelectorAll('.app-breadcrumb').forEach(el=>{\r\n            el.style.marginTop = headHeight\r\n          })\r\n        }\r\n\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-breadcrumb {\r\n  display: block;\r\n  font-size: 14px;\r\n  line-height: 50px;\r\n\r\n  .box {\r\n    display: flex;\r\n    width: 100%;\r\n    height: 100%;\r\n    justify-content: flex-start;\r\n    align-items: center;\r\n  }\r\n\r\n  .no-redirect {\r\n    color: #97a8be;\r\n    cursor: text;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AAYA,OAAAA,YAAA;AACA,SAAAC,aAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,aAAA;IACA,KAAAE,qBAAA;EACA;EACAC,OAAA;IACAR,aAAA,EAAAA,aAAA;IACAK,aAAA,WAAAA,cAAA;MACA;MACA,IAAAI,KAAA,QAAAL,MAAA;MACA,IAAAM,OAAA,GAAAD,KAAA,CAAAC,OAAA,CAAAC,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA;MAAA;MACA,IAAAC,KAAA,GAAAJ,OAAA;MACAA,OAAA;QAAAK,IAAA;MAAA,GAAAC,MAAA,CAAAN,OAAA;MAEA,KAAAR,SAAA,GAAAQ,OAAA,CAAAC,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA;MAAA;IACA;IACAI,WAAA,WAAAA,YAAAR,KAAA;MACA,IAAAS,IAAA,GAAAT,KAAA,IAAAA,KAAA,CAAAS,IAAA;MACA,KAAAA,IAAA;QACA;MACA;MACA,OAAAA,IAAA,CAAAC,IAAA,GAAAC,iBAAA,eAAAA,iBAAA;IACA;IACAC,WAAA,WAAAA,YAAAN,IAAA;MACA;MACA,IAAAO,MAAA,QAAAlB,MAAA,CAAAkB,MAAA;MACA,IAAAC,MAAA,GAAAxB,YAAA,CAAAyB,OAAA,CAAAT,IAAA;MACA,OAAAQ,MAAA,CAAAD,MAAA;IACA;IACAG,UAAA,WAAAA,WAAAb,IAAA;MACA,IAAAc,QAAA,GAAAd,IAAA,CAAAc,QAAA;QAAAX,IAAA,GAAAH,IAAA,CAAAG,IAAA;MACA,IAAAW,QAAA;QACA,KAAAC,OAAA,CAAAC,IAAA,CAAAF,QAAA;QACA;MACA;MACA,KAAAC,OAAA,CAAAC,IAAA,CAAAb,IAAA;IACA;IACAR,qBAAA,WAAAA,sBAAAsB,GAAA;MACA,KAAAC,SAAA;QACAC,QAAA,CAAAC,gBAAA,8CAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAC,SAAA;UACAD,EAAA,CAAAE,KAAA,CAAAC,KAAA;QACA;QACAN,QAAA,CAAAC,gBAAA,4CAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAE,KAAA,CAAAC,KAAA;QACA;QACAN,QAAA,CAAAC,gBAAA,uDAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAE,KAAA,CAAAC,KAAA;QACA;QAEA,IAAAC,GAAA;QACA,mBAAAA,GAAA;UACA,IAAAC,UAAA;UACAA,UAAA,GAAAC,QAAA,CAAAD,UAAA;UACAR,QAAA,CAAAC,gBAAA,oBAAAC,OAAA,WAAAC,EAAA;YACAA,EAAA,CAAAE,KAAA,CAAAK,SAAA,GAAAF,UAAA;UACA;QACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}