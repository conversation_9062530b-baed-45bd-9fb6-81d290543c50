{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\dictionary\\list.vue?vue&type=style&index=0&id=4df75745&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\dictionary\\list.vue", "mtime": 1642386766982}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouc2x0IHsNCiAgICBtYXJnaW46IDAgIWltcG9ydGFudDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICB9DQoNCiAgLmFkIHsNCiAgICBtYXJnaW46IDAgIWltcG9ydGFudDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICB9DQoNCiAgLnBhZ2VzIHsNCiAgICAmIC9kZWVwLyBlbC1wYWdpbmF0aW9uX19zaXplc3sNCiAgICAgICYgL2RlZXAvIGVsLWlucHV0X19pbm5lciB7DQogICAgICAgIGhlaWdodDogMjJweDsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDIycHg7DQogICAgICB9DQogICAgfQ0KICB9DQoNCg0KICAuZWwtYnV0dG9uKy5lbC1idXR0b24gew0KICAgIG1hcmdpbjowOw0KICB9DQoNCiAgLnRhYmxlcyB7DQoJJiAvZGVlcC8gLmVsLWJ1dHRvbi0tc3VjY2VzcyB7DQoJCWhlaWdodDogNDBweDsNCgkJY29sb3I6ICMzMzM7DQoJCWZvbnQtc2l6ZTogMTRweDsNCgkJYm9yZGVyLXdpZHRoOiAxcHg7DQoJCWJvcmRlci1zdHlsZTogc29saWQ7DQoJCWJvcmRlci1jb2xvcjogI0RDREZFNjsNCgkJYm9yZGVyLXJhZGl1czogNnB4Ow0KCQliYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDEyMSwgMjQ0LCAxNjQsIDAuNTQpOw0KCX0NCg0KCSYgL2RlZXAvIC5lbC1idXR0b24tLXByaW1hcnkgew0KCQloZWlnaHQ6IDQwcHg7DQoJCWNvbG9yOiAjMzMzOw0KCQlmb250LXNpemU6IDE0cHg7DQoJCWJvcmRlci13aWR0aDogMXB4Ow0KCQlib3JkZXItc3R5bGU6IHNvbGlkOw0KCQlib3JkZXItY29sb3I6ICNEQ0RGRTY7DQoJCWJvcmRlci1yYWRpdXM6IDZweDsNCgkJYmFja2dyb3VuZC1jb2xvcjogcmdiYSgxNjksIDExNSwgMjMyLCAwLjY1KTsNCgl9DQoNCgkmIC9kZWVwLyAuZWwtYnV0dG9uLS1kYW5nZXIgew0KCQloZWlnaHQ6IDQwcHg7DQoJCWNvbG9yOiAjMzMzOw0KCQlmb250LXNpemU6IDE0cHg7DQoJCWJvcmRlci13aWR0aDogMXB4Ow0KCQlib3JkZXItc3R5bGU6IHNvbGlkOw0KCQlib3JkZXItY29sb3I6ICNEQ0RGRTY7DQoJCWJvcmRlci1yYWRpdXM6IDZweDsNCgkJYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNDEsIDEyMCwgMTg2LCAxKTsNCgl9DQoNCiAgICAmIC9kZWVwLyAuZWwtYnV0dG9uIHsNCiAgICAgIG1hcmdpbjogNHB4Ow0KICAgIH0NCiAgfQ0K"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";AAgxBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/modules/dictionary", "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"main-content\">\r\n\r\n        <!-- 条件查询 -->\r\n        <div v-if=\"showFlag\">\r\n            <el-form :inline=\"true\" :model=\"searchForm\" class=\"form-content\">\r\n                <el-row :gutter=\"20\" class=\"slt\" :style=\"{justifyContent:contents.searchBoxPosition=='1'?'flex-start':contents.searchBoxPosition=='2'?'center':'flex-end'}\">\r\n                                         \r\n                     <el-form-item :label=\"contents.inputTitle == 1 ? '编码名字' : ''\">\r\n                         <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.indexName\" placeholder=\"编码名字\" clearable></el-input>\r\n                     </el-form-item>\r\n                        \r\n\r\n\r\n                    <el-form-item>\r\n                        <el-button type=\"success\" @click=\"search()\">查询<i class=\"el-icon-search el-icon--right\"/></el-button>\r\n                    </el-form-item>\r\n                </el-row>\r\n                <el-row class=\"ad\" :style=\"{justifyContent:contents.btnAdAllBoxPosition=='1'?'flex-start':contents.btnAdAllBoxPosition=='2'?'center':'flex-end'}\">\r\n                    <el-form-item>\r\n                        <el-button\r\n                                v-if=\"isAuth('dictionary','新增')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-plus\"\r\n                                @click=\"addOrUpdateHandler()\"\r\n                        >新增</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('dictionary','删除')\"\r\n                                :disabled=\"dataListSelections.length <= 0\"\r\n                                type=\"danger\"\r\n                                icon=\"el-icon-delete\"\r\n                                @click=\"deleteHandler()\"\r\n                        >删除</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('dictionary','报表')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-pie-chart\"\r\n                                @click=\"chartDialog()\"\r\n                        >报表</el-button>\r\n                        &nbsp;\r\n                        <a style=\"text-decoration:none\" class=\"el-button el-button--success\"\r\n                           v-if=\"isAuth('dictionary','导入导出')\"\r\n                           icon=\"el-icon-download\"\r\n                           href=\"http://localhost:8080/tiyuguan/upload/dictionaryMuBan.xls\"\r\n                        >批量导入字典数据模板</a>\r\n                        &nbsp;\r\n                        <el-upload\r\n                                v-if=\"isAuth('dictionary','导入导出')\"\r\n                                style=\"display: inline-block\"\r\n                                action=\"tiyuguan/file/upload\"\r\n                                :on-success=\"dictionaryUploadSuccess\"\r\n                                :on-error=\"dictionaryUploadError\"\r\n                                :show-file-list = false>\r\n                            <el-button\r\n                                    v-if=\"isAuth('dictionary','导入导出')\"\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-upload2\"\r\n                            >批量导入字典数据</el-button>\r\n                        </el-upload>\r\n                        &nbsp;\r\n                        <!-- 导出excel -->\r\n                        <download-excel v-if=\"isAuth('dictionary','导入导出')\" style=\"display: inline-block\" class = \"export-excel-wrapper\" :data = \"dataList\" :fields = \"json_fields\" name = \"dictionary.xls\">\r\n                            <!-- 导出excel -->\r\n                            <el-button\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-download\"\r\n                            >导出</el-button>\r\n                        </download-excel>\r\n                        &nbsp;\r\n                    </el-form-item>\r\n                </el-row>\r\n            </el-form>\r\n            <div class=\"table-content\">\r\n                <el-table class=\"tables\" :size=\"contents.tableSize\" :show-header=\"contents.tableShowHeader\"\r\n                          :header-row-style=\"headerRowStyle\" :header-cell-style=\"headerCellStyle\"\r\n                          :border=\"contents.tableBorder\"\r\n                          :fit=\"contents.tableFit\"\r\n                          :stripe=\"contents.tableStripe\"\r\n                          :row-style=\"rowStyle\"\r\n                          :cell-style=\"cellStyle\"\r\n                          :style=\"{width: '100%',fontSize:contents.tableContentFontSize,color:contents.tableContentFontColor}\"\r\n                          v-if=\"isAuth('dictionary','查看')\"\r\n                          :data=\"dataList\"\r\n                          v-loading=\"dataListLoading\"\r\n                          @selection-change=\"selectionChangeHandler\">\r\n                    <el-table-column  v-if=\"contents.tableSelection\"\r\n                                      type=\"selection\"\r\n                                      header-align=\"center\"\r\n                                      align=\"center\"\r\n                                      width=\"50\">\r\n                    </el-table-column>\r\n                    <el-table-column label=\"索引\" v-if=\"contents.tableIndex\" type=\"index\" width=\"50\" />\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"dicCode\"\r\n                                   header-align=\"center\"\r\n                                   label=\"字段\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.dicCode}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"dicName\"\r\n                                   header-align=\"center\"\r\n                                   label=\"字段名\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.dicName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"codeIndex\"\r\n                                      header-align=\"center\"\r\n                                      label=\"编码\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.codeIndex}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"indexName\"\r\n                                   header-align=\"center\"\r\n                                   label=\"编码名字\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.indexName}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"beizhu\"\r\n                                   header-align=\"center\"\r\n                                   label=\"备注\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.beizhu}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column width=\"300\" :align=\"contents.tableAlign\"\r\n                                     header-align=\"center\"\r\n                                     label=\"操作\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button v-if=\"isAuth('dictionary','查看')\" type=\"success\" icon=\"el-icon-tickets\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id,'info')\">详情</el-button>\r\n                            <el-button v-if=\"isAuth('dictionary','修改')\" type=\"primary\" icon=\"el-icon-edit\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id)\">修改</el-button>\r\n                            <el-button v-if=\"isAuth('dictionary','删除')\" type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteHandler(scope.row.id)\">删除</el-button>\r\n\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n                <el-pagination\r\n                        clsss=\"pages\"\r\n                        :layout=\"layouts\"\r\n                        @size-change=\"sizeChangeHandle\"\r\n                        @current-change=\"currentChangeHandle\"\r\n                        :current-page=\"pageIndex\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"Number(contents.pageEachNum)\"\r\n                        :total=\"totalPage\"\r\n                        :small=\"contents.pageStyle\"\r\n                        class=\"pagination-content\"\r\n                        :background=\"contents.pageBtnBG\"\r\n                        :style=\"{textAlign:contents.pagePosition==1?'left':contents.pagePosition==2?'center':'right'}\"\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <!-- 添加/修改页面  将父组件的search方法传递给子组件-->\r\n        <add-or-update v-if=\"addOrUpdateFlag\" :parent=\"this\" ref=\"addOrUpdate\"></add-or-update>\r\n\r\n\r\n\r\n        <el-dialog title=\"统计报表\" :visible.sync=\"chartVisiable\" width=\"800\">\r\n            <el-date-picker\r\n                    v-model=\"echartsDate\"\r\n                    type=\"year\"\r\n                    placeholder=\"选择年\">\r\n            </el-date-picker>\r\n            <el-button @click=\"chartDialog()\">查询</el-button>\r\n            <div id=\"statistic\" style=\"width:100%;height:600px;\"></div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"chartVisiable = false\">返回</el-button>\r\n\t\t\t</span>\r\n        </el-dialog>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import AddOrUpdate from \"./add-or-update\";\r\n    import styleJs from \"../../../utils/style.js\";\r\n    export default {\r\n        data() {\r\n        return {\r\n            searchForm: {\r\n                key: \"\"\r\n            },\r\n            sessionTable : \"\",//登录账户所在表名\r\n            role : \"\",//权限\r\n    //级联表下拉框搜索条件\r\n    //当前表下拉框搜索条件\r\n            form:{\r\n                id : null,\r\n                dicCode : null,\r\n                dicName : null,\r\n                codeIndex : null,\r\n                indexName : null,\r\n                superId : null,\r\n                beizhu : null,\r\n                createTime : null,\r\n            },\r\n            dataList: [],\r\n            pageIndex: 1,\r\n            pageSize: 10,\r\n            totalPage: 0,\r\n            dataListLoading: false,\r\n            dataListSelections: [],\r\n            showFlag: true,\r\n            sfshVisiable: false,\r\n            shForm: {},\r\n            chartVisiable: false,\r\n            addOrUpdateFlag:false,\r\n            contents:null,\r\n            layouts: '',\r\n\r\n            echartsDate: new Date(),//echarts的时间查询字段\r\n\r\n            //导出excel\r\n            json_fields: {\r\n                //级联表字段\r\n                //本表字段\r\n                     '字段': \"dicCode\",\r\n                     '字段名': \"dicName\",\r\n                     '编码': \"codeIndex\",\r\n                     '编码名字': \"indexName\",\r\n                     '备注': \"beizhu\",\r\n            },\r\n\r\n            };\r\n        },\r\n        created() {\r\n            this.contents = styleJs.listStyle();\r\n            this.init();\r\n            this.getDataList();\r\n            this.contentStyleChange()\r\n        },\r\n        mounted() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n        },\r\n        filters: {\r\n            htmlfilter: function (val) {\r\n                return val.replace(/<[^>]*>/g).replace(/undefined/g,'');\r\n            }\r\n        },\r\n        components: {\r\n            AddOrUpdate,\r\n        },\r\n        computed: {\r\n        },\r\n        methods: {\r\n            chartDialog() {\r\n                let _this = this;\r\n                let params = {\r\n                    dateFormat :\"%Y\"\r\n                    ,riqi :_this.echartsDate.getFullYear()\r\n                    ,thisTable : {//当前表\r\n                        tableName :\"shangdian_shouyin\"//当前表表名\r\n                        ,sumColum : 'shangdian_shouyin_true_price' //求和字段\r\n                        ,date : 'insert_time'//分组日期字段\r\n                        // ,string : 'name,leixing'//分组字符串字段\r\n                        // ,types : 'shangdian_shouyin_types'//分组下拉框字段\r\n                    }\r\n                    // ,joinTable : {//级联表（可以不存在）\r\n                    //     tableName :\"yuangong\"//级联表表名\r\n                    //     // ,date : 'insert_time'//分组日期字段\r\n                    //     ,string : 'yuangong_name'//分组字符串字段\r\n                    //     // ,types : 'insertTime'//分组下拉框字段\r\n                    // }\r\n                }\r\n                _this.chartVisiable = true;\r\n                _this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"barSum\",\r\n                        method: \"get\",\r\n                        params: params\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n\r\n\r\n                            //柱状图 求和 已成功使用\r\n                            //start\r\n                            let series = [];//具体数据值\r\n                            data.data.yAxis.forEach(function (item,index) {\r\n                                let tempMap = {};\r\n                                tempMap.name=data.data.legend[index];\r\n                                tempMap.type='bar';\r\n                                tempMap.data=item;\r\n                                series.push(tempMap);\r\n\r\n                            })\r\n\r\n                            var option = {\r\n                                tooltip: {\r\n                                    trigger: 'axis',\r\n                                    axisPointer: {\r\n                                        type: 'cross',\r\n                                        crossStyle: {\r\n                                            color: '#999'\r\n                                        }\r\n                                    }\r\n                                },\r\n                                toolbox: {\r\n                                    feature: {\r\n                                        // dataView: { show: true, readOnly: false },  // 数据查看\r\n                                        magicType: { show: true, type: ['line', 'bar'] },//切换图形展示方式\r\n                                        // restore: { show: true }, // 刷新\r\n                                        saveAsImage: { show: true }//保存\r\n                                    }\r\n                                },\r\n                                legend: {\r\n                                    data: data.data.legend//标题  可以点击导致某一列数据消失\r\n                                },\r\n                                xAxis: [\r\n                                    {\r\n                                        type: 'category',\r\n                                        name: '月份',\r\n                                        data: data.data.xAxis,\r\n                                        axisPointer: {\r\n                                            type: 'shadow'\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                yAxis: [\r\n                                    {\r\n                                        type: 'value',//不能改\r\n                                        name: '元',//y轴单位\r\n                                        axisLabel: {\r\n                                            formatter: '{value} 元' // 后缀\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                series:series//具体数据\r\n                            };\r\n                            // 使用刚指定的配置项和数据显示图表。\r\n                            statistic.setOption(option);\r\n                            //根据窗口的大小变动图表\r\n                            window.onresize = function () {\r\n                                statistic.resize();\r\n                            };\r\n                            //end\r\n\r\n\r\n\r\n                            //饼状图 原先自带的 未修改过\r\n                            //start\r\n                            /*let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表。\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图表\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }*/\r\n\r\n                            //end\r\n                        }else {\r\n                            this.$message({\r\n                                message: \"报表未查询到数据\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n                this.chartVisiable = !this.chartVisiable;\r\n                this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"group/xinzitongji/xinzi\",\r\n                        method: \"get\",\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表。\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图表\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }\r\n                        }\r\n                    });\r\n                // xcolumn ycolumn\r\n                });\r\n            },\r\n            contentStyleChange() {\r\n                this.contentSearchStyleChange()\r\n                this.contentBtnAdAllStyleChange()\r\n                this.contentSearchBtnStyleChange()\r\n                this.contentTableBtnStyleChange()\r\n                this.contentPageStyleChange()\r\n            },\r\n            contentSearchStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-input__inner').forEach(el => {\r\n                        let textAlign = 'left'\r\n                        if(this.contents.inputFontPosition == 2)\r\n                            textAlign = 'center'\r\n                            if (this.contents.inputFontPosition == 3) textAlign = 'right'\r\n                                el.style.textAlign = textAlign\r\n                            el.style.height = this.contents.inputHeight\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                            el.style.color = this.contents.inputFontColor\r\n                            el.style.fontSize = this.contents.inputFontSize\r\n                            el.style.borderWidth = this.contents.inputBorderWidth\r\n                            el.style.borderStyle = this.contents.inputBorderStyle\r\n                            el.style.borderColor = this.contents.inputBorderColor\r\n                            el.style.borderRadius = this.contents.inputBorderRadius\r\n                            el.style.backgroundColor = this.contents.inputBgColor\r\n                    })\r\n                    if (this.contents.inputTitle) {\r\n                        document.querySelectorAll('.form-content .slt .el-form-item__label').forEach(el => {\r\n                            el.style.color = this.contents.inputTitleColor\r\n                            el.style.fontSize = this.contents.inputTitleSize\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }\r\n                    setTimeout(() => {\r\n                        document.querySelectorAll('.form-content .slt .el-input__prefix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__suffix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__icon').forEach(el => {\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }, 10 )\r\n                })\r\n            },\r\n            // 搜索按钮\r\n            contentSearchBtnStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.searchBtnHeight\r\n                        el.style.color = this.contents.searchBtnFontColor\r\n                        el.style.fontSize = this.contents.searchBtnFontSize\r\n                        el.style.borderWidth = this.contents.searchBtnBorderWidth\r\n                        el.style.borderStyle = this.contents.searchBtnBorderStyle\r\n                        el.style.borderColor = this.contents.searchBtnBorderColor\r\n                        el.style.borderRadius = this.contents.searchBtnBorderRadius\r\n                        el.style.backgroundColor = this.contents.searchBtnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 新增、批量删除\r\n            contentBtnAdAllStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .ad .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllAddFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllAddBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--danger').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllDelFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllDelBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--warning').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllWarnFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllWarnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 表格\r\n            rowStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {color: this.contents.tableStripeFontColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            cellStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {backgroundColor: this.contents.tableStripeBgColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            headerRowStyle({row, rowIndex}) {\r\n                return {color: this.contents.tableHeaderFontColor}\r\n            },\r\n            headerCellStyle({row, rowIndex}) {\r\n                return {backgroundColor: this.contents.tableHeaderBgColor}\r\n            },\r\n            // 表格按钮\r\n            contentTableBtnStyleChange() {\r\n                // this.$nextTick(()=>{\r\n                //   setTimeout(()=>{\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--success').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDetailFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDetailBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--primary').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnEditFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnEditBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--danger').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDelFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDelBgColor\r\n                //     })\r\n\r\n                //   }, 50)\r\n                // })\r\n            },\r\n            // 分页\r\n            contentPageStyleChange() {\r\n                let arr = []\r\n                if (this.contents.pageTotal) arr.push('total')\r\n                if (this.contents.pageSizes) arr.push('sizes')\r\n                if (this.contents.pagePrevNext) {\r\n                    arr.push('prev')\r\n                    if (this.contents.pagePager) arr.push('pager')\r\n                    arr.push('next')\r\n                }\r\n                if (this.contents.pageJumper) arr.push('jumper')\r\n                this.layouts = arr.join()\r\n                this.contents.pageEachNum = 10\r\n            },\r\n\r\n            init() {\r\n            },\r\n            search() {\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 获取数据列表\r\n            getDataList() {\r\n                this.dataListLoading = true;\r\n                let params = {\r\n                    page: this.pageIndex,\r\n                    limit: this.pageSize,\r\n                    sort: 'id',\r\n                }\r\n\r\n                                         \r\n                if (this.searchForm.indexName!= '' && this.searchForm.indexName!= undefined) {\r\n                    params['indexName'] = '%' + this.searchForm.indexName + '%'\r\n                }\r\n                        \r\n                params['dictionaryDelete'] = 1// 逻辑删除字段 1 未删除 2 删除\r\n\r\n\r\n                this.$http({\r\n                    url: \"dictionary/page\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.dataList = data.data.list;\r\n                        this.totalPage = data.data.total;\r\n                    }else{\r\n                        this.dataList = [];\r\n                        this.totalPage = 0;\r\n                    }\r\n                    this.dataListLoading = false;\r\n                });\r\n\r\n                //查询级联表搜索条件所有列表\r\n                //查询当前表搜索条件所有列表\r\n            },\r\n            //每页数\r\n            sizeChangeHandle(val) {\r\n                this.pageSize = val;\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 当前页\r\n            currentChangeHandle(val) {\r\n                this.pageIndex = val;\r\n                this.getDataList();\r\n            },\r\n            // 多选\r\n            selectionChangeHandler(val) {\r\n                this.dataListSelections = val;\r\n            },\r\n            // 添加/修改\r\n            addOrUpdateHandler(id, type) {\r\n                this.showFlag = false;\r\n                this.addOrUpdateFlag = true;\r\n                this.crossAddOrUpdateFlag = false;\r\n                if (type != 'info') {\r\n                    type = 'else';\r\n                }\r\n                this.$nextTick(() => {\r\n                    this.$refs.addOrUpdate.init(id, type);\r\n                });\r\n            },\r\n            // 下载\r\n            download(file) {\r\n                window.open(\" ${file} \")\r\n            },\r\n            // 删除\r\n            deleteHandler(id) {\r\n                var ids = id ? [Number(id)] : this.dataListSelections.map(item => {\r\n                    return Number(item.id);\r\n                });\r\n\r\n                this.$confirm(`确定进行[${id ? \"删除\" : \"批量删除\"}]操作?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: \"dictionary/delete\",\r\n                        method: \"post\",\r\n                        data: ids\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            this.$message({\r\n                                message: \"操作成功\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }else{\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n            // 导入功能上传文件成功后调用导入方法\r\n            dictionaryUploadSuccess(data){\r\n                let _this = this;\r\n                _this.$http({\r\n                    url: \"dictionary/batchInsert?fileName=\" + data.file,\r\n                    method: \"get\"\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        _this.$message({\r\n                            message: \"导入字典数据成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.search();\r\n                            }\r\n                        });\r\n                    }else{\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n\r\n            },\r\n            // 导入功能上传文件失败后调用导入方法\r\n            dictionaryUploadError(data){\r\n                this.$message.error('上传失败');\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.slt {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .ad {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .pages {\r\n    & /deep/ el-pagination__sizes{\r\n      & /deep/ el-input__inner {\r\n        height: 22px;\r\n        line-height: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .el-button+.el-button {\r\n    margin:0;\r\n  }\r\n\r\n  .tables {\r\n\t& /deep/ .el-button--success {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(121, 244, 164, 0.54);\r\n\t}\r\n\r\n\t& /deep/ .el-button--primary {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(169, 115, 232, 0.65);\r\n\t}\r\n\r\n\t& /deep/ .el-button--danger {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(241, 120, 186, 1);\r\n\t}\r\n\r\n    & /deep/ .el-button {\r\n      margin: 4px;\r\n    }\r\n  }\r\n</style>\r\n\r\n\r\n"]}]}