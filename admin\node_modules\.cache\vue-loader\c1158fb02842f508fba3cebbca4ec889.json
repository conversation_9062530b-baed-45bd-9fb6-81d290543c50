{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\register.vue", "mtime": 1750584224769}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";AAqCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"container\">\r\n            <div class=\"login-form\" style=\"backgroundColor:rgba(183, 174, 174, 0.5);borderRadius:22px\">\r\n                <h1 class=\"h1\" style=\"color:#000;fontSize:28px;\">体育馆使用预约平台注�?/h1>\r\n                <el-form ref=\"rgsForm\" class=\"rgs-form\" :model=\"rgsForm\" label-width=\"120px\">\r\n                        <el-form-item label=\"账号\" class=\"input\">\r\n                            <el-input v-model=\"ruleForm.username\" autocomplete=\"off\" placeholder=\"账号\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"密码\" class=\"input\">\r\n                            <el-input type=\"password\" v-model=\"ruleForm.password\" autocomplete=\"off\" show-password/>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"重复密码\" class=\"input\">\r\n                            <el-input type=\"password\" v-model=\"ruleForm.repetitionPassword\" autocomplete=\"off\" show-password/>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"用户姓名\" class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuName\" autocomplete=\"off\" placeholder=\"用户姓名\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"用户手机�? class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuPhone\" autocomplete=\"off\" placeholder=\"用户手机�?  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"用户身份证号\" class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuIdNumber\" autocomplete=\"off\" placeholder=\"用户身份证号\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"电子邮箱\" class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuEmail\" autocomplete=\"off\" placeholder=\"电子邮箱\"  />\r\n                        </el-form-item>\r\n                        <div style=\"display: flex;flex-wrap: wrap;width: 100%;justify-content: center;\">\r\n                            <el-button class=\"btn\" type=\"primary\" @click=\"login()\">注册</el-button>\r\n                            <el-button class=\"btn close\" type=\"primary\" @click=\"close()\">取消</el-button>\r\n                        </div>\r\n                </el-form>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    export default {\r\n        data() {\r\n            return {\r\n                ruleForm: {\r\n                },\r\n                tableName:\"\",\r\n                rules: {},\r\n                sexTypesOptions : [],\r\n            };\r\n        },\r\n        mounted(){\r\n            let table = this.$storage.get(\"loginTable\");\r\n            this.tableName = table;\r\n        },\r\n        methods: {\r\n            // 获取uuid\r\n            getUUID () {\r\n                return new Date().getTime();\r\n            },\r\n            close(){\r\n                this.$router.push({ path: \"/login\" });\r\n            },\r\n            // 注册\r\n            login() {\r\n\r\n                            if((!this.ruleForm.username)){\r\n                                this.$message.error('账号不能为空');\r\n                                return\r\n                            }\r\n                            if((!this.ruleForm.password)){\r\n                                this.$message.error('密码不能为空');\r\n                                return\r\n                            }\r\n                            if((!this.ruleForm.repetitionPassword)){\r\n                                this.$message.error('重复密码不能为空');\r\n                                return\r\n                            }\r\n                            if(this.ruleForm.repetitionPassword != this.ruleForm.password){\r\n                                this.$message.error('密码和重复密码不一致');\r\n                                return\r\n                            }\r\n                            if((!this.ruleForm.yonghuName)&& 'yonghu'==this.tableName){\r\n                                this.$message.error('用户姓名不能为空');\r\n                                return\r\n                            }\r\n                             if('yonghu' == this.tableName && this.ruleForm.yonghuPhone&&(!this.$validate.isMobile(this.ruleForm.yonghuPhone))){\r\n                                 this.$message.error('手机应输入手机格式');\r\n                                 return\r\n                             }\r\n                            if((!this.ruleForm.yonghuIdNumber)&& 'yonghu'==this.tableName){\r\n                                this.$message.error('用户身份证号不能为空');\r\n                                return\r\n                            }\r\n                            if('yonghu' == this.tableName && this.ruleForm.yonghuEmail&&(!this.$validate.isEmail(this.ruleForm.yonghuEmail))){\r\n                                this.$message.error(\"邮箱应输入邮件格式\");\r\n                                return\r\n                            }\r\n                this.$http({\r\n                    url: `${this.tableName}/register`,\r\n                    method: \"post\",\r\n                    data:this.ruleForm\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                    this.$message({\r\n                        message: \"注册成功,请登录后在个人中心页面补充个人数据\",\r\n                        type: \"success\",\r\n                        duration: 1500,\r\n                        onClose: () => {\r\n                        this.$router.replace({ path: \"/login\" });\r\n                }\r\n                });\r\n                } else {\r\n                    this.$message.error(data.msg);\r\n                }\r\n            });\r\n            }\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n    .el-radio__input.is-checked .el-radio__inner {\r\n        border-color: #00c292;\r\n        background: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked .el-radio__inner {\r\n        border-color: #00c292;\r\n        background: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked .el-radio__inner {\r\n        border-color: #00c292;\r\n        background: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked+.el-radio__label {\r\n        color: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked+.el-radio__label {\r\n        color: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked+.el-radio__label {\r\n        color: #00c292;\r\n    }\r\n\r\n    .h1 {\r\n        margin-top: 10px;\r\n    }\r\n\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n\r\n    // .container {\r\n       //    min-height: 100vh;\r\n       //    text-align: center;\r\n       //    // background-color: #00c292;\r\n       //    padding-top: 20vh;\r\n       //    background-image: url(../assets/img/bg.jpg);\r\n       //    background-size: 100% 100%;\r\n       //    opacity: 0.9;\r\n       //  }\r\n\r\n    // .login-form:before {\r\n       // \tvertical-align: middle;\r\n       // \tdisplay: inline-block;\r\n       // }\r\n\r\n    // .login-form {\r\n       // \tmax-width: 500px;\r\n       // \tpadding: 20px 0;\r\n       // \twidth: 80%;\r\n       // \tposition: relative;\r\n       // \tmargin: 0 auto;\r\n\r\n    // \t.label {\r\n          // \t\tmin-width: 60px;\r\n          // \t}\r\n\r\n    // \t.input-group {\r\n          // \t\tmax-width: 500px;\r\n          // \t\tpadding: 20px 0;\r\n          // \t\twidth: 80%;\r\n          // \t\tposition: relative;\r\n          // \t\tmargin: 0 auto;\r\n          // \t\tdisplay: flex;\r\n          // \t\talign-items: center;\r\n\r\n    // \t\t.input-container {\r\n              // \t\t\tdisplay: inline-block;\r\n              // \t\t\twidth: 100%;\r\n              // \t\t\ttext-align: left;\r\n              // \t\t\tmargin-left: 10px;\r\n              // \t\t}\r\n\r\n    // \t\t.icon {\r\n              // \t\t\twidth: 30px;\r\n              // \t\t\theight: 30px;\r\n              // \t\t}\r\n\r\n    // \t\t.input {\r\n              // \t\t\tposition: relative;\r\n              // \t\t\tz-index: 2;\r\n              // \t\t\tfloat: left;\r\n              // \t\t\twidth: 100%;\r\n              // \t\t\tmargin-bottom: 0;\r\n              // \t\t\tbox-shadow: none;\r\n              // \t\t\tborder-top: 0px solid #ccc;\r\n              // \t\t\tborder-left: 0px solid #ccc;\r\n              // \t\t\tborder-right: 0px solid #ccc;\r\n              // \t\t\tborder-bottom: 1px solid #ccc;\r\n              // \t\t\tpadding: 0px;\r\n              // \t\t\tresize: none;\r\n              // \t\t\tborder-radius: 0px;\r\n              // \t\t\tdisplay: block;\r\n              // \t\t\twidth: 100%;\r\n              // \t\t\theight: 34px;\r\n              // \t\t\tpadding: 6px 12px;\r\n              // \t\t\tfont-size: 14px;\r\n              // \t\t\tline-height: 1.42857143;\r\n              // \t\t\tcolor: #555;\r\n              // \t\t\tbackground-color: #fff;\r\n              // \t\t}\r\n\r\n    // \t}\r\n    // }\r\n\r\n    .nk-navigation {\r\n        margin-top: 15px;\r\n\r\n    a {\r\n        display: inline-block;\r\n        color: #fff;\r\n        background: rgba(255, 255, 255, .2);\r\n        width: 100px;\r\n        height: 50px;\r\n        border-radius: 30px;\r\n        text-align: center;\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        justify-content: center;\r\n        padding: 0 20px;\r\n    }\r\n\r\n    .icon {\r\n        margin-left: 10px;\r\n        width: 30px;\r\n        height: 30px;\r\n    }\r\n    }\r\n\r\n    .register-container {\r\n        margin-top: 10px;\r\n\r\n    a {\r\n        display: inline-block;\r\n        color: #fff;\r\n        max-width: 500px;\r\n        height: 50px;\r\n        border-radius: 30px;\r\n        text-align: center;\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        justify-content: center;\r\n        padding: 0 20px;\r\n\r\n    div {\r\n        margin-left: 10px;\r\n    }\r\n    }\r\n    }\r\n\r\n    .container {\r\n        background-image: url(\"/tiyuguan/img/back-img-bg.jpg\");\r\n        height: 100vh;\r\n        background-position: center center;\r\n        background-size: cover;\r\n        background-repeat: no-repeat;\r\n\r\n    .login-form {\r\n        right: 50%;\r\n        top: 50%;\r\n        height: auto;\r\n        transform: translate3d(50%, -50%, 0);\r\n        border-radius: 10px;\r\n        background-color: rgba(255,255,255,.5);\r\n        width: 420px;\r\n        padding: 30px 30px 40px 30px;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n\r\n    .h1 {\r\n        margin: 0;\r\n        text-align: center;\r\n        line-height: 54px;\r\n        font-size: 24px;\r\n        color: #000;\r\n    }\r\n\r\n    .rgs-form {\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        align-items: center;\r\n\r\n    .input {\r\n        width: 100%;\r\n\r\n    & ::v-deep .el-form-item__label {\r\n          line-height: 40px;\r\n          color: rgba(17, 16, 16, 1);\r\n          font-size: #606266;\r\n      }\r\n\r\n    & ::v-deep .el-input__inner {\r\n          height: 40px;\r\n          color: rgba(23, 24, 26, 1);\r\n          font-size: 14px;\r\n          border-width: 1px;\r\n          border-style: solid;\r\n          border-color: #606266;\r\n          border-radius: 22px;\r\n          background-color: #fff;\r\n      }\r\n    }\r\n\r\n    .btn {\r\n        margin: 0 10px;\r\n        width: 88px;\r\n        height: 44px;\r\n        color: #fff;\r\n        font-size: 14px;\r\n        border-width: 1px;\r\n        border-style: solid;\r\n        border-color: #409EFF;\r\n        border-radius: 22px;\r\n        background-color: #409EFF;\r\n    }\r\n\r\n    .close {\r\n        margin: 0 10px;\r\n        width: 88px;\r\n        height: 44px;\r\n        color: #409EFF;\r\n        font-size: 14px;\r\n        border-width: 1px;\r\n        border-style: solid;\r\n        border-color: #409EFF;\r\n        border-radius: 22px;\r\n        background-color: #FFF;\r\n    }\r\n\r\n    }\r\n    }\r\n    }\r\n</style>\r\n\r\n"]}]}