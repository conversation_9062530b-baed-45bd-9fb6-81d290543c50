{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexAsideStatic.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexAsideStatic.vue", "mtime": 1642386767436}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["menu", "data", "menuList", "dynamicMenuRoutes", "role", "icons", "menulistStyle", "menulistBorderBottom", "mounted", "menus", "list", "$storage", "get", "created", "_this", "setTimeout", "menulistStyleChange", "sort", "Math", "random", "lineBorder", "methods", "style", "w", "s", "c", "borderBottomWidth", "borderBottomStyle", "borderBottomColor", "borderRightWidth", "borderRightStyle", "borderRightColor", "menu<PERSON><PERSON><PERSON>", "name", "router", "$router", "push", "catch", "err", "setMenulistHoverColor", "that", "$nextTick", "document", "querySelectorAll", "for<PERSON>ach", "el", "addEventListener", "e", "stopPropagation", "backgroundColor", "setMenulistIconColor", "color", "setMenulistStyleHeightChange", "str", "display", "paddingTop", "width", "height", "lineHeight"], "sources": ["src/components/index/IndexAsideStatic.vue"], "sourcesContent": ["<template>\r\n  <el-aside class=\"index-aside\" height=\"100vh\" width=\"250px\">\r\n    <div class=\"index-aside-inner menulist\" style=\"height:100%\">\r\n      <div v-for=\"item in menuList\" :key=\"item.roleName\" v-if=\"role==item.roleName\" class=\"menulist-item\" style=\"height:100%;broder:0;background-color:#FFB3A7\">\r\n        <div class=\"menulistImg\" style=\"backgroundColor:#ff0000;padding:25px 0\" v-if=\"false && menulistStyle == 'vertical'\">\r\n          <el-image v-if=\"'http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg'\" src=\"http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg\" fit=\"cover\" />\r\n        </div>\r\n        <el-menu mode=\"vertical\" :unique-opened=\"true\" class=\"el-menu-demo\" style=\"height:100%;\" background-color=\"#FFB3A7\" text-color=\"#ffffff\" active-text-color=\"#EEF749\" default-active=\"0\">\r\n          <el-menu-item index=\"(0).toString()\" :style=\"menulistBorderBottom\" @click=\"menuHandler('')\"><i v-if=\"true\" class=\"el-icon-s-home\" />首页</el-menu-item>\r\n          <el-submenu :index=\"(1).toString()\" :style=\"menulistBorderBottom\">\r\n            <template slot=\"title\">\r\n              <i v-if=\"true\" class=\"el-icon-user-solid\" />\r\n              <span>个人中心</span>\r\n            </template>\r\n            <el-menu-item :index=\"(1-2).toString()\" @click=\"menuHandler('updatePassword')\">修改密码</el-menu-item>\r\n            <el-menu-item :index=\"(1-2).toString()\" @click=\"menuHandler('center')\">个人信息</el-menu-item>\r\n          </el-submenu>\r\n          <el-submenu :style=\"menulistBorderBottom\" v-for=\" (menu,index) in item.backMenu\" :key=\"menu.menu\" :index=\"(index+2).toString()\">\r\n            <template slot=\"title\">\r\n              <i v-if=\"true\" :class=\"icons[index]\" />\r\n              <span>{{ menu.menu }}</span>\r\n            </template>\r\n            <el-menu-item v-for=\" (child,sort) in menu.child\" :key=\"sort\" :index=\"((index+2)+'-'+sort).toString()\" @click=\"menuHandler(child.tableName)\">{{ child.menu }}</el-menu-item>\r\n          </el-submenu>\r\n        </el-menu>\r\n\r\n      </div>\r\n    </div>\r\n  </el-aside>\r\n</template>\r\n<script>\r\nimport menu from '@/utils/menu'\r\nexport default {\r\n  data() {\r\n    return {\r\n      menuList: [],\r\n      dynamicMenuRoutes: [],\r\n      role: '',\r\n      icons: [\r\n        'el-icon-s-cooperation',\r\n        'el-icon-s-order',\r\n        'el-icon-s-platform',\r\n        'el-icon-s-fold',\r\n        'el-icon-s-unfold',\r\n        'el-icon-s-operation',\r\n        'el-icon-s-promotion',\r\n        'el-icon-s-release',\r\n        'el-icon-s-ticket',\r\n        'el-icon-s-management',\r\n        'el-icon-s-open',\r\n        'el-icon-s-shop',\r\n        'el-icon-s-marketing',\r\n        'el-icon-s-flag',\r\n        'el-icon-s-comment',\r\n        'el-icon-s-finance',\r\n        'el-icon-s-claim',\r\n        'el-icon-s-custom',\r\n        'el-icon-s-opportunity',\r\n        'el-icon-s-data',\r\n        'el-icon-s-check',\r\n        'el-icon-s-grid',\r\n        'el-icon-menu',\r\n        'el-icon-chat-dot-square',\r\n        'el-icon-message',\r\n        'el-icon-postcard',\r\n        'el-icon-position',\r\n        'el-icon-microphone',\r\n        'el-icon-close-notification',\r\n        'el-icon-bangzhu',\r\n        'el-icon-time',\r\n        'el-icon-odometer',\r\n        'el-icon-crop',\r\n        'el-icon-aim',\r\n        'el-icon-switch-button',\r\n        'el-icon-full-screen',\r\n        'el-icon-copy-document',\r\n        'el-icon-mic',\r\n        'el-icon-stopwatch',\r\n      ],\r\n      menulistStyle: 'vertical',\r\n\t  menulistBorderBottom: {},\r\n    }\r\n  },\r\n  mounted() {\r\n    const menus = menu.list()\r\n    this.menuList = menus\r\n    this.role = this.$storage.get('role')\r\n  },\r\n  created(){\r\n    setTimeout(()=>{\r\n      this.menulistStyleChange()\r\n    },10)\r\n    this.icons.sort(()=>{\r\n      return (0.5-Math.random())\r\n    })\r\n\tthis.lineBorder()\r\n  },\r\n  methods: {\r\n\tlineBorder() {\r\n\t\tlet style = 'vertical'\r\n\t\tlet w = '2px'\r\n\t\tlet s = 'solid'\r\n\t\tlet c = '#D6CFCF'\r\n\t\tif(style == 'vertical') {\r\n\t\t\tthis.menulistBorderBottom = {\r\n\t\t\t\tborderBottomWidth: w,\r\n\t\t\t\tborderBottomStyle: s,\r\n\t\t\t\tborderBottomColor: c\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tthis.menulistBorderBottom = {\r\n\t\t\t\tborderRightWidth: w,\r\n\t\t\t\tborderRightStyle: s,\r\n\t\t\t\tborderRightColor: c\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n    menuHandler(name) {\r\n      let router = this.$router\r\n      name = '/'+name\r\n      router.push(name).catch(err => err)\r\n    },\r\n    // 菜单\r\n    setMenulistHoverColor(){\r\n      let that = this\r\n      this.$nextTick(()=>{\r\n        document.querySelectorAll('.menulist .el-menu-item').forEach(el=>{\r\n          el.addEventListener(\"mouseenter\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"rgba(103, 228, 224, 1)\"\r\n          })\r\n          el.addEventListener(\"mouseleave\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"#FFB3A7\"\r\n          })\r\n          el.addEventListener(\"focus\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"rgba(103, 228, 224, 1)\"\r\n          })\r\n        })\r\n        document.querySelectorAll('.menulist .el-submenu__title').forEach(el=>{\r\n          el.addEventListener(\"mouseenter\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"rgba(103, 228, 224, 1)\"\r\n          })\r\n          el.addEventListener(\"mouseleave\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"#FFB3A7\"\r\n          })\r\n        })\r\n      })\r\n    },\r\n    setMenulistIconColor() {\r\n      this.$nextTick(()=>{\r\n        document.querySelectorAll('.menulist .el-submenu__title .el-submenu__icon-arrow').forEach(el=>{\r\n          el.style.color = \"rgba(247, 11, 11, 1)\"\r\n        })\r\n      })\r\n    },\r\n    menulistStyleChange() {\r\n      this.setMenulistIconColor()\r\n      this.setMenulistHoverColor()\r\n      this.setMenulistStyleHeightChange()\r\n      let str = \"vertical\"\r\n      if(\"horizontal\" === str) {\r\n        this.$nextTick(()=>{\r\n          document.querySelectorAll('.el-container .el-container').forEach(el=>{\r\n            el.style.display = \"block\"\r\n            el.style.paddingTop = \"60px\" // header 高度\r\n          })\r\n          document.querySelectorAll('.el-aside').forEach(el=>{\r\n            el.style.width = \"100%\"\r\n            el.style.height = \"60px\"\r\n            el.style.paddingTop = '0'\r\n          })\r\n          document.querySelectorAll('.index-aside .index-aside-inner').forEach(el=>{\r\n            el.style.paddingTop = '0'\r\n          })\r\n        })\r\n      }\r\n      if(\"vertical\" === str) {\r\n        this.$nextTick(()=>{\r\n          document.querySelectorAll('.index-aside .index-aside-inner').forEach(el=>{\r\n            el.style.paddingTop = \"60px\"\r\n          })\r\n        })\r\n      }\r\n    },\r\n    setMenulistStyleHeightChange() {\r\n      this.$nextTick(()=>{\r\n        document.querySelectorAll('.menulist-item>.el-menu--horizontal>.el-menu-item').forEach(el=>{\r\n          el.style.height = \"60px\"\r\n          el.style.lineHeight = \"60px\"\r\n        })\r\n        document.querySelectorAll('.menulist-item>.el-menu--horizontal>.el-submenu>.el-submenu__title').forEach(el=>{\r\n          el.style.height = \"60px\"\r\n          el.style.lineHeight = \"60px\"\r\n        })\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n  .index-aside {\r\n    position: relative;\r\n    overflow: hidden;\r\n\r\n    .menulistImg {\r\n      padding: 24px 0;\r\n      box-sizing: border-box;\r\n\r\n      .el-image {\r\n        margin: 0 auto;\r\n        width: 100px;\r\n        height: 100px;\r\n        border-radius: 100%;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .index-aside-inner {\r\n      height: 100%;\r\n      margin-right: -17px;\r\n      margin-bottom: -17px;\r\n      overflow: scroll;\r\n      overflow-x: hidden !important;\r\n      padding-top: 60px;\r\n      box-sizing: border-box;\r\n\r\n      &:focus {\r\n        outline: none;\r\n      }\r\n\r\n      .el-menu {\r\n        border: 0;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;AA+BA,OAAAA,IAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,iBAAA;MACAC,IAAA;MACAC,KAAA,GACA,yBACA,mBACA,sBACA,kBACA,oBACA,uBACA,uBACA,qBACA,oBACA,wBACA,kBACA,kBACA,uBACA,kBACA,qBACA,qBACA,mBACA,oBACA,yBACA,kBACA,mBACA,kBACA,gBACA,2BACA,mBACA,oBACA,oBACA,sBACA,8BACA,mBACA,gBACA,oBACA,gBACA,eACA,yBACA,uBACA,yBACA,eACA,oBACA;MACAC,aAAA;MACAC,oBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,KAAA,GAAAT,IAAA,CAAAU,IAAA;IACA,KAAAR,QAAA,GAAAO,KAAA;IACA,KAAAL,IAAA,QAAAO,QAAA,CAAAC,GAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACAC,UAAA;MACAD,KAAA,CAAAE,mBAAA;IACA;IACA,KAAAX,KAAA,CAAAY,IAAA;MACA,aAAAC,IAAA,CAAAC,MAAA;IACA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IACAD,UAAA,WAAAA,WAAA;MACA,IAAAE,KAAA;MACA,IAAAC,CAAA;MACA,IAAAC,CAAA;MACA,IAAAC,CAAA;MACA,IAAAH,KAAA;QACA,KAAAf,oBAAA;UACAmB,iBAAA,EAAAH,CAAA;UACAI,iBAAA,EAAAH,CAAA;UACAI,iBAAA,EAAAH;QACA;MACA;QACA,KAAAlB,oBAAA;UACAsB,gBAAA,EAAAN,CAAA;UACAO,gBAAA,EAAAN,CAAA;UACAO,gBAAA,EAAAN;QACA;MACA;IACA;IACAO,WAAA,WAAAA,YAAAC,IAAA;MACA,IAAAC,MAAA,QAAAC,OAAA;MACAF,IAAA,SAAAA,IAAA;MACAC,MAAA,CAAAE,IAAA,CAAAH,IAAA,EAAAI,KAAA,WAAAC,GAAA;QAAA,OAAAA,GAAA;MAAA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAA;MACA,IAAAC,IAAA;MACA,KAAAC,SAAA;QACAC,QAAA,CAAAC,gBAAA,4BAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAC,gBAAA,yBAAAC,CAAA;YACAA,CAAA,CAAAC,eAAA;YACAH,EAAA,CAAAvB,KAAA,CAAA2B,eAAA;UACA;UACAJ,EAAA,CAAAC,gBAAA,yBAAAC,CAAA;YACAA,CAAA,CAAAC,eAAA;YACAH,EAAA,CAAAvB,KAAA,CAAA2B,eAAA;UACA;UACAJ,EAAA,CAAAC,gBAAA,oBAAAC,CAAA;YACAA,CAAA,CAAAC,eAAA;YACAH,EAAA,CAAAvB,KAAA,CAAA2B,eAAA;UACA;QACA;QACAP,QAAA,CAAAC,gBAAA,iCAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAC,gBAAA,yBAAAC,CAAA;YACAA,CAAA,CAAAC,eAAA;YACAH,EAAA,CAAAvB,KAAA,CAAA2B,eAAA;UACA;UACAJ,EAAA,CAAAC,gBAAA,yBAAAC,CAAA;YACAA,CAAA,CAAAC,eAAA;YACAH,EAAA,CAAAvB,KAAA,CAAA2B,eAAA;UACA;QACA;MACA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MACA,KAAAT,SAAA;QACAC,QAAA,CAAAC,gBAAA,yDAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAvB,KAAA,CAAA6B,KAAA;QACA;MACA;IACA;IACAnC,mBAAA,WAAAA,oBAAA;MACA,KAAAkC,oBAAA;MACA,KAAAX,qBAAA;MACA,KAAAa,4BAAA;MACA,IAAAC,GAAA;MACA,qBAAAA,GAAA;QACA,KAAAZ,SAAA;UACAC,QAAA,CAAAC,gBAAA,gCAAAC,OAAA,WAAAC,EAAA;YACAA,EAAA,CAAAvB,KAAA,CAAAgC,OAAA;YACAT,EAAA,CAAAvB,KAAA,CAAAiC,UAAA;UACA;UACAb,QAAA,CAAAC,gBAAA,cAAAC,OAAA,WAAAC,EAAA;YACAA,EAAA,CAAAvB,KAAA,CAAAkC,KAAA;YACAX,EAAA,CAAAvB,KAAA,CAAAmC,MAAA;YACAZ,EAAA,CAAAvB,KAAA,CAAAiC,UAAA;UACA;UACAb,QAAA,CAAAC,gBAAA,oCAAAC,OAAA,WAAAC,EAAA;YACAA,EAAA,CAAAvB,KAAA,CAAAiC,UAAA;UACA;QACA;MACA;MACA,mBAAAF,GAAA;QACA,KAAAZ,SAAA;UACAC,QAAA,CAAAC,gBAAA,oCAAAC,OAAA,WAAAC,EAAA;YACAA,EAAA,CAAAvB,KAAA,CAAAiC,UAAA;UACA;QACA;MACA;IACA;IACAH,4BAAA,WAAAA,6BAAA;MACA,KAAAX,SAAA;QACAC,QAAA,CAAAC,gBAAA,sDAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAvB,KAAA,CAAAmC,MAAA;UACAZ,EAAA,CAAAvB,KAAA,CAAAoC,UAAA;QACA;QACAhB,QAAA,CAAAC,gBAAA,uEAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAvB,KAAA,CAAAmC,MAAA;UACAZ,EAAA,CAAAvB,KAAA,CAAAoC,UAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}