{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdiCollection\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdiCollection\\list.vue", "mtime": 1642387833310}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";AAw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file": "list.vue", "sourceRoot": "src/views/modules/changdiCollection", "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"main-content\">\r\n\r\n        <!-- 条件查询 -->\r\n        <div v-if=\"showFlag\">\r\n            <el-form :inline=\"true\" :model=\"searchForm\" class=\"form-content\">\r\n                <el-row :gutter=\"20\" class=\"slt\" :style=\"{justifyContent:contents.searchBoxPosition=='1'?'flex-start':contents.searchBoxPosition=='2'?'center':'flex-end'}\">\r\n                                                \r\n                                                             \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '场地名称' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.changdiName\" placeholder=\"场地名称\" clearable></el-input>\r\n                    </el-form-item>\r\n                                         \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '场地类型' : ''\">\r\n                        <el-select v-model=\"searchForm.changdiTypes\" placeholder=\"请选择场地类型\">\r\n                            <el-option label=\"=-请选择-=\" value=\"\"></el-option>\r\n                            <el-option\r\n                                    v-for=\"(item,index) in changdiTypesSelectSearch\"\r\n                                    v-bind:key=\"index\"\r\n                                    :label=\"item.indexName\"\r\n                                    :value=\"item.codeIndex\">\r\n                                <!--lable是要展示的名称-->\r\n                                <!--value是值-->\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                                                                                                                                                                                                                                                                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户姓名' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuName\" placeholder=\"用户姓名\" clearable></el-input>\r\n                    </el-form-item>\r\n                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户手机号' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuPhone\" placeholder=\"用户手机号\" clearable></el-input>\r\n                    </el-form-item>\r\n                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户身份证号' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuIdNumber\" placeholder=\"用户身份证号\" clearable></el-input>\r\n                    </el-form-item>\r\n                                                                                \r\n\r\n                    <el-form-item>\r\n                        <el-button type=\"success\" @click=\"search()\">查询<i class=\"el-icon-search el-icon--right\"/></el-button>\r\n                    </el-form-item>\r\n                </el-row>\r\n                <el-row class=\"ad\" :style=\"{justifyContent:contents.btnAdAllBoxPosition=='1'?'flex-start':contents.btnAdAllBoxPosition=='2'?'center':'flex-end'}\">\r\n                    <el-form-item>\r\n                        <el-button\r\n                                v-if=\"isAuth('changdiCollection','新增')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-plus\"\r\n                                @click=\"addOrUpdateHandler()\"\r\n                        >新增</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('changdiCollection','删除')\"\r\n                                :disabled=\"dataListSelections.length <= 0\"\r\n                                type=\"danger\"\r\n                                icon=\"el-icon-delete\"\r\n                                @click=\"deleteHandler()\"\r\n                        >删除</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('changdiCollection','报表')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-pie-chart\"\r\n                                @click=\"chartDialog()\"\r\n                        >报表</el-button>\r\n                        &nbsp;\r\n                        <a style=\"text-decoration:none\" class=\"el-button el-button--success\"\r\n                           v-if=\"isAuth('changdiCollection','导入导出')\"\r\n                           icon=\"el-icon-download\"\r\n                           href=\"http://localhost:8080/tiyuguan/upload/changdiCollectionMuBan.xls\"\r\n                        >批量导入场地收藏数据模板</a>\r\n                        &nbsp;\r\n                        <el-upload\r\n                                v-if=\"isAuth('changdiCollection','导入导出')\"\r\n                                style=\"display: inline-block\"\r\n                                action=\"tiyuguan/file/upload\"\r\n                                :on-success=\"changdiCollectionUploadSuccess\"\r\n                                :on-error=\"changdiCollectionUploadError\"\r\n                                :show-file-list = false>\r\n                            <el-button\r\n                                    v-if=\"isAuth('changdiCollection','导入导出')\"\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-upload2\"\r\n                            >批量导入场地收藏数据</el-button>\r\n                        </el-upload>\r\n                        &nbsp;\r\n                        <!-- 导出excel -->\r\n                        <download-excel v-if=\"isAuth('changdiCollection','导入导出')\" style=\"display: inline-block\" class = \"export-excel-wrapper\" :data = \"dataList\" :fields = \"json_fields\" name = \"changdiCollection.xls\">\r\n                            <!-- 导出excel -->\r\n                            <el-button\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-download\"\r\n                            >导出</el-button>\r\n                        </download-excel>\r\n                        &nbsp;\r\n                    </el-form-item>\r\n                </el-row>\r\n            </el-form>\r\n            <div class=\"table-content\">\r\n                <el-table class=\"tables\" :size=\"contents.tableSize\" :show-header=\"contents.tableShowHeader\"\r\n                          :header-row-style=\"headerRowStyle\" :header-cell-style=\"headerCellStyle\"\r\n                          :border=\"contents.tableBorder\"\r\n                          :fit=\"contents.tableFit\"\r\n                          :stripe=\"contents.tableStripe\"\r\n                          :row-style=\"rowStyle\"\r\n                          :cell-style=\"cellStyle\"\r\n                          :style=\"{width: '100%',fontSize:contents.tableContentFontSize,color:contents.tableContentFontColor}\"\r\n                          v-if=\"isAuth('changdiCollection','查看')\"\r\n                          :data=\"dataList\"\r\n                          v-loading=\"dataListLoading\"\r\n                          @selection-change=\"selectionChangeHandler\">\r\n                    <el-table-column  v-if=\"contents.tableSelection\"\r\n                                      type=\"selection\"\r\n                                      header-align=\"center\"\r\n                                      align=\"center\"\r\n                                      width=\"50\">\r\n                    </el-table-column>\r\n                    <el-table-column label=\"索引\" v-if=\"contents.tableIndex\" type=\"index\" width=\"50\" />\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiUuidNumber\"\r\n                                      header-align=\"center\"\r\n                                      label=\"场地编号\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiUuidNumber}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiName\"\r\n                                      header-align=\"center\"\r\n                                      label=\"场地名称\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\" prop=\"changdiPhoto\"\r\n                               header-align=\"center\"\r\n                               width=\"200\"\r\n                               label=\"场地照片\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div v-if=\"scope.row.changdiPhoto\">\r\n                                <img :src=\"scope.row.changdiPhoto\" width=\"100\" height=\"100\">\r\n                            </div>\r\n                            <div v-else>无图片</div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiTypes\"\r\n                                      header-align=\"center\"\r\n                                      label=\"场地类型\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiValue}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuName\"\r\n                                      header-align=\"center\"\r\n                                      label=\"用户姓名\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuPhone\"\r\n                                      header-align=\"center\"\r\n                                      label=\"用户手机号\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuPhone}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuIdNumber\"\r\n                                      header-align=\"center\"\r\n                                      label=\"用户身份证号\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuIdNumber}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\" prop=\"yonghuPhoto\"\r\n                               header-align=\"center\"\r\n                               width=\"200\"\r\n                               label=\"用户头像\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div v-if=\"scope.row.yonghuPhoto\">\r\n                                <img :src=\"scope.row.yonghuPhoto\" width=\"100\" height=\"100\">\r\n                            </div>\r\n                            <div v-else>无图片</div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiCollectionTypes\"\r\n                                      header-align=\"center\"\r\n                                      label=\"类型\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiCollectionValue}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"insertTime\"\r\n                                   header-align=\"center\"\r\n                                   label=\"收藏时间\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.insertTime}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column width=\"300\" :align=\"contents.tableAlign\"\r\n                                     header-align=\"center\"\r\n                                     label=\"操作\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button v-if=\"isAuth('changdiCollection','查看')\" type=\"success\" icon=\"el-icon-tickets\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id,'info')\">详情</el-button>\r\n                            <el-button v-if=\"isAuth('changdiCollection','修改')\" type=\"primary\" icon=\"el-icon-edit\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id)\">修改</el-button>\r\n                            <el-button v-if=\"isAuth('changdiCollection','删除')\" type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteHandler(scope.row.id)\">删除</el-button>\r\n\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n                <el-pagination\r\n                        clsss=\"pages\"\r\n                        :layout=\"layouts\"\r\n                        @size-change=\"sizeChangeHandle\"\r\n                        @current-change=\"currentChangeHandle\"\r\n                        :current-page=\"pageIndex\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"Number(contents.pageEachNum)\"\r\n                        :total=\"totalPage\"\r\n                        :small=\"contents.pageStyle\"\r\n                        class=\"pagination-content\"\r\n                        :background=\"contents.pageBtnBG\"\r\n                        :style=\"{textAlign:contents.pagePosition==1?'left':contents.pagePosition==2?'center':'right'}\"\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <!-- 添加/修改页面  将父组件的search方法传递给子组件-->\r\n        <add-or-update v-if=\"addOrUpdateFlag\" :parent=\"this\" ref=\"addOrUpdate\"></add-or-update>\r\n\r\n\r\n\r\n        <el-dialog title=\"统计报表\" :visible.sync=\"chartVisiable\" width=\"800\">\r\n            <el-date-picker\r\n                    v-model=\"echartsDate\"\r\n                    type=\"year\"\r\n                    placeholder=\"选择年\">\r\n            </el-date-picker>\r\n            <el-button @click=\"chartDialog()\">查询</el-button>\r\n            <div id=\"statistic\" style=\"width:100%;height:600px;\"></div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"chartVisiable = false\">返回</el-button>\r\n\t\t\t</span>\r\n        </el-dialog>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import AddOrUpdate from \"./add-or-update\";\r\n    import styleJs from \"../../../utils/style.js\";\r\n    export default {\r\n        data() {\r\n        return {\r\n            searchForm: {\r\n                key: \"\"\r\n            },\r\n            sessionTable : \"\",//登录账户所在表名\r\n            role : \"\",//权限\r\n    //级联表下拉框搜索条件\r\n              changdiTypesSelectSearch : [],\r\n    //当前表下拉框搜索条件\r\n            form:{\r\n                id : null,\r\n                changdiId : null,\r\n                yonghuId : null,\r\n                changdiCollectionTypes : null,\r\n                insertTime : null,\r\n                createTime : null,\r\n            },\r\n            dataList: [],\r\n            pageIndex: 1,\r\n            pageSize: 10,\r\n            totalPage: 0,\r\n            dataListLoading: false,\r\n            dataListSelections: [],\r\n            showFlag: true,\r\n            sfshVisiable: false,\r\n            shForm: {},\r\n            chartVisiable: false,\r\n            addOrUpdateFlag:false,\r\n            contents:null,\r\n            layouts: '',\r\n\r\n            echartsDate: new Date(),//echarts的时间查询字段\r\n\r\n            //导出excel\r\n            json_fields: {\r\n                //级联表字段\r\n                     '场地编号': 'changdiUuidNumber',\r\n                     '场地名称': 'changdiName',\r\n                     '场地照片': 'changdiPhoto',\r\n                     '场地类型': 'changdiTypes',\r\n                     '场地原价': 'changdiOldMoney',\r\n                     '场地现价': 'changdiNewMoney',\r\n                     '时间段': 'shijianduan',\r\n                     '人数': 'shijianduanRen',\r\n                     '点击次数': 'changdiClicknum',\r\n                     '半全场': 'banquanTypes',\r\n                     '是否上架': 'shangxiaTypes',\r\n                     '推荐吃饭地点': 'tuijian',\r\n                     '用户姓名': 'yonghuName',\r\n                     '用户手机号': 'yonghuPhone',\r\n                     '用户身份证号': 'yonghuIdNumber',\r\n                     '用户头像': 'yonghuPhoto',\r\n                     '电子邮箱': 'yonghuEmail',\r\n                     '余额': 'newMoney',\r\n                //本表字段\r\n                     '类型': \"changdiCollectionTypes\",\r\n                     '收藏时间': \"insertTime\",\r\n            },\r\n\r\n            };\r\n        },\r\n        created() {\r\n            this.contents = styleJs.listStyle();\r\n            this.init();\r\n            this.getDataList();\r\n            this.contentStyleChange()\r\n        },\r\n        mounted() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n        },\r\n        filters: {\r\n            htmlfilter: function (val) {\r\n                return val.replace(/<[^>]*>/g).replace(/undefined/g,'');\r\n            }\r\n        },\r\n        components: {\r\n            AddOrUpdate,\r\n        },\r\n        computed: {\r\n        },\r\n        methods: {\r\n            chartDialog() {\r\n                let _this = this;\r\n                let params = {\r\n                    dateFormat :\"%Y\"\r\n                    ,riqi :_this.echartsDate.getFullYear()\r\n                    ,thisTable : {//当前表\r\n                        tableName :\"shangdian_shouyin\"//当前表表名\r\n                        ,sumColum : 'shangdian_shouyin_true_price' //求和字段\r\n                        ,date : 'insert_time'//分组日期字段\r\n                        // ,string : 'name,leixing'//分组字符串字段\r\n                        // ,types : 'shangdian_shouyin_types'//分组下拉框字段\r\n                    }\r\n                    // ,joinTable : {//级联表（可以不存在）\r\n                    //     tableName :\"yuangong\"//级联表表名\r\n                    //     // ,date : 'insert_time'//分组日期字段\r\n                    //     ,string : 'yuangong_name'//分组字符串字段\r\n                    //     // ,types : 'insertTime'//分组下拉框字段\r\n                    // }\r\n                }\r\n                _this.chartVisiable = true;\r\n                _this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"barSum\",\r\n                        method: \"get\",\r\n                        params: params\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n\r\n\r\n                            //柱状图 求和 已成功使用\r\n                            //start\r\n                            let series = [];//具体数据值\r\n                            data.data.yAxis.forEach(function (item,index) {\r\n                                let tempMap = {};\r\n                                tempMap.name=data.data.legend[index];\r\n                                tempMap.type='bar';\r\n                                tempMap.data=item;\r\n                                series.push(tempMap);\r\n\r\n                            })\r\n\r\n                            var option = {\r\n                                tooltip: {\r\n                                    trigger: 'axis',\r\n                                    axisPointer: {\r\n                                        type: 'cross',\r\n                                        crossStyle: {\r\n                                            color: '#999'\r\n                                        }\r\n                                    }\r\n                                },\r\n                                toolbox: {\r\n                                    feature: {\r\n                                        // dataView: { show: true, readOnly: false },  // 数据查看\r\n                                        magicType: { show: true, type: ['line', 'bar'] },//切换图形展示方式\r\n                                        // restore: { show: true }, // 刷新\r\n                                        saveAsImage: { show: true }//保存\r\n                                    }\r\n                                },\r\n                                legend: {\r\n                                    data: data.data.legend//标题  可以点击导致某一列数据消失\r\n                                },\r\n                                xAxis: [\r\n                                    {\r\n                                        type: 'category',\r\n                                        name: '月份',\r\n                                        data: data.data.xAxis,\r\n                                        axisPointer: {\r\n                                            type: 'shadow'\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                yAxis: [\r\n                                    {\r\n                                        type: 'value',//不能改\r\n                                        name: '元',//y轴单位\r\n                                        axisLabel: {\r\n                                            formatter: '{value} 元' // 后缀\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                series:series//具体数据\r\n                            };\r\n                            // 使用刚指定的配置项和数据显示图表。\r\n                            statistic.setOption(option);\r\n                            //根据窗口的大小变动图表\r\n                            window.onresize = function () {\r\n                                statistic.resize();\r\n                            };\r\n                            //end\r\n\r\n\r\n\r\n                            //饼状图 原先自带的 未修改过\r\n                            //start\r\n                            /*let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表。\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图表\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }*/\r\n\r\n                            //end\r\n                        }else {\r\n                            this.$message({\r\n                                message: \"报表未查询到数据\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n                this.chartVisiable = !this.chartVisiable;\r\n                this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"group/xinzitongji/xinzi\",\r\n                        method: \"get\",\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表。\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图表\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }\r\n                        }\r\n                    });\r\n                // xcolumn ycolumn\r\n                });\r\n            },\r\n            contentStyleChange() {\r\n                this.contentSearchStyleChange()\r\n                this.contentBtnAdAllStyleChange()\r\n                this.contentSearchBtnStyleChange()\r\n                this.contentTableBtnStyleChange()\r\n                this.contentPageStyleChange()\r\n            },\r\n            contentSearchStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-input__inner').forEach(el => {\r\n                        let textAlign = 'left'\r\n                        if(this.contents.inputFontPosition == 2)\r\n                            textAlign = 'center'\r\n                            if (this.contents.inputFontPosition == 3) textAlign = 'right'\r\n                                el.style.textAlign = textAlign\r\n                            el.style.height = this.contents.inputHeight\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                            el.style.color = this.contents.inputFontColor\r\n                            el.style.fontSize = this.contents.inputFontSize\r\n                            el.style.borderWidth = this.contents.inputBorderWidth\r\n                            el.style.borderStyle = this.contents.inputBorderStyle\r\n                            el.style.borderColor = this.contents.inputBorderColor\r\n                            el.style.borderRadius = this.contents.inputBorderRadius\r\n                            el.style.backgroundColor = this.contents.inputBgColor\r\n                    })\r\n                    if (this.contents.inputTitle) {\r\n                        document.querySelectorAll('.form-content .slt .el-form-item__label').forEach(el => {\r\n                            el.style.color = this.contents.inputTitleColor\r\n                            el.style.fontSize = this.contents.inputTitleSize\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }\r\n                    setTimeout(() => {\r\n                        document.querySelectorAll('.form-content .slt .el-input__prefix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__suffix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__icon').forEach(el => {\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }, 10 )\r\n                })\r\n            },\r\n            // 搜索按钮\r\n            contentSearchBtnStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.searchBtnHeight\r\n                        el.style.color = this.contents.searchBtnFontColor\r\n                        el.style.fontSize = this.contents.searchBtnFontSize\r\n                        el.style.borderWidth = this.contents.searchBtnBorderWidth\r\n                        el.style.borderStyle = this.contents.searchBtnBorderStyle\r\n                        el.style.borderColor = this.contents.searchBtnBorderColor\r\n                        el.style.borderRadius = this.contents.searchBtnBorderRadius\r\n                        el.style.backgroundColor = this.contents.searchBtnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 新增、批量删除\r\n            contentBtnAdAllStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .ad .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllAddFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllAddBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--danger').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllDelFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllDelBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--warning').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllWarnFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllWarnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 表格\r\n            rowStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {color: this.contents.tableStripeFontColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            cellStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {backgroundColor: this.contents.tableStripeBgColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            headerRowStyle({row, rowIndex}) {\r\n                return {color: this.contents.tableHeaderFontColor}\r\n            },\r\n            headerCellStyle({row, rowIndex}) {\r\n                return {backgroundColor: this.contents.tableHeaderBgColor}\r\n            },\r\n            // 表格按钮\r\n            contentTableBtnStyleChange() {\r\n                // this.$nextTick(()=>{\r\n                //   setTimeout(()=>{\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--success').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDetailFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDetailBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--primary').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnEditFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnEditBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--danger').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDelFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDelBgColor\r\n                //     })\r\n\r\n                //   }, 50)\r\n                // })\r\n            },\r\n            // 分页\r\n            contentPageStyleChange() {\r\n                let arr = []\r\n                if (this.contents.pageTotal) arr.push('total')\r\n                if (this.contents.pageSizes) arr.push('sizes')\r\n                if (this.contents.pagePrevNext) {\r\n                    arr.push('prev')\r\n                    if (this.contents.pagePager) arr.push('pager')\r\n                    arr.push('next')\r\n                }\r\n                if (this.contents.pageJumper) arr.push('jumper')\r\n                this.layouts = arr.join()\r\n                this.contents.pageEachNum = 10\r\n            },\r\n\r\n            init() {\r\n            },\r\n            search() {\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 获取数据列表\r\n            getDataList() {\r\n                this.dataListLoading = true;\r\n                let params = {\r\n                    page: this.pageIndex,\r\n                    limit: this.pageSize,\r\n                    sort: 'id',\r\n                    changdiCollectionTypes: 1,\r\n                }\r\n\r\n                                                             \r\n                if (this.searchForm.changdiName!= '' && this.searchForm.changdiName!= undefined) {\r\n                    params['changdiName'] = '%' + this.searchForm.changdiName + '%'\r\n                }\r\n                                         \r\n                if (this.searchForm.changdiTypes!= '' && this.searchForm.changdiTypes!= undefined) {\r\n                    params['changdiTypes'] = this.searchForm.changdiTypes\r\n                }\r\n                                                                                                                                                                                                                                                                     \r\n                if (this.searchForm.yonghuName!= '' && this.searchForm.yonghuName!= undefined) {\r\n                    params['yonghuName'] = '%' + this.searchForm.yonghuName + '%'\r\n                }\r\n                     \r\n                if (this.searchForm.yonghuPhone!= '' && this.searchForm.yonghuPhone!= undefined) {\r\n                    params['yonghuPhone'] = '%' + this.searchForm.yonghuPhone + '%'\r\n                }\r\n                     \r\n                if (this.searchForm.yonghuIdNumber!= '' && this.searchForm.yonghuIdNumber!= undefined) {\r\n                    params['yonghuIdNumber'] = '%' + this.searchForm.yonghuIdNumber + '%'\r\n                }\r\n                                                                                                                                \r\n                params['changdiCollectionDelete'] = 1// 逻辑删除字段 1 未删除 2 删除\r\n\r\n\r\n                this.$http({\r\n                    url: \"changdiCollection/page\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.dataList = data.data.list;\r\n                        this.totalPage = data.data.total;\r\n                    }else{\r\n                        this.dataList = [];\r\n                        this.totalPage = 0;\r\n                    }\r\n                    this.dataListLoading = false;\r\n                });\r\n\r\n                //查询级联表搜索条件所有列表\r\n                this.$http({\r\n                    url: \"dictionary/page?dicCode=changdi_types&page=1&limit=100\",\r\n                    method: \"get\",\r\n                    page: 1,\r\n                    limit: 100,\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.changdiTypesSelectSearch = data.data.list;\r\n                    }\r\n                });\r\n                //查询当前表搜索条件所有列表\r\n            },\r\n            //每页数\r\n            sizeChangeHandle(val) {\r\n                this.pageSize = val;\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 当前页\r\n            currentChangeHandle(val) {\r\n                this.pageIndex = val;\r\n                this.getDataList();\r\n            },\r\n            // 多选\r\n            selectionChangeHandler(val) {\r\n                this.dataListSelections = val;\r\n            },\r\n            // 添加/修改\r\n            addOrUpdateHandler(id, type) {\r\n                this.showFlag = false;\r\n                this.addOrUpdateFlag = true;\r\n                this.crossAddOrUpdateFlag = false;\r\n                if (type != 'info') {\r\n                    type = 'else';\r\n                }\r\n                this.$nextTick(() => {\r\n                    this.$refs.addOrUpdate.init(id, type);\r\n                });\r\n            },\r\n            // 下载\r\n            download(file) {\r\n                window.open(\" ${file} \")\r\n            },\r\n            // 删除\r\n            deleteHandler(id) {\r\n                var ids = id ? [Number(id)] : this.dataListSelections.map(item => {\r\n                    return Number(item.id);\r\n                });\r\n\r\n                this.$confirm(`确定进行[${id ? \"删除\" : \"批量删除\"}]操作?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: \"changdiCollection/delete\",\r\n                        method: \"post\",\r\n                        data: ids\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            this.$message({\r\n                                message: \"操作成功\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }else{\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n            // 导入功能上传文件成功后调用导入方法\r\n            changdiCollectionUploadSuccess(data){\r\n                let _this = this;\r\n                _this.$http({\r\n                    url: \"changdiCollection/batchInsert?fileName=\" + data.file,\r\n                    method: \"get\"\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        _this.$message({\r\n                            message: \"导入场地收藏数据成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.search();\r\n                            }\r\n                        });\r\n                    }else{\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n\r\n            },\r\n            // 导入功能上传文件失败后调用导入方法\r\n            changdiCollectionUploadError(data){\r\n                this.$message.error('上传失败');\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.slt {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .ad {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .pages {\r\n    & /deep/ el-pagination__sizes{\r\n      & /deep/ el-input__inner {\r\n        height: 22px;\r\n        line-height: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .el-button+.el-button {\r\n    margin:0;\r\n  }\r\n\r\n  .tables {\r\n\t& /deep/ .el-button--success {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(121, 244, 164, 0.54);\r\n\t}\r\n\r\n\t& /deep/ .el-button--primary {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(169, 115, 232, 0.65);\r\n\t}\r\n\r\n\t& /deep/ .el-button--danger {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(241, 120, 186, 1);\r\n\t}\r\n\r\n    & /deep/ .el-button {\r\n      margin: 4px;\r\n    }\r\n  }\r\n</style>\r\n\r\n\r\n"]}]}