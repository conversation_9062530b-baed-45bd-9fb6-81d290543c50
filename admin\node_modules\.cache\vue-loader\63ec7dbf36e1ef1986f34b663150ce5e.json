{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexHeader.vue?vue&type=style&index=0&id=71e3f342&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexHeader.vue", "mtime": 1642386767413}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5uYXZiYXIgewogICAgaGVpZ2h0OiA2MHB4OwogICAgbGluZS1oZWlnaHQ6IDYwcHg7CiAgICB3aWR0aDogMTAwJTsKICAgIHBhZGRpbmc6IDAgMzRweDsKICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmYwMGZmOwogICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgei1pbmRleDogMTExOwoKLnJpZ2h0LW1lbnUgewogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgcmlnaHQ6IDM0cHg7CiAgICB0b3A6IDA7CiAgICBoZWlnaHQ6IDEwMCU7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICB6LWluZGV4OiAxMTE7CgoudXNlci1pbmZvIHsKICAgIGZvbnQtc2l6ZTogMTZweDsKICAgIGNvbG9yOiByZWQ7CiAgICBwYWRkaW5nOiAwIDEycHg7Cn0KCi5sb2dvdXQgewogICAgZm9udC1zaXplOiAxNnB4OwogICAgY29sb3I6IHJlZDsKICAgIHBhZGRpbmc6IDAgMTJweDsKICAgIGN1cnNvcjogcG9pbnRlcjsKfQoKfQoKLnRpdGxlLW1lbnUgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICB3aWR0aDogMTAwJTsKICAgIGhlaWdodDogMTAwJTsKCi50aXRsZS1pbWcgewogICAgd2lkdGg6IDQ0cHg7CiAgICBoZWlnaHQ6IDQ0cHg7CiAgICBib3JkZXItcmFkaXVzOiAyMnB4OwogICAgYm94LXNoYWRvdzogMCAxcHggNnB4ICM0NDQ7CiAgICBtYXJnaW4tcmlnaHQ6IDE2cHg7Cn0KCi50aXRsZS1uYW1lIHsKICAgIGZvbnQtc2l6ZTogMjRweDsKICAgIGNvbG9yOiAjZmZmOwogICAgZm9udC13ZWlnaHQ6IDcwMDsKfQp9Cn0KLy8gLmVsLWhlYWRlciAuZnIgewogICAvLyAJZmxvYXQ6IHJpZ2h0OwogICAvLyB9CgovLyAuZWwtaGVhZGVyIC5mbCB7CiAgIC8vIAlmbG9hdDogbGVmdDsKICAgLy8gfQoKLy8gLmVsLWhlYWRlciB7CiAgIC8vIAl3aWR0aDogMTAwJTsKICAgLy8gCWNvbG9yOiAjMzMzOwogICAvLyAJdGV4dC1hbGlnbjogY2VudGVyOwogICAvLyAJbGluZS1oZWlnaHQ6IDYwcHg7CiAgIC8vIAlwYWRkaW5nOiAwOwogICAvLyAJei1pbmRleDogOTk7CiAgIC8vIH0KCi8vIC5sb2dvIHsKICAgLy8gCXdpZHRoOiA2MHB4OwogICAvLyAJaGVpZ2h0OiA2MHB4OwogICAvLyAJbWFyZ2luLWxlZnQ6IDcwcHg7CiAgIC8vIH0KCi8vIC5hdmF0b3IgewogICAvLyAJd2lkdGg6IDQwcHg7CiAgIC8vIAloZWlnaHQ6IDQwcHg7CiAgIC8vIAliYWNrZ3JvdW5kOiAjZmZmZmZmOwogICAvLyAJYm9yZGVyLXJhZGl1czogNTAlOwogICAvLyB9CgovLyAudGl0bGUgewogICAvLyAJY29sb3I6ICNmZmZmZmY7CiAgIC8vIAlmb250LXNpemU6IDIwcHg7CiAgIC8vIAlmb250LXdlaWdodDogYm9sZDsKICAgLy8gCW1hcmdpbi1sZWZ0OiAyMHB4OwogICAvLyB9Cg=="}, {"version": 3, "sources": ["IndexHeader.vue"], "names": [], "mappings": ";AAyLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "IndexHeader.vue", "sourceRoot": "src/components/index", "sourcesContent": ["<template>\r\n    <!-- <el-header>\r\n        <el-menu background-color=\"#00c292\" text-color=\"#FFFFFF\" active-text-color=\"#FFFFFF\" mode=\"horizontal\">\r\n            <div class=\"fl title\">{{this.$project.projectName}}</div>\r\n            <div class=\"fr logout\" style=\"display:flex;\">\r\n                <el-menu-item index=\"3\">\r\n                    <div>{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</div>\r\n                </el-menu-item>\r\n                <el-menu-item @click=\"onLogout\" index=\"2\">\r\n                    <div>退出登录</div>\r\n                </el-menu-item>\r\n            </div>\r\n        </el-menu>\r\n    </el-header> -->\r\n    <div class=\"navbar\" :style=\"{backgroundColor:heads.headBgColor,height:heads.headHeight,boxShadow:heads.headBoxShadow,lineHeight:heads.headHeight}\">\r\n        <div class=\"title-menu\" :style=\"{justifyContent:heads.headTitleStyle=='1'?'flex-start':'center'}\">\r\n            <el-image v-if=\"heads.headTitleImg\" class=\"title-img\" :style=\"{width:heads.headTitleImgWidth,height:heads.headTitleImgHeight,boxShadow:heads.headTitleImgBoxShadow,borderRadius:heads.headTitleImgBorderRadius}\" :src=\"heads.headTitleImgUrl\" fit=\"cover\"></el-image>\r\n            <div class=\"title-name\" :style=\"{color:heads.headFontColor,fontSize:heads.headFontSize}\">{{this.$project.projectName}}</div>\r\n        </div>\r\n        <div class=\"right-menu\">\r\n            <div class=\"user-info\" :style=\"{color:heads.headUserInfoFontColor,fontSize:heads.headUserInfoFontSize}\">{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</div>\r\n\t\t\t<div class=\"logout\" :style=\"{color:heads.headLogoutFontColor,fontSize:heads.headLogoutFontSize}\" @click=\"onIndexTap\">退出到前台</div>\r\n            <div class=\"logout\" :style=\"{color:heads.headLogoutFontColor,fontSize:heads.headLogoutFontSize}\" @click=\"onLogout\">退出登录</div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        data() {\r\n            return {\r\n                dialogVisible: false,\r\n                ruleForm: {},\r\n                user: {},\r\n                heads: {\"headLogoutFontHoverColor\":\"#fff\",\"headFontSize\":\"20px\",\"headUserInfoFontColor\":\"#333\",\"headBoxShadow\":\"0 1px 6px #444\",\"headTitleImgHeight\":\"44px\",\"headLogoutFontHoverBgColor\":\"#333\",\"headFontColor\":\"#000\",\"headTitleImg\":false,\"headHeight\":\"60px\",\"headTitleImgBorderRadius\":\"22px\",\"headTitleImgUrl\":\"http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg\",\"headBgColor\":\"#E0F0E9\",\"headTitleImgBoxShadow\":\"0 1px 6px #444\",\"headLogoutFontColor\":\"#333\",\"headUserInfoFontSize\":\"16px\",\"headTitleImgWidth\":\"44px\",\"headTitleStyle\":\"1\",\"headLogoutFontSize\":\"16px\"},\r\n            };\r\n        },\r\n        created() {\r\n            this.setHeaderStyle()\r\n        },\r\n        mounted() {\r\n            let sessionTable = this.$storage.get(\"sessionTable\")\r\n            this.$http({\r\n                url: sessionTable + '/session',\r\n                method: \"get\"\r\n            }).then(({\r\n                         data\r\n                     }) => {\r\n                if (data && data.code === 0) {\r\n                    this.user = data.data;\r\n                } else {\r\n                    let message = this.$message\r\n                    message.error(data.msg);\r\n                }\r\n            });\r\n        },\r\n        methods: {\r\n            onLogout() {\r\n                let storage = this.$storage\r\n                let router = this.$router\r\n                storage.remove(\"Token\");\r\n                router.replace({\r\n                    name: \"login\"\r\n                });\r\n            },\r\n            onIndexTap(){\r\n                window.location.href = `${this.$base.indexUrl}`\r\n            },\r\n            setHeaderStyle() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.navbar .right-menu .logout').forEach(el=>{\r\n                        el.addEventListener(\"mouseenter\", e => {\r\n                            e.stopPropagation()\r\n                            el.style.backgroundColor = this.heads.headLogoutFontHoverBgColor\r\n                            el.style.color = this.heads.headLogoutFontHoverColor\r\n                        })\r\n                        el.addEventListener(\"mouseleave\", e => {\r\n                            e.stopPropagation()\r\n                            el.style.backgroundColor = \"transparent\"\r\n                            el.style.color = this.heads.headLogoutFontColor\r\n                        })\r\n                    })\r\n                })\r\n            },\r\n        }\r\n    };\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n    .navbar {\r\n        height: 60px;\r\n        line-height: 60px;\r\n        width: 100%;\r\n        padding: 0 34px;\r\n        box-sizing: border-box;\r\n        background-color: #ff00ff;\r\n        position: relative;\r\n        z-index: 111;\r\n\r\n    .right-menu {\r\n        position: absolute;\r\n        right: 34px;\r\n        top: 0;\r\n        height: 100%;\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        align-items: center;\r\n        z-index: 111;\r\n\r\n    .user-info {\r\n        font-size: 16px;\r\n        color: red;\r\n        padding: 0 12px;\r\n    }\r\n\r\n    .logout {\r\n        font-size: 16px;\r\n        color: red;\r\n        padding: 0 12px;\r\n        cursor: pointer;\r\n    }\r\n\r\n    }\r\n\r\n    .title-menu {\r\n        display: flex;\r\n        justify-content: flex-start;\r\n        align-items: center;\r\n        width: 100%;\r\n        height: 100%;\r\n\r\n    .title-img {\r\n        width: 44px;\r\n        height: 44px;\r\n        border-radius: 22px;\r\n        box-shadow: 0 1px 6px #444;\r\n        margin-right: 16px;\r\n    }\r\n\r\n    .title-name {\r\n        font-size: 24px;\r\n        color: #fff;\r\n        font-weight: 700;\r\n    }\r\n    }\r\n    }\r\n    // .el-header .fr {\r\n       // \tfloat: right;\r\n       // }\r\n\r\n    // .el-header .fl {\r\n       // \tfloat: left;\r\n       // }\r\n\r\n    // .el-header {\r\n       // \twidth: 100%;\r\n       // \tcolor: #333;\r\n       // \ttext-align: center;\r\n       // \tline-height: 60px;\r\n       // \tpadding: 0;\r\n       // \tz-index: 99;\r\n       // }\r\n\r\n    // .logo {\r\n       // \twidth: 60px;\r\n       // \theight: 60px;\r\n       // \tmargin-left: 70px;\r\n       // }\r\n\r\n    // .avator {\r\n       // \twidth: 40px;\r\n       // \theight: 40px;\r\n       // \tbackground: #ffffff;\r\n       // \tborder-radius: 50%;\r\n       // }\r\n\r\n    // .title {\r\n       // \tcolor: #ffffff;\r\n       // \tfont-size: 20px;\r\n       // \tfont-weight: bold;\r\n       // \tmargin-left: 20px;\r\n       // }\r\n</style>\r\n"]}]}