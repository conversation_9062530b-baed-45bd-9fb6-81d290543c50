{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\center.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\center.vue", "mtime": 1642386767421}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQovLyDmlbDlrZfvvIzpgq7ku7bvvIzmiYvmnLrvvIx1cmzvvIzouqvku73or4HmoKHpqowNCmltcG9ydCB7IGlzTnVtYmVyLGlzSW50TnVtZXIsaXNFbWFpbCxpc01vYmlsZSxpc1Bob25lLGlzVVJMLGNoZWNrSWRDYXJkIH0gZnJvbSAiQC91dGlscy92YWxpZGF0ZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcnVsZUZvcm06IHt9LA0KICAgICAgZmxhZzogJycsDQogICAgICB1c2Vyc0ZsYWc6IGZhbHNlLA0KICAgICAgc2V4VHlwZXNPcHRpb25zIDogW10sDQoNCg0KDQoNCg0KDQoNCg0KICAgIH07DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgLy/ojrflj5blvZPliY3nmbvlvZXnlKjmiLfnmoTkv6Hmga8NCiAgICB2YXIgdGFibGUgPSB0aGlzLiRzdG9yYWdlLmdldCgic2Vzc2lvblRhYmxlIik7DQogICAgdGhpcy5zZXNzaW9uVGFibGUgPSB0aGlzLiRzdG9yYWdlLmdldCgic2Vzc2lvblRhYmxlIik7DQogICAgdGhpcy5yb2xlID0gdGhpcy4kc3RvcmFnZS5nZXQoInJvbGUiKTsNCiAgICBpZiAodGhpcy5yb2xlICE9ICLnrqHnkIblkZgiKXsNCiAgICB9DQoNCiAgICB0aGlzLmZsYWcgPSB0YWJsZTsNCiAgICB0aGlzLiRodHRwKHsNCiAgICAgIHVybDogYCR7dGhpcy4kc3RvcmFnZS5nZXQoInNlc3Npb25UYWJsZSIpfS9zZXNzaW9uYCwNCiAgICAgIG1ldGhvZDogImdldCINCiAgICB9KS50aGVuKCh7IGRhdGEgfSkgPT4gew0KICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7DQogICAgICAgIHRoaXMucnVsZUZvcm0gPSBkYXRhLmRhdGE7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGRhdGEubXNnKTsNCiAgICAgIH0NCiAgICB9KTsNCiAgICAgIHRoaXMuJGh0dHAoew0KICAgICAgICAgIHVybDogYGRpY3Rpb25hcnkvcGFnZT9wYWdlPTEmbGltaXQ9MTAwJnNvcnQ9Jm9yZGVyPSZkaWNDb2RlPXNleF90eXBlc2AsDQogICAgICAgICAgbWV0aG9kOiAiZ2V0Ig0KICAgICAgfSkudGhlbigoeyBkYXRhIH0pID0+IHsNCiAgICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmNvZGUgPT09IDApIHsNCiAgICAgICAgICB0aGlzLnNleFR5cGVzT3B0aW9ucyA9IGRhdGEuZGF0YS5saXN0Ow0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGRhdGEubXNnKTsNCiAgICAgIH0NCiAgfSk7DQoNCg0KDQoNCg0KDQoNCg0KICB9LA0KICBtZXRob2RzOiB7DQogICAgeW9uZ2h1UGhvdG9VcGxvYWRDaGFuZ2UoZmlsZVVybHMpIHsNCiAgICAgICAgdGhpcy5ydWxlRm9ybS55b25naHVQaG90byA9IGZpbGVVcmxzOw0KICAgIH0sDQoNCiAgICBvblVwZGF0ZUhhbmRsZXIoKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgaWYoKCF0aGlzLnJ1bGVGb3JtLnlvbmdodU5hbWUpJiYgJ3lvbmdodSc9PXRoaXMuZmxhZyl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+eUqOaIt+Wnk+WQjeS4jeiDveS4uuepuicpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYoICd5b25naHUnID09dGhpcy5mbGFnICYmIHRoaXMucnVsZUZvcm0ueW9uZ2h1UGhvbmUmJighaXNNb2JpbGUodGhpcy5ydWxlRm9ybS55b25naHVQaG9uZSkpKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOaJi+acuuW6lOi+k+WFpeaJi+acuuagvOW8j2ApOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgICBpZigoIXRoaXMucnVsZUZvcm0ueW9uZ2h1SWROdW1iZXIpJiYgJ3lvbmdodSc9PXRoaXMuZmxhZyl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+eUqOaIt+i6q+S7veivgeWPt+S4jeiDveS4uuepuicpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAgICAgICAgICBpZigoIXRoaXMucnVsZUZvcm0ueW9uZ2h1UGhvdG8pJiYgJ3lvbmdodSc9PXRoaXMuZmxhZyl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+eUqOaIt+WktOWDj+S4jeiDveS4uuepuicpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYoICd5b25naHUnID09dGhpcy5mbGFnICYmIHRoaXMucnVsZUZvcm0ueW9uZ2h1RW1haWwmJighaXNFbWFpbCh0aGlzLnJ1bGVGb3JtLnlvbmdodUVtYWlsKSkpew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg6YKu566x5bqU6L6T5YWl6YKu566x5qC85byPYCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICBpZigoIXRoaXMucnVsZUZvcm0uc2V4VHlwZXMpJiYgdGhpcy5mbGFnICE9J3VzZXJzJyl7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmgKfliKvkuI3og73kuLrnqbonKTsNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICBpZigndXNlcnMnPT10aGlzLmZsYWcgJiYgdGhpcy5ydWxlRm9ybS51c2VybmFtZS50cmltKCkubGVuZ3RoPDEpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg55So5oi35ZCN5LiN6IO95Li656m6YCk7DQogICAgICAgIHJldHVybgkNCiAgICAgIH0NCiAgICAgIHRoaXMuJGh0dHAoew0KICAgICAgICB1cmw6IGAke3RoaXMuJHN0b3JhZ2UuZ2V0KCJzZXNzaW9uVGFibGUiKX0vdXBkYXRlYCwNCiAgICAgICAgbWV0aG9kOiAicG9zdCIsDQogICAgICAgIGRhdGE6IHRoaXMucnVsZUZvcm0NCiAgICAgIH0pLnRoZW4oKHsgZGF0YSB9KSA9PiB7DQogICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogIuS/ruaUueS/oeaBr+aIkOWKnyIsDQogICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgICAgICBkdXJhdGlvbjogMTUwMCwNCiAgICAgICAgICAgIG9uQ2xvc2U6ICgpID0+IHsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGRhdGEubXNnKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["center.vue"], "names": [], "mappings": ";AAuEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AASA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "center.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      class=\"detail-form-content\"\r\n      ref=\"ruleForm\"\r\n      :model=\"ruleForm\"\r\n      label-width=\"80px\"\r\n    >  \r\n     <el-row>\r\n                    <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='用户姓名' prop=\"yonghuName\">\r\n               <el-input v-model=\"ruleForm.yonghuName\"  placeholder='用户姓名' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='用户手机号' prop=\"yonghuPhone\">\r\n               <el-input v-model=\"ruleForm.yonghuPhone\"  placeholder='用户手机号' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='用户身份证号' prop=\"yonghuIdNumber\">\r\n               <el-input v-model=\"ruleForm.yonghuIdNumber\"  placeholder='用户身份证号' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-col :span=\"24\">\r\n             <el-form-item v-if=\"flag=='yonghu'\" label='用户头像' prop=\"yonghuPhoto\">\r\n                 <file-upload\r\n                         tip=\"点击上传照片\"\r\n                         action=\"file/upload\"\r\n                         :limit=\"3\"\r\n                         :multiple=\"true\"\r\n                         :fileUrls=\"ruleForm.yonghuPhoto?ruleForm.yonghuPhoto:''\"\r\n                         @change=\"yonghuPhotoUploadChange\"\r\n                 ></file-upload>\r\n             </el-form-item>\r\n         </el-col>\r\n         <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='电子邮箱' prop=\"yonghuEmail\">\r\n               <el-input v-model=\"ruleForm.yonghuEmail\"  placeholder='电子邮箱' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-form-item v-if=\"flag=='users'\" label=\"用户名\" prop=\"username\">\r\n             <el-input v-model=\"ruleForm.username\"\r\n                       placeholder=\"用户名\"></el-input>\r\n         </el-form-item>\r\n         <el-col :span=\"12\">\r\n             <el-form-item v-if=\"flag!='users'\"  label=\"性别\" prop=\"sexTypes\">\r\n                 <el-select v-model=\"ruleForm.sexTypes\" placeholder=\"请选择性别\">\r\n                     <el-option\r\n                             v-for=\"(item,index) in sexTypesOptions\"\r\n                             v-bind:key=\"item.codeIndex\"\r\n                             :label=\"item.indexName\"\r\n                             :value=\"item.codeIndex\">\r\n                     </el-option>\r\n                 </el-select>\r\n             </el-form-item>\r\n         </el-col>\r\n         <el-col :span=\"24\">\r\n             <el-form-item>\r\n                 <el-button type=\"primary\" @click=\"onUpdateHandler\">修 改</el-button>\r\n             </el-form-item>\r\n         </el-col>\r\n     </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isMobile,isPhone,isURL,checkIdCard } from \"@/utils/validate\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      ruleForm: {},\r\n      flag: '',\r\n      usersFlag: false,\r\n      sexTypesOptions : [],\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n    };\r\n  },\r\n  mounted() {\r\n    //获取当前登录用户的信息\r\n    var table = this.$storage.get(\"sessionTable\");\r\n    this.sessionTable = this.$storage.get(\"sessionTable\");\r\n    this.role = this.$storage.get(\"role\");\r\n    if (this.role != \"管理员\"){\r\n    }\r\n\r\n    this.flag = table;\r\n    this.$http({\r\n      url: `${this.$storage.get(\"sessionTable\")}/session`,\r\n      method: \"get\"\r\n    }).then(({ data }) => {\r\n      if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n      } else {\r\n        this.$message.error(data.msg);\r\n      }\r\n    });\r\n      this.$http({\r\n          url: `dictionary/page?page=1&limit=100&sort=&order=&dicCode=sex_types`,\r\n          method: \"get\"\r\n      }).then(({ data }) => {\r\n          if (data && data.code === 0) {\r\n          this.sexTypesOptions = data.data.list;\r\n      } else {\r\n          this.$message.error(data.msg);\r\n      }\r\n  });\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  },\r\n  methods: {\r\n    yonghuPhotoUploadChange(fileUrls) {\r\n        this.ruleForm.yonghuPhoto = fileUrls;\r\n    },\r\n\r\n    onUpdateHandler() {\r\n                         if((!this.ruleForm.yonghuName)&& 'yonghu'==this.flag){\r\n                             this.$message.error('用户姓名不能为空');\r\n                             return\r\n                         }\r\n\r\n                             if( 'yonghu' ==this.flag && this.ruleForm.yonghuPhone&&(!isMobile(this.ruleForm.yonghuPhone))){\r\n                                 this.$message.error(`手机应输入手机格式`);\r\n                                 return\r\n                             }\r\n                         if((!this.ruleForm.yonghuIdNumber)&& 'yonghu'==this.flag){\r\n                             this.$message.error('用户身份证号不能为空');\r\n                             return\r\n                         }\r\n\r\n                         if((!this.ruleForm.yonghuPhoto)&& 'yonghu'==this.flag){\r\n                             this.$message.error('用户头像不能为空');\r\n                             return\r\n                         }\r\n\r\n                             if( 'yonghu' ==this.flag && this.ruleForm.yonghuEmail&&(!isEmail(this.ruleForm.yonghuEmail))){\r\n                                 this.$message.error(`邮箱应输入邮箱格式`);\r\n                                 return\r\n                             }\r\n        if((!this.ruleForm.sexTypes)&& this.flag !='users'){\r\n            this.$message.error('性别不能为空');\r\n            return\r\n        }\r\n      if('users'==this.flag && this.ruleForm.username.trim().length<1) {\r\n        this.$message.error(`用户名不能为空`);\r\n        return\t\r\n      }\r\n      this.$http({\r\n        url: `${this.$storage.get(\"sessionTable\")}/update`,\r\n        method: \"post\",\r\n        data: this.ruleForm\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n          this.$message({\r\n            message: \"修改信息成功\",\r\n            type: \"success\",\r\n            duration: 1500,\r\n            onClose: () => {\r\n            }\r\n          });\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n"]}]}