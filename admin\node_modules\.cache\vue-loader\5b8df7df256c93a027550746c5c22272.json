{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\center.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\center.vue", "mtime": 1750584012658}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["center.vue"], "names": [], "mappings": ";AAuEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AASA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "center.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      class=\"detail-form-content\"\r\n      ref=\"ruleForm\"\r\n      :model=\"ruleForm\"\r\n      label-width=\"80px\"\r\n    >  \r\n     <el-row>\r\n                    <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='用户姓名' prop=\"yonghuName\">\r\n               <el-input v-model=\"ruleForm.yonghuName\"  placeholder='用户姓名' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='用户手机�? prop=\"yonghuPhone\">\r\n               <el-input v-model=\"ruleForm.yonghuPhone\"  placeholder='用户手机�? clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='用户身份证号' prop=\"yonghuIdNumber\">\r\n               <el-input v-model=\"ruleForm.yonghuIdNumber\"  placeholder='用户身份证号' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-col :span=\"24\">\r\n             <el-form-item v-if=\"flag=='yonghu'\" label='用户头像' prop=\"yonghuPhoto\">\r\n                 <file-upload\r\n                         tip=\"点击上传照片\"\r\n                         action=\"file/upload\"\r\n                         :limit=\"3\"\r\n                         :multiple=\"true\"\r\n                         :fileUrls=\"ruleForm.yonghuPhoto?ruleForm.yonghuPhoto:''\"\r\n                         @change=\"yonghuPhotoUploadChange\"\r\n                 ></file-upload>\r\n             </el-form-item>\r\n         </el-col>\r\n         <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='电子邮箱' prop=\"yonghuEmail\">\r\n               <el-input v-model=\"ruleForm.yonghuEmail\"  placeholder='电子邮箱' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-form-item v-if=\"flag=='users'\" label=\"用户�? prop=\"username\">\r\n             <el-input v-model=\"ruleForm.username\"\r\n                       placeholder=\"用户�?></el-input>\r\n         </el-form-item>\r\n         <el-col :span=\"12\">\r\n             <el-form-item v-if=\"flag!='users'\"  label=\"性别\" prop=\"sexTypes\">\r\n                 <el-select v-model=\"ruleForm.sexTypes\" placeholder=\"请选择性别\">\r\n                     <el-option\r\n                             v-for=\"(item,index) in sexTypesOptions\"\r\n                             v-bind:key=\"item.codeIndex\"\r\n                             :label=\"item.indexName\"\r\n                             :value=\"item.codeIndex\">\r\n                     </el-option>\r\n                 </el-select>\r\n             </el-form-item>\r\n         </el-col>\r\n         <el-col :span=\"24\">\r\n             <el-form-item>\r\n                 <el-button type=\"primary\" @click=\"onUpdateHandler\">�?�?/el-button>\r\n             </el-form-item>\r\n         </el-col>\r\n     </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isMobile,isPhone,isURL,checkIdCard } from \"@/utils/validate\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      ruleForm: {},\r\n      flag: '',\r\n      usersFlag: false,\r\n      sexTypesOptions : [],\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n    };\r\n  },\r\n  mounted() {\r\n    //获取当前登录用户的信�?\r\n    var table = this.$storage.get(\"sessionTable\");\r\n    this.sessionTable = this.$storage.get(\"sessionTable\");\r\n    this.role = this.$storage.get(\"role\");\r\n    if (this.role != \"管理员\"){\r\n    }\r\n\r\n    this.flag = table;\r\n    this.$http({\r\n      url: `${this.$storage.get(\"sessionTable\")}/session`,\r\n      method: \"get\"\r\n    }).then(({ data }) => {\r\n      if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n      } else {\r\n        this.$message.error(data.msg);\r\n      }\r\n    });\r\n      this.$http({\r\n          url: `dictionary/page?page=1&limit=100&sort=&order=&dicCode=sex_types`,\r\n          method: \"get\"\r\n      }).then(({ data }) => {\r\n          if (data && data.code === 0) {\r\n          this.sexTypesOptions = data.data.list;\r\n      } else {\r\n          this.$message.error(data.msg);\r\n      }\r\n  });\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  },\r\n  methods: {\r\n    yonghuPhotoUploadChange(fileUrls) {\r\n        this.ruleForm.yonghuPhoto = fileUrls;\r\n    },\r\n\r\n    onUpdateHandler() {\r\n                         if((!this.ruleForm.yonghuName)&& 'yonghu'==this.flag){\r\n                             this.$message.error('用户姓名不能为空');\r\n                             return\r\n                         }\r\n\r\n                             if( 'yonghu' ==this.flag && this.ruleForm.yonghuPhone&&(!isMobile(this.ruleForm.yonghuPhone))){\r\n                                 this.$message.error(`手机应输入手机格式`);\r\n                                 return\r\n                             }\r\n                         if((!this.ruleForm.yonghuIdNumber)&& 'yonghu'==this.flag){\r\n                             this.$message.error('用户身份证号不能为空');\r\n                             return\r\n                         }\r\n\r\n                         if((!this.ruleForm.yonghuPhoto)&& 'yonghu'==this.flag){\r\n                             this.$message.error('用户头像不能为空');\r\n                             return\r\n                         }\r\n\r\n                             if( 'yonghu' ==this.flag && this.ruleForm.yonghuEmail&&(!isEmail(this.ruleForm.yonghuEmail))){\r\n                                 this.$message.error(`邮箱应输入邮箱格式`);\r\n                                 return\r\n                             }\r\n        if((!this.ruleForm.sexTypes)&& this.flag !='users'){\r\n            this.$message.error('性别不能为空');\r\n            return\r\n        }\r\n      if('users'==this.flag && this.ruleForm.username.trim().length<1) {\r\n        this.$message.error(`用户名不能为空`);\r\n        return\t\r\n      }\r\n      this.$http({\r\n        url: `${this.$storage.get(\"sessionTable\")}/update`,\r\n        method: \"post\",\r\n        data: this.ruleForm\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n          this.$message({\r\n            message: \"修改信息成功\",\r\n            type: \"success\",\r\n            duration: 1500,\r\n            onClose: () => {\r\n            }\r\n          });\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n\r\n"]}]}