{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\SvgIcon\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\SvgIcon\\index.vue", "mtime": 1750583733788}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdTdmdJY29uJywNCiAgcHJvcHM6IHsNCiAgICBpY29uQ2xhc3M6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIHJlcXVpcmVkOiB0cnVlDQogICAgfSwNCiAgICBjbGFzc05hbWU6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIGljb25OYW1lKCkgew0KICAgICAgcmV0dXJuIGAjaWNvbi0ke3RoaXMuaWNvbkNsYXNzfWANCiAgICB9LA0KICAgIHN2Z0NsYXNzKCkgew0KICAgICAgaWYgKHRoaXMuY2xhc3NOYW1lKSB7DQogICAgICAgIHJldHVybiAnc3ZnLWljb24gJyArIHRoaXMuY2xhc3NOYW1lDQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJ3N2Zy1pY29uJw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/SvgIcon", "sourcesContent": ["<template>\r\n  <svg :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\r\n    <use :xlink:href=\"iconName\" />\r\n  </svg>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SvgIcon',\r\n  props: {\r\n    iconClass: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    className: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  computed: {\r\n    iconName() {\r\n      return `#icon-${this.iconClass}`\r\n    },\r\n    svgClass() {\r\n      if (this.className) {\r\n        return 'svg-icon ' + this.className\r\n      } else {\r\n        return 'svg-icon'\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.svg-icon {\r\n  width: 1em;\r\n  height: 1em;\r\n  vertical-align: -0.15em;\r\n  fill: currentColor;\r\n  overflow: hidden;\r\n}\r\n</style>\r\n\r\n"]}]}