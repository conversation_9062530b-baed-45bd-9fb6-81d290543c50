{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\home.vue?vue&type=style&index=0&id=7eb2bc79&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\home.vue", "mtime": 1642386765415}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouY29udGVudCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIG1pbi1oZWlnaHQ6IDUwMHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIC5tYWluLXRleHR7DQogICAgZm9udC1zaXplOiAzOHB4Ow0KICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgIG1hcmdpbi10b3A6IDE1JTsNCiAgfQ0KICAudGV4dCB7DQogICAgZm9udC1zaXplOiAyNHB4Ow0KICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgIGNvbG9yOiAjMzMzOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["home.vue"], "names": [], "mappings": ";AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n<div class=\"content\">\r\n\r\n<div class=\"text main-text\">欢迎使用 {{this.$project.projectName}}</div>\r\n\r\n</div>\r\n</template>\r\n<script>\r\nimport router from '@/router/router-static'\r\nexport default {\r\n  mounted(){\r\n    this.init();\r\n  },\r\n  methods:{\r\n    init(){\r\n        if(this.$storage.get('Token')){\r\n        this.$http({\r\n            url: `${this.$storage.get('sessionTable')}/session`,\r\n            method: \"get\"\r\n        }).then(({ data }) => {\r\n            if (data && data.code != 0) {\r\n            router.push({ name: 'login' })\r\n            }\r\n        });\r\n        }else{\r\n            router.push({ name: 'login' })\r\n        }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n  min-height: 500px;\r\n  text-align: center;\r\n  .main-text{\r\n    font-size: 38px;\r\n    font-weight: bold;\r\n    margin-top: 15%;\r\n  }\r\n  .text {\r\n    font-size: 24px;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n}\r\n</style>"]}]}