{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\404.vue?vue&type=style&index=0&id=6b3aeb0e&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\404.vue", "mtime": 1750583733818}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouY29udGVudCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIG1pbi1oZWlnaHQ6IDkwMHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIC5iYWNrZ3JvdWQgew0KICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICB3aWR0aDogMjAwcHg7DQogICAgaGVpZ2h0OiAyMDBweDsNCiAgICBtYXJnaW4tdG9wOiA4MHB4Ow0KICB9DQogIC5tYWluLXRleHR7DQogICAgbWFyZ2luLXRvcDogODBweDsNCiAgfQ0KICAudGV4dCB7DQogICAgZm9udC1zaXplOiAyNHB4Ow0KICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgIGNvbG9yOiAjMzMzOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["404.vue"], "names": [], "mappings": ";AAqBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "404.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <img class=\"backgroud\" src=\"@/assets/img/404.png\" alt>\r\n    <div class=\"text main-text\">出错�?..页面失踪�?/div>\r\n    <div>\r\n      <el-button class=\"text\" @click=\"back()\" type=\"text\" icon=\"el-icon-back\">返回</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  methods: {\r\n    back() {\r\n      window.history.length > 1 ? this.$router.go(-1) : this.$router.push(\"/\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n  min-height: 900px;\r\n  text-align: center;\r\n  .backgroud {\r\n    display: inline-block;\r\n    width: 200px;\r\n    height: 200px;\r\n    margin-top: 80px;\r\n  }\r\n  .main-text{\r\n    margin-top: 80px;\r\n  }\r\n  .text {\r\n    font-size: 24px;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n}\r\n</style>\r\n\r\n\r\n"]}]}