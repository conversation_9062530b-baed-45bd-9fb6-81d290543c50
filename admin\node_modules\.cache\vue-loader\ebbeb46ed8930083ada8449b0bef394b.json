{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdiOrder\\add-or-update.vue?vue&type=style&index=0&id=25e2f0ee&lang=scss", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdiOrder\\add-or-update.vue", "mtime": 1750585694096}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZWRpdG9yew0KICBoZWlnaHQ6IDUwMHB4Ow0KDQogICYgOjp2LWRlZXAgLnFsLWNvbnRhaW5lciB7DQoJICBoZWlnaHQ6IDMxMHB4Ow0KICB9DQp9DQouYW1hcC13cmFwcGVyIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogNTAwcHg7DQp9DQouc2VhcmNoLWJveCB7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCn0NCi5hZGRFZGl0LWJsb2NrIHsNCgltYXJnaW46IC0xMHB4Ow0KfQ0KLmRldGFpbC1mb3JtLWNvbnRlbnQgew0KCXBhZGRpbmc6IDEycHg7DQp9DQouYnRuIC5lbC1idXR0b24gew0KICBwYWRkaW5nOiAwOw0KfQ=="}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AA4sBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/changdiOrder", "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"addEdit-block\">\r\n        <el-form\r\n                class=\"detail-form-content\"\r\n                ref=\"ruleForm\"\r\n                :model=\"ruleForm\"\r\n                :rules=\"rules\"\r\n                label-width=\"80px\"\r\n                :style=\"{backgroundColor:addEditForm.addEditBoxColor}\">\r\n            <el-row>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='changdi'\">\r\n                    <el-form-item class=\"select\" v-if=\"type!='info'\"  label=\"场地\" prop=\"changdiId\">\r\n                        <el-select v-model=\"ruleForm.changdiId\" filterable placeholder=\"请选择场地\" @change=\"changdiChange\">\r\n                            <el-option\r\n                                    v-for=\"(item,index) in changdiOptions\"\r\n                                    v-bind:key=\"item.id\"\r\n                                    :label=\"item.changdiName\"\r\n                                    :value=\"item.id\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"场地编号\" prop=\"changdiUuidNumber\">\r\n                        <el-input v-model=\"changdiForm.changdiUuidNumber\"\r\n                                  placeholder=\"场地编号\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"场地编号\" prop=\"changdiUuidNumber\">\r\n                            <el-input v-model=\"ruleForm.changdiUuidNumber\"\r\n                                      placeholder=\"场地编号\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"场地名称\" prop=\"changdiName\">\r\n                        <el-input v-model=\"changdiForm.changdiName\"\r\n                                  placeholder=\"场地名称\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"场地名称\" prop=\"changdiName\">\r\n                            <el-input v-model=\"ruleForm.changdiName\"\r\n                                      placeholder=\"场地名称\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"24\" v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"upload\" v-if=\"type!='info' && !ro.changdiPhoto\" label=\"场地照片\" prop=\"changdiPhoto\">\r\n                        <img style=\"margin-right:20px;\" v-bind:key=\"index\" v-for=\"(item,index) in (changdiForm.changdiPhoto || '').split(',')\" :src=\"item\" width=\"100\" height=\"100\">\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item v-if=\"ruleForm.changdiPhoto\" label=\"场地照片\" prop=\"changdiPhoto\">\r\n                            <img style=\"margin-right:20px;\" v-bind:key=\"index\" v-for=\"(item,index) in (ruleForm.changdiPhoto || '').split(',')\" :src=\"item\" width=\"100\" height=\"100\">\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"场地类型\" prop=\"changdiValue\">\r\n                        <el-input v-model=\"changdiForm.changdiValue\"\r\n                                  placeholder=\"场地类型\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"场地类型\" prop=\"changdiValue\">\r\n                            <el-input v-model=\"ruleForm.changdiValue\"\r\n                                      placeholder=\"场地类型\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"场地原价\" prop=\"changdiOldMoney\">\r\n                        <el-input v-model=\"changdiForm.changdiOldMoney\"\r\n                                  placeholder=\"场地原价\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"场地原价\" prop=\"changdiOldMoney\">\r\n                            <el-input v-model=\"ruleForm.changdiOldMoney\"\r\n                                      placeholder=\"场地原价\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"时间段\" prop=\"shijianduan\">\r\n                        <el-input v-model=\"changdiForm.shijianduan\"\r\n                                  placeholder=\"时间段\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"时间段\" prop=\"shijianduan\">\r\n                            <el-input v-model=\"ruleForm.shijianduan\"\r\n                                      placeholder=\"时间段\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <!--<el-col :span=\"12\"  v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"人数\" prop=\"shijianduanRen\">\r\n                        <el-input v-model=\"changdiForm.shijianduanRen\"\r\n                                  placeholder=\"人数\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"人数\" prop=\"shijianduanRen\">\r\n                            <el-input v-model=\"ruleForm.shijianduanRen\"\r\n                                      placeholder=\"人数\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>-->\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"点击次数\" prop=\"changdiClicknum\">\r\n                        <el-input v-model=\"changdiForm.changdiClicknum\"\r\n                                  placeholder=\"点击次数\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"点击次数\" prop=\"changdiClicknum\">\r\n                            <el-input v-model=\"ruleForm.changdiClicknum\"\r\n                                      placeholder=\"点击次数\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"半全场\" prop=\"banquanValue\">\r\n                        <el-input v-model=\"changdiForm.banquanValue\"\r\n                                  placeholder=\"半全场\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"半全场\" prop=\"banquanValue\">\r\n                            <el-input v-model=\"ruleForm.banquanValue\"\r\n                                      placeholder=\"半全场\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='changdi' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"推荐吃饭地点\" prop=\"tuijian\">\r\n                        <el-input v-model=\"changdiForm.tuijian\"\r\n                                  placeholder=\"推荐吃饭地点\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"推荐吃饭地点\" prop=\"tuijian\">\r\n                            <el-input v-model=\"ruleForm.tuijian\"\r\n                                      placeholder=\"推荐吃饭地点\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='yonghu'\">\r\n                    <el-form-item class=\"select\" v-if=\"type!='info'\"  label=\"用户\" prop=\"yonghuId\">\r\n                        <el-select v-model=\"ruleForm.yonghuId\" filterable placeholder=\"请选择用户\" @change=\"yonghuChange\">\r\n                            <el-option\r\n                                    v-for=\"(item,index) in yonghuOptions\"\r\n                                    v-bind:key=\"item.id\"\r\n                                    :label=\"item.yonghuName\"\r\n                                    :value=\"item.id\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='yonghu' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"用户姓名\" prop=\"yonghuName\">\r\n                        <el-input v-model=\"yonghuForm.yonghuName\"\r\n                                  placeholder=\"用户姓名\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"用户姓名\" prop=\"yonghuName\">\r\n                            <el-input v-model=\"ruleForm.yonghuName\"\r\n                                      placeholder=\"用户姓名\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='yonghu' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"用户手机号\" prop=\"yonghuPhone\">\r\n                        <el-input v-model=\"yonghuForm.yonghuPhone\"\r\n                                  placeholder=\"用户手机号\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"用户手机号\" prop=\"yonghuPhone\">\r\n                            <el-input v-model=\"ruleForm.yonghuPhone\"\r\n                                      placeholder=\"用户手机号\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='yonghu' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"用户身份证号\" prop=\"yonghuIdNumber\">\r\n                        <el-input v-model=\"yonghuForm.yonghuIdNumber\"\r\n                                  placeholder=\"用户身份证号\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"用户身份证号\" prop=\"yonghuIdNumber\">\r\n                            <el-input v-model=\"ruleForm.yonghuIdNumber\"\r\n                                      placeholder=\"用户身份证号\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"24\" v-if=\"sessionTable !='yonghu' \">\r\n                    <el-form-item class=\"upload\" v-if=\"type!='info' && !ro.yonghuPhoto\" label=\"用户头像\" prop=\"yonghuPhoto\">\r\n                        <img style=\"margin-right:20px;\" v-bind:key=\"index\" v-for=\"(item,index) in (yonghuForm.yonghuPhoto || '').split(',')\" :src=\"item\" width=\"100\" height=\"100\">\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item v-if=\"ruleForm.yonghuPhoto\" label=\"用户头像\" prop=\"yonghuPhoto\">\r\n                            <img style=\"margin-right:20px;\" v-bind:key=\"index\" v-for=\"(item,index) in (ruleForm.yonghuPhoto || '').split(',')\" :src=\"item\" width=\"100\" height=\"100\">\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\"  v-if=\"sessionTable !='yonghu' \">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"电子邮箱\" prop=\"yonghuEmail\">\r\n                        <el-input v-model=\"yonghuForm.yonghuEmail\"\r\n                                  placeholder=\"电子邮箱\" clearable readonly></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"电子邮箱\" prop=\"yonghuEmail\">\r\n                            <el-input v-model=\"ruleForm.yonghuEmail\"\r\n                                      placeholder=\"电子邮箱\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <input id=\"updateId\" name=\"id\" type=\"hidden\">\r\n               <el-col :span=\"12\">\r\n                   <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"订单号\" prop=\"changdiOrderUuidNumber\">\r\n                       <el-input v-model=\"ruleForm.changdiOrderUuidNumber\"\r\n                                 placeholder=\"订单号\" clearable  :readonly=\"ro.changdiOrderUuidNumber\"></el-input>\r\n                   </el-form-item>\r\n                   <div v-else>\r\n                       <el-form-item class=\"input\" label=\"订单号\" prop=\"changdiOrderUuidNumber\">\r\n                           <el-input v-model=\"ruleForm.changdiOrderUuidNumber\"\r\n                                     placeholder=\"订单号\" readonly></el-input>\r\n                       </el-form-item>\r\n                   </div>\r\n               </el-col>\r\n            <input id=\"changdiId\" name=\"changdiId\" type=\"hidden\">\r\n            <input id=\"yonghuId\" name=\"yonghuId\" type=\"hidden\">\r\n               <el-col :span=\"12\">\r\n                   <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"实付价格\" prop=\"changdiOrderTruePrice\">\r\n                       <el-input v-model=\"ruleForm.changdiOrderTruePrice\"\r\n                                 placeholder=\"实付价格\" clearable  :readonly=\"ro.changdiOrderTruePrice\"></el-input>\r\n                   </el-form-item>\r\n                   <div v-else>\r\n                       <el-form-item class=\"input\" label=\"实付价格\" prop=\"changdiOrderTruePrice\">\r\n                           <el-input v-model=\"ruleForm.changdiOrderTruePrice\"\r\n                                     placeholder=\"实付价格\" readonly></el-input>\r\n                       </el-form-item>\r\n                   </div>\r\n               </el-col>\r\n                <el-col :span=\"12\">\r\n                    <el-form-item class=\"select\" v-if=\"type!='info'\"  label=\"订单类型\" prop=\"changdiOrderTypes\">\r\n                        <el-select v-model=\"ruleForm.changdiOrderTypes\" placeholder=\"请选择订单类型\">\r\n                            <el-option\r\n                                v-for=\"(item,index) in changdiOrderTypesOptions\"\r\n                                v-bind:key=\"item.codeIndex\"\r\n                                :label=\"item.indexName\"\r\n                                :value=\"item.codeIndex\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"订单类型\" prop=\"changdiOrderValue\">\r\n                        <el-input v-model=\"ruleForm.changdiOrderValue\"\r\n                            placeholder=\"订单类型\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n               <el-col :span=\"12\">\r\n                   <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"预约时间段\" prop=\"shijianduan\">\r\n                       <el-input v-model=\"ruleForm.shijianduan\"\r\n                                 placeholder=\"预约时间段\" clearable  :readonly=\"ro.shijianduan\"></el-input>\r\n                   </el-form-item>\r\n                   <div v-else>\r\n                       <el-form-item class=\"input\" label=\"预约时间段\" prop=\"shijianduan\">\r\n                           <el-input v-model=\"ruleForm.shijianduan\"\r\n                                     placeholder=\"预约时间段\" readonly></el-input>\r\n                       </el-form-item>\r\n                   </div>\r\n               </el-col>\r\n                <el-col :span=\"24\">\r\n                    <el-form-item v-if=\"type!='info'\"  class=\"input\" label=\"预约日期\" prop=\"buyTime\">\r\n                        <el-date-picker\r\n                                value-format=\"yyyy-MM-dd\"\r\n                                v-model=\"ruleForm.buyTime\"\r\n                                type=\"datetime\"\r\n                                placeholder=\"预约日期\">\r\n                        </el-date-picker>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item v-if=\"ruleForm.buyTime\" label=\"预约日期\" prop=\"buyTime\">\r\n                            <span v-html=\"ruleForm.buyTime\"></span>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n\r\n            </el-row>\r\n            <el-form-item class=\"btn\">\r\n                <el-button v-if=\"type!='info'\" type=\"primary\" class=\"btn-success\" @click=\"onSubmit\">提交</el-button>\r\n                <el-button v-if=\"type!='info'\" class=\"btn-close\" @click=\"back()\">取消</el-button>\r\n                <el-button v-if=\"type=='info'\" class=\"btn-close\" @click=\"back()\">返回</el-button>\r\n            </el-form-item>\r\n        </el-form>\r\n    </div>\r\n</template>\r\n<script>\r\n    import styleJs from \"../../../utils/style.js\";\r\n    // 数字，邮件，手机，url，身份证校验\r\n    import { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                addEditForm:null,\r\n                id: '',\r\n                type: '',\r\n                sessionTable : \"\",//登录账户所在表名\r\n                role : \"\",//权限\r\n                changdiForm: {},\r\n                yonghuForm: {},\r\n                ro:{\r\n                    changdiOrderUuidNumber: false,\r\n                    changdiId: false,\r\n                    yonghuId: false,\r\n                    changdiOrderTruePrice: false,\r\n                    changdiOrderTypes: false,\r\n                    shijianduan: false,\r\n                    buyTime: false,\r\n                    insertTime: false,\r\n                },\r\n                ruleForm: {\r\n                    changdiOrderUuidNumber: new Date().getTime(),\r\n                    changdiId: '',\r\n                    yonghuId: '',\r\n                    changdiOrderTruePrice: '',\r\n                    changdiOrderTypes: '',\r\n                    shijianduan: '',\r\n                    buyTime: '',\r\n                    insertTime: '',\r\n                },\r\n                changdiOrderTypesOptions : [],\r\n                changdiOptions : [],\r\n                yonghuOptions : [],\r\n                rules: {\r\n                   changdiOrderUuidNumber: [\r\n                              { required: true, message: '订单号不能为空', trigger: 'blur' },\r\n                          ],\r\n                   changdiId: [\r\n                              { required: true, message: '场地不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   yonghuId: [\r\n                              { required: true, message: '用户不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiOrderTruePrice: [\r\n                              { required: true, message: '实付价格不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[0-9]{0,6}(\\.[0-9]{1,2})?$/,\r\n                                  message: '只允许输入整数或小数2位的数字',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiOrderTypes: [\r\n                              { required: true, message: '订单类型不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   shijianduan: [\r\n                              { required: true, message: '预约时间段不能为空', trigger: 'blur' },\r\n                          ],\r\n                   buyTime: [\r\n                              { required: true, message: '预约日期不能为空', trigger: 'blur' },\r\n                          ],\r\n                   insertTime: [\r\n                              { required: true, message: '订单创建时间不能为空', trigger: 'blur' },\r\n                          ],\r\n                }\r\n            };\r\n        },\r\n        props: [\"parent\"],\r\n        computed: {\r\n        },\r\n        created() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n            if (this.role != \"管理员\"){\r\n            }\r\n            this.addEditForm = styleJs.addStyle();\r\n            this.addEditStyleChange()\r\n            this.addEditUploadStyleChange()\r\n            //获取下拉框信息\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=changdi_order_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.changdiOrderTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n\r\n         this.$http({\r\n             url: `changdi/page?page=1&limit=100`,\r\n             method: \"get\"\r\n         }).then(({ data }) => {\r\n             if (data && data.code === 0) {\r\n                this.changdiOptions = data.data.list;\r\n            }\r\n         });\r\n         this.$http({\r\n             url: `yonghu/page?page=1&limit=100`,\r\n             method: \"get\"\r\n         }).then(({ data }) => {\r\n             if (data && data.code === 0) {\r\n                this.yonghuOptions = data.data.list;\r\n            }\r\n         });\r\n\r\n        },\r\n        mounted() {\r\n        },\r\n        methods: {\r\n            // 下载\r\n            download(file){\r\n                window.open(`${file}`)\r\n            },\r\n            // 初始化\r\n            init(id,type) {\r\n                if (id) {\r\n                    this.id = id;\r\n                    this.type = type;\r\n                }\r\n                if(this.type=='info'||this.type=='else'){\r\n                    this.info(id);\r\n                }else if(this.type=='cross'){\r\n                    var obj = this.$storage.getObj('crossObj');\r\n                    for (var o in obj){\r\n\r\n                      if(o=='changdiOrderUuidNumber'){\r\n                          this.ruleForm.changdiOrderUuidNumber = obj[o];\r\n                          this.ro.changdiOrderUuidNumber = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiId'){\r\n                          this.ruleForm.changdiId = obj[o];\r\n                          this.ro.changdiId = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='yonghuId'){\r\n                          this.ruleForm.yonghuId = obj[o];\r\n                          this.ro.yonghuId = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiOrderTruePrice'){\r\n                          this.ruleForm.changdiOrderTruePrice = obj[o];\r\n                          this.ro.changdiOrderTruePrice = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiOrderTypes'){\r\n                          this.ruleForm.changdiOrderTypes = obj[o];\r\n                          this.ro.changdiOrderTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='shijianduan'){\r\n                          this.ruleForm.shijianduan = obj[o];\r\n                          this.ro.shijianduan = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='buyTime'){\r\n                          this.ruleForm.buyTime = obj[o];\r\n                          this.ro.buyTime = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='insertTime'){\r\n                          this.ruleForm.insertTime = obj[o];\r\n                          this.ro.insertTime = true;\r\n                          continue;\r\n                      }\r\n                    }\r\n                }\r\n                // 获取用户信息\r\n                this.$http({\r\n                    url:`${this.$storage.get(\"sessionTable\")}/session`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        var json = data.data;\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            changdiChange(id){\r\n                this.$http({\r\n                    url: `changdi/info/`+id,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.changdiForm = data.data;\r\n                    }\r\n                });\r\n            },\r\n            yonghuChange(id){\r\n                this.$http({\r\n                    url: `yonghu/info/`+id,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.yonghuForm = data.data;\r\n                    }\r\n                });\r\n            },\r\n            // 多级联动参数\r\n            info(id) {\r\n                this.$http({\r\n                    url: `changdiOrder/info/${id}`,\r\n                    method: 'get'\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.ruleForm = data.data;\r\n                        this.changdiChange(data.data.changdiId)\r\n                        this.yonghuChange(data.data.yonghuId)\r\n                        //解决前台上传图片后台不显示的问题\r\n                        let reg=new RegExp('../../../upload','g')//g代表全部\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            // 提交\r\n            onSubmit() {\r\n                this.$refs[\"ruleForm\"].validate(valid => {\r\n                    if (valid) {\r\n                        this.$http({\r\n                            url:`changdiOrder/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n                            method: \"post\",\r\n                            data: this.ruleForm\r\n                        }).then(({ data }) => {\r\n                            if (data && data.code === 0) {\r\n                                this.$message({\r\n                                    message: \"操作成功\",\r\n                                    type: \"success\",\r\n                                    duration: 1500,\r\n                                    onClose: () => {\r\n                                        this.parent.showFlag = true;\r\n                                        this.parent.addOrUpdateFlag = false;\r\n                                        this.parent.changdiOrderCrossAddOrUpdateFlag = false;\r\n                                        this.parent.search();\r\n                                        this.parent.contentStyleChange();\r\n                                    }\r\n                                });\r\n                            } else {\r\n                                this.$message.error(data.msg);\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            },\r\n            // 获取uuid\r\n            getUUID () {\r\n                return new Date().getTime();\r\n            },\r\n            // 返回\r\n            back() {\r\n                this.parent.showFlag = true;\r\n                this.parent.addOrUpdateFlag = false;\r\n                this.parent.changdiOrderCrossAddOrUpdateFlag = false;\r\n                this.parent.contentStyleChange();\r\n            },\r\n            //图片\r\n\r\n            addEditStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    // input\r\n                    document.querySelectorAll('.addEdit-block .input .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputFontColor\r\n                        el.style.fontSize = this.addEditForm.inputFontSize\r\n                        el.style.borderWidth = this.addEditForm.inputBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.inputBorderStyle\r\n                        el.style.borderColor = this.addEditForm.inputBorderColor\r\n                        el.style.borderRadius = this.addEditForm.inputBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.inputBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .input .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputLableColor\r\n                        el.style.fontSize = this.addEditForm.inputLableFontSize\r\n                    })\r\n                    // select\r\n                    document.querySelectorAll('.addEdit-block .select .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectFontColor\r\n                        el.style.fontSize = this.addEditForm.selectFontSize\r\n                        el.style.borderWidth = this.addEditForm.selectBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.selectBorderStyle\r\n                        el.style.borderColor = this.addEditForm.selectBorderColor\r\n                        el.style.borderRadius = this.addEditForm.selectBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.selectBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectLableColor\r\n                        el.style.fontSize = this.addEditForm.selectLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-select__caret').forEach(el=>{\r\n                        el.style.color = this.addEditForm.selectIconFontColor\r\n                        el.style.fontSize = this.addEditForm.selectIconFontSize\r\n                    })\r\n                    // date\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateFontColor\r\n                        el.style.fontSize = this.addEditForm.dateFontSize\r\n                        el.style.borderWidth = this.addEditForm.dateBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.dateBorderStyle\r\n                        el.style.borderColor = this.addEditForm.dateBorderColor\r\n                        el.style.borderRadius = this.addEditForm.dateBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.dateBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateLableColor\r\n                        el.style.fontSize = this.addEditForm.dateLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__icon').forEach(el=>{\r\n                        el.style.color = this.addEditForm.dateIconFontColor\r\n                        el.style.fontSize = this.addEditForm.dateIconFontSize\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                    })\r\n                    // upload\r\n                    let iconLineHeight = parseInt(this.addEditForm.uploadHeight) - parseInt(this.addEditForm.uploadBorderWidth) * 2 + 'px'\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload--picture-card').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.uploadHeight\r\n                        el.style.color = this.addEditForm.uploadLableColor\r\n                        el.style.fontSize = this.addEditForm.uploadLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-icon-plus').forEach(el=>{\r\n                        el.style.color = this.addEditForm.uploadIconFontColor\r\n                        el.style.fontSize = this.addEditForm.uploadIconFontSize\r\n                        el.style.lineHeight = iconLineHeight\r\n                        el.style.display = 'block'\r\n                    })\r\n                    // 多文本输入框\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-textarea__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaFontColor\r\n                        el.style.fontSize = this.addEditForm.textareaFontSize\r\n                        el.style.borderWidth = this.addEditForm.textareaBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.textareaBorderStyle\r\n                        el.style.borderColor = this.addEditForm.textareaBorderColor\r\n                        el.style.borderRadius = this.addEditForm.textareaBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.textareaBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-form-item__label').forEach(el=>{\r\n                        // el.style.lineHeight = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaLableColor\r\n                        el.style.fontSize = this.addEditForm.textareaLableFontSize\r\n                    })\r\n                    // 保存\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-success').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnSaveWidth\r\n                        el.style.height = this.addEditForm.btnSaveHeight\r\n                        el.style.color = this.addEditForm.btnSaveFontColor\r\n                        el.style.fontSize = this.addEditForm.btnSaveFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnSaveBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnSaveBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnSaveBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnSaveBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnSaveBgColor\r\n                    })\r\n                    // 返回\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-close').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnCancelWidth\r\n                        el.style.height = this.addEditForm.btnCancelHeight\r\n                        el.style.color = this.addEditForm.btnCancelFontColor\r\n                        el.style.fontSize = this.addEditForm.btnCancelFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnCancelBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnCancelBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnCancelBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnCancelBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnCancelBgColor\r\n                    })\r\n                })\r\n            },\r\n            addEditUploadStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload-list--picture-card .el-upload-list__item').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                })\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\">\r\n.editor{\r\n  height: 500px;\r\n\r\n  & ::v-deep .ql-container {\r\n\t  height: 310px;\r\n  }\r\n}\r\n.amap-wrapper {\r\n  width: 100%;\r\n  height: 500px;\r\n}\r\n.search-box {\r\n  position: absolute;\r\n}\r\n.addEdit-block {\r\n\tmargin: -10px;\r\n}\r\n.detail-form-content {\r\n\tpadding: 12px;\r\n}\r\n.btn .el-button {\r\n  padding: 0;\r\n}</style>\r\n\r\n\r\n\r\n"]}]}