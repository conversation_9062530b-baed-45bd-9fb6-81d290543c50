{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\pay.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\pay.vue", "mtime": 1642386765424}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["pay.vue"], "names": [], "mappings": ";AAoDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "pay.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"container\">\r\n    <el-alert title=\"确认支付前请先核对订单信息\" type=\"success\" :closable=\"false\"></el-alert>\r\n    <!-- <div class=\"top-content\">\r\n      <span>收款人</span>\r\n      <el-input style=\"width:300px\" v-model=\"name\" placeholder=\"收款人\"></el-input>\r\n      <span style=\"margin-left:20px\">收款账号</span>\r\n      <el-input style=\"width:300px\" v-model=\"account\" placeholder=\"收款账号\"></el-input>\r\n    </div> -->\r\n    <!-- <div class=\"price-content\">\r\n      <span>金额</span>\r\n      <span>￥99.0</span>\r\n    </div> -->\r\n    <div class=\"pay-type-content\">\r\n      <div class=\"pay-type-item\">\r\n        <el-radio v-model=\"type\" label=\"微信支付\"></el-radio>\r\n        <img src=\"@/assets/img/test/weixin.png\" alt>\r\n        <!-- <span>微信支付</span> -->\r\n      </div>\r\n      <div class=\"pay-type-item\">\r\n        <el-radio v-model=\"type\" label=\"支付宝支付\"></el-radio>\r\n        <img src=\"@/assets/img/test/zhifubao.png\" alt>\r\n        <!-- <span>支付宝支付</span> -->\r\n      </div>\r\n      <div class=\"pay-type-item\">\r\n        <el-radio v-model=\"type\" label=\"建设银行\"></el-radio>\r\n        <img src=\"@/assets/img/test/jianshe.png\" alt>\r\n        <!-- <span>建设银行</span> -->\r\n      </div>\r\n      <div class=\"pay-type-item\">\r\n        <el-radio v-model=\"type\" label=\"农业银行\"></el-radio>\r\n        <img src=\"@/assets/img/test/nongye.png\" alt>\r\n        <!-- <span>农业银行</span> -->\r\n      </div>\r\n      <div class=\"pay-type-item\">\r\n        <el-radio v-model=\"type\" label=\"中国银行\"></el-radio>\r\n        <img src=\"@/assets/img/test/zhongguo.png\" alt>\r\n        <!-- <span>中国银行</span> -->\r\n      </div>\r\n      <div class=\"pay-type-item\">\r\n        <el-radio v-model=\"type\" label=\"交通银行\"></el-radio>\r\n        <img src=\"@/assets/img/test/jiaotong.png\" alt>\r\n        <!-- <span>交通银行</span> -->\r\n      </div>\r\n    </div>\r\n    <div class=\"buton-content\">\r\n      <el-button @click=\"submitTap\" type=\"primary\">确认支付</el-button>\r\n      <el-button @click=\"back()\">返回</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n// import { Message } from \"element-ui\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      name: \"\",\r\n      account: \"\",\r\n      type: \"\",\r\n      table: \"\",\r\n      obj: \"\"\r\n    };\r\n  },\r\n  mounted() {\r\n    let table = this.$storage.get(\"paytable\");\r\n    let obj = this.$storage.getObj(\"payObject\");\r\n    this.table = table;\r\n    this.obj = obj;\r\n  },\r\n  methods: {\r\n    submitTap() {\r\n      // if (!this.name) {\r\n      //   this.$message.error(\"请输入收款人姓名\");\r\n      //   return;\r\n      // }\r\n      // if (!this.account) {\r\n      //   this.$message.error(\"请输入收款人账号\");\r\n      //   return;\r\n      // }\r\n      if (!this.type) {\r\n        this.$message.error(\"请选择支付方式\");\r\n        return;\r\n      }\r\n      this.$confirm(`确定支付?`, \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.obj.ispay = \"已支付\";\r\n        this.$http({\r\n          url: `${this.table}/update`,\r\n          method: \"post\",\r\n          data: this.obj\r\n        }).then(({ data }) => {\r\n          if (data && data.code === 0) {\r\n            this.$message({\r\n              message: \"支付成功\",\r\n              type: \"success\",\r\n              duration: 1500,\r\n              onClose: () => {\r\n                this.$router.go(-1);\r\n              }\r\n            });\r\n          } else {\r\n            this.$message.error(data.msg);\r\n          }\r\n        });\r\n      });\r\n    },\r\n    back(){\r\n      this.$router.go(-1);\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n  margin: 10px;\r\n  font-size: 14px;\r\n  span {\r\n    width: 60px;\r\n  }\r\n  .top-content {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 20px;\r\n  }\r\n  .price-content {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    padding-bottom: 20px;\r\n    padding: 20px;\r\n    border-bottom: 1px solid #eeeeee;\r\n    font-size: 20px;\r\n    font-weight: bold;\r\n    color: red;\r\n  }\r\n  .pay-type-content {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    flex-wrap: wrap;\r\n    span {\r\n      width: 100px;\r\n    }\r\n    .pay-type-item {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      width: 300px;\r\n      margin: 20px;\r\n      border: 1px solid #eeeeee;\r\n      padding: 20px;\r\n    }\r\n  }\r\n  .buton-content {\r\n    margin: 20px;\r\n  }\r\n}\r\n</style>\r\n"]}]}