{"remainingRequest": "D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\src\\components\\SvgIcon\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\src\\components\\SvgIcon\\index.vue", "mtime": 1642386765189}, {"path": "D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\springboot和vue体育馆预约系统黄粉\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTdmdJY29uJywKICBwcm9wczogewogICAgaWNvbkNsYXNzOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICBjbGFzc05hbWU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGljb25OYW1lOiBmdW5jdGlvbiBpY29uTmFtZSgpIHsKICAgICAgcmV0dXJuICIjaWNvbi0iLmNvbmNhdCh0aGlzLmljb25DbGFzcyk7CiAgICB9LAogICAgc3ZnQ2xhc3M6IGZ1bmN0aW9uIHN2Z0NsYXNzKCkgewogICAgICBpZiAodGhpcy5jbGFzc05hbWUpIHsKICAgICAgICByZXR1cm4gJ3N2Zy1pY29uICcgKyB0aGlzLmNsYXNzTmFtZTsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gJ3N2Zy1pY29uJzsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "props", "iconClass", "type", "String", "required", "className", "default", "computed", "iconName", "concat", "svgClass"], "sources": ["src/components/SvgIcon/index.vue"], "sourcesContent": ["<template>\r\n  <svg :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\r\n    <use :xlink:href=\"iconName\" />\r\n  </svg>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SvgIcon',\r\n  props: {\r\n    iconClass: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    className: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  computed: {\r\n    iconName() {\r\n      return `#icon-${this.iconClass}`\r\n    },\r\n    svgClass() {\r\n      if (this.className) {\r\n        return 'svg-icon ' + this.className\r\n      } else {\r\n        return 'svg-icon'\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.svg-icon {\r\n  width: 1em;\r\n  height: 1em;\r\n  vertical-align: -0.15em;\r\n  fill: currentColor;\r\n  overflow: hidden;\r\n}\r\n</style>\r\n"], "mappings": "AAOA;EACAA,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAC,MAAA;MACAG,OAAA;IACA;EACA;EACAC,QAAA;IACAC,QAAA,WAAAA,SAAA;MACA,gBAAAC,MAAA,MAAAR,SAAA;IACA;IACAS,QAAA,WAAAA,SAAA;MACA,SAAAL,SAAA;QACA,0BAAAA,SAAA;MACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}