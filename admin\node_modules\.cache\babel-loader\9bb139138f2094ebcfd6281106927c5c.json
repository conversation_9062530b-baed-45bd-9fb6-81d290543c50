{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\home.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\home.vue", "mtime": 1750583733888}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCByb3V0ZXIgZnJvbSAnQC9yb3V0ZXIvcm91dGVyLXN0YXRpYyc7CmV4cG9ydCBkZWZhdWx0IHsKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5pbml0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBpbml0OiBmdW5jdGlvbiBpbml0KCkgewogICAgICBpZiAodGhpcy4kc3RvcmFnZS5nZXQoJ1Rva2VuJykpIHsKICAgICAgICB0aGlzLiRodHRwKHsKICAgICAgICAgIHVybDogIiIuY29uY2F0KHRoaXMuJHN0b3JhZ2UuZ2V0KCdzZXNzaW9uVGFibGUnKSwgIi9zZXNzaW9uIiksCiAgICAgICAgICBtZXRob2Q6ICJnZXQiCiAgICAgICAgfSkudGhlbihmdW5jdGlvbiAoX3JlZikgewogICAgICAgICAgdmFyIGRhdGEgPSBfcmVmLmRhdGE7CiAgICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmNvZGUgIT0gMCkgewogICAgICAgICAgICByb3V0ZXIucHVzaCh7CiAgICAgICAgICAgICAgbmFtZTogJ2xvZ2luJwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICByb3V0ZXIucHVzaCh7CiAgICAgICAgICBuYW1lOiAnbG9naW4nCiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["router", "mounted", "init", "methods", "$storage", "get", "$http", "url", "concat", "method", "then", "_ref", "data", "code", "push", "name"], "sources": ["src/views/home.vue"], "sourcesContent": ["<template>\r\n<div class=\"content\">\r\n\r\n<div class=\"text main-text\">欢迎使用 {{this.$project.projectName}}</div>\r\n\r\n</div>\r\n</template>\r\n<script>\r\nimport router from '@/router/router-static'\r\nexport default {\r\n  mounted(){\r\n    this.init();\r\n  },\r\n  methods:{\r\n    init(){\r\n        if(this.$storage.get('Token')){\r\n        this.$http({\r\n            url: `${this.$storage.get('sessionTable')}/session`,\r\n            method: \"get\"\r\n        }).then(({ data }) => {\r\n            if (data && data.code != 0) {\r\n            router.push({ name: 'login' })\r\n            }\r\n        });\r\n        }else{\r\n            router.push({ name: 'login' })\r\n        }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n  min-height: 500px;\r\n  text-align: center;\r\n  .main-text{\r\n    font-size: 38px;\r\n    font-weight: bold;\r\n    margin-top: 15%;\r\n  }\r\n  .text {\r\n    font-size: 24px;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AAQA,OAAAA,MAAA;AACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,IAAA,WAAAA,KAAA;MACA,SAAAE,QAAA,CAAAC,GAAA;QACA,KAAAC,KAAA;UACAC,GAAA,KAAAC,MAAA,MAAAJ,QAAA,CAAAC,GAAA;UACAI,MAAA;QACA,GAAAC,IAAA,WAAAC,IAAA;UAAA,IAAAC,IAAA,GAAAD,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;YACAb,MAAA,CAAAc,IAAA;cAAAC,IAAA;YAAA;UACA;QACA;MACA;QACAf,MAAA,CAAAc,IAAA;UAAAC,IAAA;QAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}