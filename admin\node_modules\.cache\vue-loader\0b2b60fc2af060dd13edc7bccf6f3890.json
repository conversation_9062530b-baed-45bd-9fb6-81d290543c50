{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\home.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\home.vue", "mtime": 1642386765415}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgcm91dGVyIGZyb20gJ0Avcm91dGVyL3JvdXRlci1zdGF0aWMnDQpleHBvcnQgZGVmYXVsdCB7DQogIG1vdW50ZWQoKXsNCiAgICB0aGlzLmluaXQoKTsNCiAgfSwNCiAgbWV0aG9kczp7DQogICAgaW5pdCgpew0KICAgICAgICBpZih0aGlzLiRzdG9yYWdlLmdldCgnVG9rZW4nKSl7DQogICAgICAgIHRoaXMuJGh0dHAoew0KICAgICAgICAgICAgdXJsOiBgJHt0aGlzLiRzdG9yYWdlLmdldCgnc2Vzc2lvblRhYmxlJyl9L3Nlc3Npb25gLA0KICAgICAgICAgICAgbWV0aG9kOiAiZ2V0Ig0KICAgICAgICB9KS50aGVuKCh7IGRhdGEgfSkgPT4gew0KICAgICAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlICE9IDApIHsNCiAgICAgICAgICAgIHJvdXRlci5wdXNoKHsgbmFtZTogJ2xvZ2luJyB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgICAgfWVsc2V7DQogICAgICAgICAgICByb3V0ZXIucHVzaCh7IG5hbWU6ICdsb2dpbicgfSkNCiAgICAgICAgfQ0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["home.vue"], "names": [], "mappings": ";AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n<div class=\"content\">\r\n\r\n<div class=\"text main-text\">欢迎使用 {{this.$project.projectName}}</div>\r\n\r\n</div>\r\n</template>\r\n<script>\r\nimport router from '@/router/router-static'\r\nexport default {\r\n  mounted(){\r\n    this.init();\r\n  },\r\n  methods:{\r\n    init(){\r\n        if(this.$storage.get('Token')){\r\n        this.$http({\r\n            url: `${this.$storage.get('sessionTable')}/session`,\r\n            method: \"get\"\r\n        }).then(({ data }) => {\r\n            if (data && data.code != 0) {\r\n            router.push({ name: 'login' })\r\n            }\r\n        });\r\n        }else{\r\n            router.push({ name: 'login' })\r\n        }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n  min-height: 500px;\r\n  text-align: center;\r\n  .main-text{\r\n    font-size: 38px;\r\n    font-weight: bold;\r\n    margin-top: 15%;\r\n  }\r\n  .text {\r\n    font-size: 24px;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n}\r\n</style>"]}]}