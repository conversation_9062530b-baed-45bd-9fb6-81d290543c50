{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\forum\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\forum\\list.vue", "mtime": 1642386767096}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["AddOrUpdate", "styleJs", "data", "searchForm", "key", "sessionTable", "role", "form", "id", "forumName", "yonghuId", "usersId", "forumContent", "superIds", "forumTypes", "forumStateTypes", "insertTime", "updateTime", "createTime", "dataList", "pageIndex", "pageSize", "totalPage", "dataListLoading", "dataListSelections", "showFlag", "sfshVisiable", "shForm", "chartVisiable", "addOrUpdateFlag", "contents", "layouts", "echartsDate", "Date", "forumReplyDialogVisible", "forumReplyInfoDialogVisible", "forumTitle", "forumReplyContent", "forumReplyInfoContent", "forumData", "json_fields", "created", "listStyle", "init", "getDataList", "contentStyleChange", "mounted", "$storage", "get", "filters", "htmlfilter", "val", "replace", "components", "computed", "methods", "chartDialog", "_this2", "_this", "params", "dateFormat", "riqi", "getFullYear", "thisTable", "tableName", "sumColum", "date", "$nextTick", "statistic", "$echarts", "document", "getElementById", "$http", "url", "method", "then", "_ref", "code", "series", "yAxis", "for<PERSON>ach", "item", "index", "tempMap", "name", "legend", "type", "push", "option", "tooltip", "trigger", "axisPointer", "crossStyle", "color", "toolbox", "feature", "magicType", "show", "saveAsImage", "xAxis", "axisLabel", "formatter", "setOption", "window", "onresize", "resize", "$message", "message", "duration", "onClose", "search", "_ref2", "res", "p<PERSON><PERSON>y", "i", "length", "xinzi", "total", "value", "title", "text", "left", "radius", "center", "emphasis", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "contentSearchStyleChange", "contentBtnAdAllStyleChange", "contentSearchBtnStyleChange", "contentTableBtnStyleChange", "contentPageStyleChange", "_this3", "querySelectorAll", "el", "textAlign", "inputFontPosition", "style", "height", "inputHeight", "lineHeight", "inputFontColor", "fontSize", "inputFontSize", "borderWidth", "inputBorderWidth", "borderStyle", "inputBorderStyle", "borderColor", "inputBorderColor", "borderRadius", "inputBorderRadius", "backgroundColor", "inputBgColor", "inputTitle", "inputTitleColor", "inputTitleSize", "setTimeout", "inputIconColor", "_this4", "searchBtnHeight", "searchBtnFontColor", "searchBtnFontSize", "searchBtnBorderWidth", "searchBtnBorderStyle", "searchBtnBorderColor", "searchBtnBorderRadius", "searchBtnBgColor", "_this5", "btnAdAllHeight", "btnAdAllAddFontColor", "btnAdAllFontSize", "btnAdAllBorderWidth", "btnAdAllBorderStyle", "btnAdAllBorderColor", "btnAdAllBorderRadius", "btnAdAllAddBgColor", "btnAdAllDelFontColor", "btnAdAllDelBgColor", "btnAdAllWarnFontColor", "btnAdAllWarnBgColor", "rowStyle", "_ref3", "row", "rowIndex", "tableStripe", "tableStripeFontColor", "cellStyle", "_ref4", "tableStripeBgColor", "headerRowStyle", "_ref5", "tableHeaderFontColor", "headerCellStyle", "_ref6", "tableHeaderBgColor", "arr", "pageTotal", "pageSizes", "pagePrevNext", "pagePager", "pageJumper", "join", "pageEachNum", "_this6", "page", "limit", "sort", "yo<PERSON><PERSON><PERSON><PERSON>", "undefined", "yonghuPhone", "yonghuIdNumber", "_ref7", "list", "sizeChangeHandle", "currentChangeHandle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addOrUpdateHandler", "_this7", "crossAddOrUpdateFlag", "$refs", "addOrUpdate", "download", "file", "open", "delete<PERSON><PERSON><PERSON>", "_this8", "ids", "Number", "map", "$confirm", "concat", "confirmButtonText", "cancelButtonText", "_ref8", "error", "msg", "forumUploadSuccess", "_ref9", "forumUploadError", "openReplyForum", "_ref10", "forum", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yonghuEmail", "newMoney", "addtime", "see<PERSON><PERSON><PERSON><PERSON>nt", "deleteForumData", "_ref11", "forumReply", "alert", "_ref12"], "sources": ["src/views/modules/forum/list.vue"], "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"main-content\">\r\n        <el-dialog title=\"帖子回复详情\" :visible.sync=\"forumReplyDialogVisible\">\r\n            <el-dialog\r\n                    width=\"30%\"\r\n                    :title=\"forumReplyInfoContent\"\r\n                    :visible.sync=\"forumReplyInfoDialogVisible\"\r\n                    append-to-body>\r\n            </el-dialog>\r\n            <div class=\"demo-input-suffix\">\r\n                <span style=\"width: 20%\">帖子标题:</span><el-input v-model=\"forumTitle\" :disabled=\"true\" placeholder=\"帖子标题\" style=\"width: 80%\"></el-input>\r\n            </div>\r\n            <div class=\"demo-input-suffix\">\r\n                <span style=\"width: 20%\">帖子内容:</span><el-input v-model=\"forumContent\" :disabled=\"true\" placeholder=\"帖子内容\" style=\"width: 80%\" type=\"textarea\"></el-input>\r\n            </div>\r\n            <el-table :data=\"forumData\" height=\"250\">\r\n                <!--<el-table-column label=\"id\" width=\"40\"></el-table-column>-->\r\n                <el-table-column label=\"身份\" width=\"50\" fixed>\r\n                    <template slot-scope=\"scope\">\r\n                        <span v-if=\"scope.row.yonghuId\">\r\n                            用户\r\n                        </span>\r\n                        <span v-if=\"scope.row.usersId\">\r\n                            管理员\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"姓名\" width=\"80\">\r\n                        <template slot-scope=\"scope\">\r\n                        <span v-if=\"scope.row.yonghuId\">\r\n                            {{scope.row.yonghuName}}\r\n                        </span>\r\n                        <span v-if=\"scope.row.usersId\">\r\n                            管理员\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"手机号\" width=\"120\">\r\n                    <template slot-scope=\"scope\">\r\n                        <span v-if=\"scope.row.yonghuId\">\r\n                            {{scope.row.yonghuPhone}}\r\n                        </span>\r\n                        <span v-if=\"scope.row.usersId\">\r\n                            管理员\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"回复内容\" width=\"220\">\r\n                    <template slot-scope=\"scope\">\r\n                        {{scope.row.forumContent.length>20?(scope.row.forumContent.substring(0,20)+'...'):scope.row.forumContent}}\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column property=\"insertTime\" label=\"回帖时间\" width=\"160\"></el-table-column>\r\n                <el-table-column property=\"caozuo\" label=\"操作\" fixed=\"right\">\r\n                    <template slot-scope=\"scope\">\r\n                        <el-button type=\"info\"                          @click=\"seeForumContent(scope.row.forumContent)\">查看回帖详情</el-button>\r\n                        <el-button v-if=\"isAuth('forum','删除') && role == '管理员'\" type=\"danger\" @click=\"deleteForumData(scope.row.id)\" >删除</el-button>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"demo-input-suffix\">\r\n                <span style=\"width: 20%\">回帖内容:</span>\r\n                <el-input v-model=\"forumReplyContent\" placeholder=\"回帖内容\" style=\"width:80%\" type=\"textarea\"></el-input>\r\n            </div>\r\n\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"forumReplyDialogVisible = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"forumReply\">回 帖</el-button>\r\n            </div>\r\n        </el-dialog>\r\n        <!-- 条件查询 -->\r\n        <div v-if=\"showFlag\">\r\n            <el-form :inline=\"true\" :model=\"searchForm\" class=\"form-content\">\r\n                <el-row :gutter=\"20\" class=\"slt\" :style=\"{justifyContent:contents.searchBoxPosition=='1'?'flex-start':contents.searchBoxPosition=='2'?'center':'flex-end'}\">\r\n                 \r\n                     <el-form-item :label=\"contents.inputTitle == 1 ? '帖子标题' : ''\">\r\n                         <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.forumName\" placeholder=\"帖子标题\" clearable></el-input>\r\n                     </el-form-item>\r\n                                                                        \r\n                                         \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户姓名' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuName\" placeholder=\"用户姓名\" clearable></el-input>\r\n                    </el-form-item>\r\n                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户手机号' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuPhone\" placeholder=\"用户手机号\" clearable></el-input>\r\n                    </el-form-item>\r\n                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户身份证号' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuIdNumber\" placeholder=\"用户身份证号\" clearable></el-input>\r\n                    </el-form-item>\r\n                                                                                                                                            \r\n\r\n                    <el-form-item>\r\n                        <el-button type=\"success\" @click=\"search()\">查询<i class=\"el-icon-search el-icon--right\"/></el-button>\r\n                    </el-form-item>\r\n                </el-row>\r\n                <el-row class=\"ad\" :style=\"{justifyContent:contents.btnAdAllBoxPosition=='1'?'flex-start':contents.btnAdAllBoxPosition=='2'?'center':'flex-end'}\">\r\n                    <el-form-item>\r\n                        <el-button\r\n                                v-if=\"isAuth('forum','新增')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-plus\"\r\n                                @click=\"addOrUpdateHandler()\"\r\n                        >新增</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('forum','删除')\"\r\n                                :disabled=\"dataListSelections.length <= 0\"\r\n                                type=\"danger\"\r\n                                icon=\"el-icon-delete\"\r\n                                @click=\"deleteHandler()\"\r\n                        >删除</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('forum','报表')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-pie-chart\"\r\n                                @click=\"chartDialog()\"\r\n                        >报表</el-button>\r\n                        &nbsp;\r\n                        <a style=\"text-decoration:none\" class=\"el-button el-button--success\"\r\n                           v-if=\"isAuth('forum','导入导出')\"\r\n                           icon=\"el-icon-download\"\r\n                           href=\"http://localhost:8080/tiyuguan/upload/forumMuBan.xls\"\r\n                        >批量导入论坛数据模板</a>\r\n                        &nbsp;\r\n                        <el-upload\r\n                                v-if=\"isAuth('forum','导入导出')\"\r\n                                style=\"display: inline-block\"\r\n                                action=\"tiyuguan/file/upload\"\r\n                                :on-success=\"forumUploadSuccess\"\r\n                                :on-error=\"forumUploadError\"\r\n                                :show-file-list = false>\r\n                            <el-button\r\n                                    v-if=\"isAuth('forum','导入导出')\"\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-upload2\"\r\n                            >批量导入论坛数据</el-button>\r\n                        </el-upload>\r\n                        &nbsp;\r\n                        <!-- 导出excel -->\r\n                        <download-excel v-if=\"isAuth('forum','导入导出')\" style=\"display: inline-block\" class = \"export-excel-wrapper\" :data = \"dataList\" :fields = \"json_fields\" name = \"forum.xls\">\r\n                            <!-- 导出excel -->\r\n                            <el-button\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-download\"\r\n                            >导出</el-button>\r\n                        </download-excel>\r\n                        &nbsp;\r\n                    </el-form-item>\r\n                </el-row>\r\n            </el-form>\r\n            <div class=\"table-content\">\r\n                <el-table class=\"tables\" :size=\"contents.tableSize\" :show-header=\"contents.tableShowHeader\"\r\n                          :header-row-style=\"headerRowStyle\" :header-cell-style=\"headerCellStyle\"\r\n                          :border=\"contents.tableBorder\"\r\n                          :fit=\"contents.tableFit\"\r\n                          :stripe=\"contents.tableStripe\"\r\n                          :row-style=\"rowStyle\"\r\n                          :cell-style=\"cellStyle\"\r\n                          :style=\"{width: '100%',fontSize:contents.tableContentFontSize,color:contents.tableContentFontColor}\"\r\n                          v-if=\"isAuth('forum','查看')\"\r\n                          :data=\"dataList\"\r\n                          v-loading=\"dataListLoading\"\r\n                          @selection-change=\"selectionChangeHandler\">\r\n                    <el-table-column  v-if=\"contents.tableSelection\"\r\n                                      type=\"selection\"\r\n                                      header-align=\"center\"\r\n                                      align=\"center\"\r\n                                      width=\"50\">\r\n                    </el-table-column>\r\n                    <el-table-column label=\"索引\" v-if=\"contents.tableIndex\" type=\"index\" width=\"50\" />\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      header-align=\"center\"\r\n                                      label=\"身份\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.row.yonghuId\">\r\n                                用户\r\n                            </span>\r\n                            <span v-if=\"scope.row.usersId\">\r\n                                管理员\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      header-align=\"center\"\r\n                                      label=\"姓名\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.row.yonghuId\">\r\n                                {{scope.row.yonghuName}}\r\n                            </span>\r\n                            <span v-if=\"scope.row.usersId\">\r\n                                管理员\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      header-align=\"center\"\r\n                                      label=\"手机号\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.row.yonghuId\">\r\n                                {{scope.row.yonghuPhone}}\r\n                            </span>\r\n                            <span v-if=\"scope.row.usersId\">\r\n                                管理员\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      header-align=\"center\"\r\n                                      label=\"头像\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.row.yonghuId\">\r\n                                <div v-if=\"scope.row.yonghuPhoto\">\r\n                                    <img :src=\"scope.row.yonghuPhoto\" width=\"100\" height=\"100\">\r\n                                </div>\r\n                                <div v-else>无图片</div>\r\n                            </span>\r\n                            <span v-if=\"scope.row.usersId\">\r\n                                管理员\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"forumTypes\"\r\n                                      header-align=\"center\"\r\n                                      label=\"帖子类型\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.forumValue}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"forumName\"\r\n                                      header-align=\"center\"\r\n                                      label=\"帖子标题\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.forumName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"forumContent\"\r\n                                      header-align=\"center\"\r\n                                      label=\"帖子内容\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.forumContent.length>20?(scope.row.forumContent.substring(0,20)+'...'):scope.row.forumContent}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                      prop=\"insertTime\"\r\n                      header-align=\"center\"\r\n                      label=\"发帖时间\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.insertTime}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column width=\"300\" :align=\"contents.tableAlign\"\r\n                                     header-align=\"center\"\r\n                                     label=\"操作\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button v-if=\"isAuth('forum','查看')\" type=\"success\" icon=\"el-icon-tickets\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id,'info')\">详情</el-button>\r\n                            <el-button v-if=\"isAuth('forum','查看')\" type=\"primary\" icon=\"el-icon-edit\" size=\"mini\" @click=\"openReplyForum(scope.row.id,scope.row.forumName,scope.row.forumContent)\">查看论坛回复</el-button>\r\n\r\n                            <el-button v-if=\"isAuth('forum','修改')\" type=\"primary\" icon=\"el-icon-edit\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id)\">修改</el-button>\r\n                            <el-button v-if=\"isAuth('forum','删除')\" type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteHandler(scope.row.id)\">删除</el-button>\r\n\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n                <el-pagination\r\n                        clsss=\"pages\"\r\n                        :layout=\"layouts\"\r\n                        @size-change=\"sizeChangeHandle\"\r\n                        @current-change=\"currentChangeHandle\"\r\n                        :current-page=\"pageIndex\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"Number(contents.pageEachNum)\"\r\n                        :total=\"totalPage\"\r\n                        :small=\"contents.pageStyle\"\r\n                        class=\"pagination-content\"\r\n                        :background=\"contents.pageBtnBG\"\r\n                        :style=\"{textAlign:contents.pagePosition==1?'left':contents.pagePosition==2?'center':'right'}\"\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <!-- 添加/修改页面  将父组件的search方法传递给子组件-->\r\n        <add-or-update v-if=\"addOrUpdateFlag\" :parent=\"this\" ref=\"addOrUpdate\"></add-or-update>\r\n\r\n\r\n\r\n        <el-dialog title=\"统计报表\" :visible.sync=\"chartVisiable\" width=\"800\">\r\n            <el-date-picker\r\n                    v-model=\"echartsDate\"\r\n                    type=\"year\"\r\n                    placeholder=\"选择年\">\r\n            </el-date-picker>\r\n            <el-button @click=\"chartDialog()\">查询</el-button>\r\n            <div id=\"statistic\" style=\"width:100%;height:600px;\"></div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"chartVisiable = false\">返回</el-button>\r\n\t\t\t</span>\r\n        </el-dialog>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import AddOrUpdate from \"./add-or-update\";\r\n    import styleJs from \"../../../utils/style.js\";\r\n    export default {\r\n        data() {\r\n        return {\r\n            searchForm: {\r\n                key: \"\"\r\n            },\r\n            sessionTable : \"\",//登录账户所在表名\r\n            role : \"\",//权限\r\n    //级联表下拉框搜索条件\r\n    //当前表下拉框搜索条件\r\n            form:{\r\n                id : null,\r\n                forumName : null,\r\n                yonghuId : null,\r\n                usersId : null,\r\n                forumContent : null,\r\n                superIds : null,\r\n                forumTypes : null,\r\n                forumStateTypes : null,\r\n                insertTime : null,\r\n                updateTime : null,\r\n                createTime : null,\r\n            },\r\n            dataList: [],\r\n            pageIndex: 1,\r\n            pageSize: 10,\r\n            totalPage: 0,\r\n            dataListLoading: false,\r\n            dataListSelections: [],\r\n            showFlag: true,\r\n            sfshVisiable: false,\r\n            shForm: {},\r\n            chartVisiable: false,\r\n            addOrUpdateFlag:false,\r\n            contents:null,\r\n            layouts: '',\r\n\r\n            echartsDate: new Date(),//echarts的时间查询字段\r\n\r\n            forumReplyDialogVisible : false,//论坛回复模态框\r\n            forumReplyInfoDialogVisible : false,//论坛回复详情模态框\r\n            superIds : \"\",//帖子id\r\n            forumTitle : \"\",//帖子标题\r\n            forumContent : \"\",//帖子内容\r\n            forumReplyContent : \"\",//帖子回复内容\r\n            forumReplyInfoContent : \"\",//帖子某个回复详情 全\r\n            forumData : [],//论坛回复数据集合\r\n\r\n            //导出excel\r\n            json_fields: {\r\n                //级联表字段\r\n                     '用户姓名': 'yonghuName',\r\n                     '用户手机号': 'yonghuPhone',\r\n                     '用户身份证号': 'yonghuIdNumber',\r\n                     '用户头像': 'yonghuPhoto',\r\n                     '电子邮箱': 'yonghuEmail',\r\n                     '余额': 'newMoney',\r\n                     '角色': 'role',\r\n                     '新增时间': 'addtime',\r\n                //本表字段\r\n                     '帖子标题': \"forumName\",\r\n                     '父id': \"superIds\",\r\n                     '帖子类型': \"forumTypes\",\r\n                     '帖子状态': \"forumStateTypes\",\r\n                     '发帖时间': \"insertTime\",\r\n                     '修改时间': \"updateTime\",\r\n            },\r\n\r\n            };\r\n        },\r\n        created() {\r\n            this.contents = styleJs.listStyle();\r\n            this.init();\r\n            this.getDataList();\r\n            this.contentStyleChange()\r\n        },\r\n        mounted() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n        },\r\n        filters: {\r\n            htmlfilter: function (val) {\r\n                return val.replace(/<[^>]*>/g).replace(/undefined/g,'');\r\n            }\r\n        },\r\n        components: {\r\n            AddOrUpdate,\r\n        },\r\n        computed: {\r\n        },\r\n        methods: {\r\n            chartDialog() {\r\n                let _this = this;\r\n                let params = {\r\n                    dateFormat :\"%Y\"\r\n                    ,riqi :_this.echartsDate.getFullYear()\r\n                    ,thisTable : {//当前表\r\n                        tableName :\"shangdian_shouyin\"//当前表表名\r\n                        ,sumColum : 'shangdian_shouyin_true_price' //求和字段\r\n                        ,date : 'insert_time'//分组日期字段\r\n                        // ,string : 'name,leixing'//分组字符串字段\r\n                        // ,types : 'shangdian_shouyin_types'//分组下拉框字段\r\n                    }\r\n                    // ,joinTable : {//级联表（可以不存在）\r\n                    //     tableName :\"yuangong\"//级联表表名\r\n                    //     // ,date : 'insert_time'//分组日期字段\r\n                    //     ,string : 'yuangong_name'//分组字符串字段\r\n                    //     // ,types : 'insertTime'//分组下拉框字段\r\n                    // }\r\n                }\r\n                _this.chartVisiable = true;\r\n                _this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"barSum\",\r\n                        method: \"get\",\r\n                        params: params\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n\r\n\r\n                            //柱状图 求和 已成功使用\r\n                            //start\r\n                            let series = [];//具体数据值\r\n                            data.data.yAxis.forEach(function (item,index) {\r\n                                let tempMap = {};\r\n                                tempMap.name=data.data.legend[index];\r\n                                tempMap.type='bar';\r\n                                tempMap.data=item;\r\n                                series.push(tempMap);\r\n\r\n                            })\r\n\r\n                            var option = {\r\n                                tooltip: {\r\n                                    trigger: 'axis',\r\n                                    axisPointer: {\r\n                                        type: 'cross',\r\n                                        crossStyle: {\r\n                                            color: '#999'\r\n                                        }\r\n                                    }\r\n                                },\r\n                                toolbox: {\r\n                                    feature: {\r\n                                        // dataView: { show: true, readOnly: false },  // 数据查看\r\n                                        magicType: { show: true, type: ['line', 'bar'] },//切换图形展示方式\r\n                                        // restore: { show: true }, // 刷新\r\n                                        saveAsImage: { show: true }//保存\r\n                                    }\r\n                                },\r\n                                legend: {\r\n                                    data: data.data.legend//标题  可以点击导致某一列数据消失\r\n                                },\r\n                                xAxis: [\r\n                                    {\r\n                                        type: 'category',\r\n                                        name: '月份',\r\n                                        data: data.data.xAxis,\r\n                                        axisPointer: {\r\n                                            type: 'shadow'\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                yAxis: [\r\n                                    {\r\n                                        type: 'value',//不能改\r\n                                        name: '元',//y轴单位\r\n                                        axisLabel: {\r\n                                            formatter: '{value} 元' // 后缀\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                series:series//具体数据\r\n                            };\r\n                            // 使用刚指定的配置项和数据显示图表。\r\n                            statistic.setOption(option);\r\n                            //根据窗口的大小变动图表\r\n                            window.onresize = function () {\r\n                                statistic.resize();\r\n                            };\r\n                            //end\r\n\r\n\r\n\r\n                            //饼状图 原先自带的 未修改过\r\n                            //start\r\n                            /*let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表。\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图表\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }*/\r\n\r\n                            //end\r\n                        }else {\r\n                            this.$message({\r\n                                message: \"报表未查询到数据\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n                this.chartVisiable = !this.chartVisiable;\r\n                this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"group/xinzitongji/xinzi\",\r\n                        method: \"get\",\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表。\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图表\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }\r\n                        }\r\n                    });\r\n                // xcolumn ycolumn\r\n                });\r\n            },\r\n            contentStyleChange() {\r\n                this.contentSearchStyleChange()\r\n                this.contentBtnAdAllStyleChange()\r\n                this.contentSearchBtnStyleChange()\r\n                this.contentTableBtnStyleChange()\r\n                this.contentPageStyleChange()\r\n            },\r\n            contentSearchStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-input__inner').forEach(el => {\r\n                        let textAlign = 'left'\r\n                        if(this.contents.inputFontPosition == 2)\r\n                            textAlign = 'center'\r\n                            if (this.contents.inputFontPosition == 3) textAlign = 'right'\r\n                                el.style.textAlign = textAlign\r\n                            el.style.height = this.contents.inputHeight\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                            el.style.color = this.contents.inputFontColor\r\n                            el.style.fontSize = this.contents.inputFontSize\r\n                            el.style.borderWidth = this.contents.inputBorderWidth\r\n                            el.style.borderStyle = this.contents.inputBorderStyle\r\n                            el.style.borderColor = this.contents.inputBorderColor\r\n                            el.style.borderRadius = this.contents.inputBorderRadius\r\n                            el.style.backgroundColor = this.contents.inputBgColor\r\n                    })\r\n                    if (this.contents.inputTitle) {\r\n                        document.querySelectorAll('.form-content .slt .el-form-item__label').forEach(el => {\r\n                            el.style.color = this.contents.inputTitleColor\r\n                            el.style.fontSize = this.contents.inputTitleSize\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }\r\n                    setTimeout(() => {\r\n                        document.querySelectorAll('.form-content .slt .el-input__prefix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__suffix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__icon').forEach(el => {\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }, 10 )\r\n                })\r\n            },\r\n            // 搜索按钮\r\n            contentSearchBtnStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.searchBtnHeight\r\n                        el.style.color = this.contents.searchBtnFontColor\r\n                        el.style.fontSize = this.contents.searchBtnFontSize\r\n                        el.style.borderWidth = this.contents.searchBtnBorderWidth\r\n                        el.style.borderStyle = this.contents.searchBtnBorderStyle\r\n                        el.style.borderColor = this.contents.searchBtnBorderColor\r\n                        el.style.borderRadius = this.contents.searchBtnBorderRadius\r\n                        el.style.backgroundColor = this.contents.searchBtnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 新增、批量删除\r\n            contentBtnAdAllStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .ad .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllAddFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllAddBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--danger').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllDelFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllDelBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--warning').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllWarnFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllWarnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 表格\r\n            rowStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {color: this.contents.tableStripeFontColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            cellStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {backgroundColor: this.contents.tableStripeBgColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            headerRowStyle({row, rowIndex}) {\r\n                return {color: this.contents.tableHeaderFontColor}\r\n            },\r\n            headerCellStyle({row, rowIndex}) {\r\n                return {backgroundColor: this.contents.tableHeaderBgColor}\r\n            },\r\n            // 表格按钮\r\n            contentTableBtnStyleChange() {\r\n                // this.$nextTick(()=>{\r\n                //   setTimeout(()=>{\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--success').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDetailFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDetailBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--primary').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnEditFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnEditBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--danger').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDelFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDelBgColor\r\n                //     })\r\n\r\n                //   }, 50)\r\n                // })\r\n            },\r\n            // 分页\r\n            contentPageStyleChange() {\r\n                let arr = []\r\n                if (this.contents.pageTotal) arr.push('total')\r\n                if (this.contents.pageSizes) arr.push('sizes')\r\n                if (this.contents.pagePrevNext) {\r\n                    arr.push('prev')\r\n                    if (this.contents.pagePager) arr.push('pager')\r\n                    arr.push('next')\r\n                }\r\n                if (this.contents.pageJumper) arr.push('jumper')\r\n                this.layouts = arr.join()\r\n                this.contents.pageEachNum = 10\r\n            },\r\n\r\n            init() {\r\n            },\r\n            search() {\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 获取数据列表\r\n            getDataList() {\r\n                this.dataListLoading = true;\r\n                let params = {\r\n                    page: this.pageIndex,\r\n                    limit: this.pageSize,\r\n                    sort: 'id',\r\n                    forumStateTypes:1\r\n                }\r\n\r\n                                         \r\n                if (this.searchForm.yonghuName!= '' && this.searchForm.yonghuName!= undefined) {\r\n                    params['yonghuName'] = '%' + this.searchForm.yonghuName + '%'\r\n                }\r\n                     \r\n                if (this.searchForm.yonghuPhone!= '' && this.searchForm.yonghuPhone!= undefined) {\r\n                    params['yonghuPhone'] = '%' + this.searchForm.yonghuPhone + '%'\r\n                }\r\n                     \r\n                if (this.searchForm.yonghuIdNumber!= '' && this.searchForm.yonghuIdNumber!= undefined) {\r\n                    params['yonghuIdNumber'] = '%' + this.searchForm.yonghuIdNumber + '%'\r\n                }\r\n                                                                                                                                                             \r\n                if (this.searchForm.forumName!= '' && this.searchForm.forumName!= undefined) {\r\n                    params['forumName'] = '%' + this.searchForm.forumName + '%'\r\n                }\r\n                                                                        \r\n                params['forumDelete'] = 1// 逻辑删除字段 1 未删除 2 删除\r\n\r\n\r\n                this.$http({\r\n                    url: \"forum/page\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.dataList = data.data.list;\r\n                        this.totalPage = data.data.total;\r\n                    }else{\r\n                        this.dataList = [];\r\n                        this.totalPage = 0;\r\n                    }\r\n                    this.dataListLoading = false;\r\n                });\r\n\r\n                //查询级联表搜索条件所有列表\r\n                //查询当前表搜索条件所有列表\r\n            },\r\n            //每页数\r\n            sizeChangeHandle(val) {\r\n                this.pageSize = val;\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 当前页\r\n            currentChangeHandle(val) {\r\n                this.pageIndex = val;\r\n                this.getDataList();\r\n            },\r\n            // 多选\r\n            selectionChangeHandler(val) {\r\n                this.dataListSelections = val;\r\n            },\r\n            // 添加/修改\r\n            addOrUpdateHandler(id, type) {\r\n                this.showFlag = false;\r\n                this.addOrUpdateFlag = true;\r\n                this.crossAddOrUpdateFlag = false;\r\n                if (type != 'info') {\r\n                    type = 'else';\r\n                }\r\n                this.$nextTick(() => {\r\n                    this.$refs.addOrUpdate.init(id, type);\r\n                });\r\n            },\r\n            // 下载\r\n            download(file) {\r\n                window.open(\" ${file} \")\r\n            },\r\n            // 删除\r\n            deleteHandler(id) {\r\n                var ids = id ? [Number(id)] : this.dataListSelections.map(item => {\r\n                    return Number(item.id);\r\n                });\r\n\r\n                this.$confirm(`确定进行[${id ? \"删除\" : \"批量删除\"}]操作?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: \"forum/delete\",\r\n                        method: \"post\",\r\n                        data: ids\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            this.$message({\r\n                                message: \"操作成功\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }else{\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n            // 导入功能上传文件成功后调用导入方法\r\n            forumUploadSuccess(data){\r\n                let _this = this;\r\n                _this.$http({\r\n                    url: \"forum/batchInsert?fileName=\" + data.file,\r\n                    method: \"get\"\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        _this.$message({\r\n                            message: \"导入论坛数据成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.search();\r\n                            }\r\n                        });\r\n                    }else{\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n\r\n            },\r\n            // 导入功能上传文件失败后调用导入方法\r\n            forumUploadError(data){\r\n                this.$message.error('上传失败');\r\n            },\r\n            // 打开回帖模态框\r\n            openReplyForum(id,forumName,forumContent) {\r\n                let _this = this;\r\n            // 当前帖子相关 start\r\n                _this.superIds = id;\r\n                _this.forumTitle = forumName;\r\n                _this.forumContent = forumContent;\r\n            // 当前帖子相关 end\r\n                _this.forumReplyContent = \"\";//帖子回复\r\n                _this.forumReplyDialogVisible = true;//论坛回复模态框\r\n                _this.forumReplyInfoDialogVisible = false;//论坛回复详情模态框\r\n\r\n\r\n                // 查看当前帖子的回复列表\r\n                let params = {\r\n                    page: 1,\r\n                    limit: 10000,\r\n                    sort: 'id',\r\n                    forumStateTypes:2,\r\n                    superIds:_this.superIds\r\n                }\r\n                _this.$http({\r\n                    url: \"forum/page\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({data}) => {\r\n                    if (data && data.code === 0) {\r\n                        _this.forumData = [];\r\n                        data.data.list.forEach(el=>{\r\n                            let forum  = {};\r\n                            forum.id = el.id;\r\n                            forum.forumName = el.forumName;\r\n                            forum.yonghuId = el.yonghuId;//---\r\n                            forum.yonghuName = el.yonghuName;\r\n                            forum.yonghuPhone = el.yonghuPhone;\r\n                            forum.yonghuIdNumber = el.yonghuIdNumber;\r\n                            forum.yonghuPhoto = el.yonghuPhoto;\r\n                            forum.yonghuEmail = el.yonghuEmail;\r\n                            forum.newMoney = el.newMoney;\r\n                            forum.createTime = el.createTime;\r\n                            forum.usersId = el.usersId;//---\r\n                            forum.role = el.role;\r\n                            forum.addtime = el.addtime;\r\n                            forum.forumContent = el.forumContent;\r\n                            forum.superIds = el.superIds;\r\n                            forum.forumTypes = el.forumTypes;\r\n                            forum.forumStateTypes = el.forumStateTypes;\r\n                            forum.insertTime = el.insertTime;\r\n                            forum.updateTime = el.updateTime;\r\n                            forum.createTime = el.createTime;\r\n                            _this.forumData.push(forum);\r\n                        })\r\n                    }\r\n                });\r\n            },\r\n\r\n            // 查看某个回复帖子的帖子内容全部\r\n            seeForumContent(forumContent) {\r\n                let _this = this;\r\n                _this.forumReplyInfoContent = forumContent;//帖子某个回复详情 全\r\n                _this.forumReplyInfoDialogVisible = true;//论坛回复详情模态框\r\n            },\r\n            // 删除数据\r\n            deleteForumData(id){\r\n                let _this = this;\r\n                let ids = [];\r\n                ids.push(Number(id));\r\n                _this.$http({\r\n                    url: \"forum/delete\",\r\n                    method: \"post\",\r\n                    data: ids\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        _this.$message({\r\n                            message: \"删除回帖成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.openReplyForum(_this.superIds,_this.forumTitle,_this.forumContent);\r\n                            }\r\n                        });\r\n                    }else{\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n\r\n            // 回帖\r\n            forumReply() {\r\n                let _this = this;\r\n                if(_this.forumReplyContent == \"\"){\r\n                    alert(\"请输入回帖内容\");\r\n                    return false;\r\n                }\r\n                let data = {\"superIds\":_this.superIds,\"forumStateTypes\":2,\"forumContent\":_this.forumReplyContent};\r\n                _this.$http({\r\n                    url:`forum/save`,\r\n                    method: \"post\",\r\n                    data: data\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        _this.$message({\r\n                            message: \"回帖成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.openReplyForum(_this.superIds,_this.forumTitle,_this.forumContent);\r\n                            }\r\n                        });\r\n                    } else {\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.slt {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .ad {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .pages {\r\n    & /deep/ el-pagination__sizes{\r\n      & /deep/ el-input__inner {\r\n        height: 22px;\r\n        line-height: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .el-button+.el-button {\r\n    margin:0;\r\n  }\r\n\r\n  .tables {\r\n\t& /deep/ .el-button--success {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(121, 244, 164, 0.54);\r\n\t}\r\n\r\n\t& /deep/ .el-button--primary {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(169, 115, 232, 0.65);\r\n\t}\r\n\r\n\t& /deep/ .el-button--danger {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(241, 120, 186, 1);\r\n\t}\r\n\r\n    & /deep/ .el-button {\r\n      margin: 4px;\r\n    }\r\n  }\r\n</style>\r\n\r\n\r\n"], "mappings": ";;;;;;;;;;;AA2TA,OAAAA,WAAA;AACA,OAAAC,OAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;QACAC,GAAA;MACA;MACAC,YAAA;MAAA;MACAC,IAAA;MAAA;MACA;MACA;MACAC,IAAA;QACAC,EAAA;QACAC,SAAA;QACAC,QAAA;QACAC,OAAA;QACAC,YAAA;QACAC,QAAA;QACAC,UAAA;QACAC,eAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;MACA;MACAC,QAAA;MACAC,SAAA;MACAC,QAAA;MACAC,SAAA;MACAC,eAAA;MACAC,kBAAA;MACAC,QAAA;MACAC,YAAA;MACAC,MAAA;MACAC,aAAA;MACAC,eAAA;MACAC,QAAA;MACAC,OAAA;MAEAC,WAAA,MAAAC,IAAA;MAAA;;MAEAC,uBAAA;MAAA;MACAC,2BAAA;MAAA;MACAtB,QAAA;MAAA;MACAuB,UAAA;MAAA;MACAxB,YAAA;MAAA;MACAyB,iBAAA;MAAA;MACAC,qBAAA;MAAA;MACAC,SAAA;MAAA;;MAEA;MACAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAX,QAAA,GAAA7B,OAAA,CAAAyC,SAAA;IACA,KAAAC,IAAA;IACA,KAAAC,WAAA;IACA,KAAAC,kBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAzC,YAAA,QAAA0C,QAAA,CAAAC,GAAA;IACA,KAAA1C,IAAA,QAAAyC,QAAA,CAAAC,GAAA;EAEA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,OAAA,aAAAA,OAAA;IACA;EACA;EACAC,UAAA;IACArD,WAAA,EAAAA;EACA;EACAsD,QAAA,GACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,KAAA;MACA,IAAAC,MAAA;QACAC,UAAA;QACAC,IAAA,EAAAH,KAAA,CAAA1B,WAAA,CAAA8B,WAAA;QACAC,SAAA;UAAA;UACAC,SAAA;UAAA;UACAC,QAAA;UAAA;UACAC,IAAA;UACA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAR,KAAA,CAAA9B,aAAA;MACA8B,KAAA,CAAAS,SAAA;QACA,IAAAC,SAAA,GAAAX,MAAA,CAAAY,QAAA,CAAA1B,IAAA,CAAA2B,QAAA,CAAAC,cAAA;QACAd,MAAA,CAAAe,KAAA;UACAC,GAAA;UACAC,MAAA;UACAf,MAAA,EAAAA;QACA,GAAAgB,IAAA,WAAAC,IAAA;UAAA,IAAA1E,IAAA,GAAA0E,IAAA,CAAA1E,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2E,IAAA;YAGA;YACA;YACA,IAAAC,MAAA;YACA5E,IAAA,CAAAA,IAAA,CAAA6E,KAAA,CAAAC,OAAA,WAAAC,IAAA,EAAAC,KAAA;cACA,IAAAC,OAAA;cACAA,OAAA,CAAAC,IAAA,GAAAlF,IAAA,CAAAA,IAAA,CAAAmF,MAAA,CAAAH,KAAA;cACAC,OAAA,CAAAG,IAAA;cACAH,OAAA,CAAAjF,IAAA,GAAA+E,IAAA;cACAH,MAAA,CAAAS,IAAA,CAAAJ,OAAA;YAEA;YAEA,IAAAK,MAAA;cACAC,OAAA;gBACAC,OAAA;gBACAC,WAAA;kBACAL,IAAA;kBACAM,UAAA;oBACAC,KAAA;kBACA;gBACA;cACA;cACAC,OAAA;gBACAC,OAAA;kBACA;kBACAC,SAAA;oBAAAC,IAAA;oBAAAX,IAAA;kBAAA;kBAAA;kBACA;kBACAY,WAAA;oBAAAD,IAAA;kBAAA;gBACA;cACA;cACAZ,MAAA;gBACAnF,IAAA,EAAAA,IAAA,CAAAA,IAAA,CAAAmF,MAAA;cACA;cACAc,KAAA,GACA;gBACAb,IAAA;gBACAF,IAAA;gBACAlF,IAAA,EAAAA,IAAA,CAAAA,IAAA,CAAAiG,KAAA;gBACAR,WAAA;kBACAL,IAAA;gBACA;cACA,EACA;cACAP,KAAA,GACA;gBACAO,IAAA;gBAAA;gBACAF,IAAA;gBAAA;gBACAgB,SAAA;kBACAC,SAAA;gBACA;cACA,EACA;cACAvB,MAAA,EAAAA,MAAA;YACA;YACA;YACAV,SAAA,CAAAkC,SAAA,CAAAd,MAAA;YACA;YACAe,MAAA,CAAAC,QAAA;cACApC,SAAA,CAAAqC,MAAA;YACA;YACA;;YAIA;YACA;YACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;YAEA;UACA;YACAhD,MAAA,CAAAiD,QAAA;cACAC,OAAA;cACArB,IAAA;cACAsB,QAAA;cACAC,OAAA,WAAAA,QAAA;gBACApD,MAAA,CAAAqD,MAAA;cACA;YACA;UACA;QACA;MACA;MACA,KAAAlF,aAAA,SAAAA,aAAA;MACA,KAAAuC,SAAA;QACA,IAAAC,SAAA,GAAAX,MAAA,CAAAY,QAAA,CAAA1B,IAAA,CAAA2B,QAAA,CAAAC,cAAA;QACAd,MAAA,CAAAe,KAAA;UACAC,GAAA;UACAC,MAAA;QACA,GAAAC,IAAA,WAAAoC,KAAA;UAAA,IAAA7G,IAAA,GAAA6G,KAAA,CAAA7G,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2E,IAAA;YACA,IAAAmC,GAAA,GAAA9G,IAAA,CAAAA,IAAA;YACA,IAAAiG,KAAA;YACA,IAAApB,KAAA;YACA,IAAAkC,MAAA;YACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,GAAA,CAAAG,MAAA,EAAAD,CAAA;cACAf,KAAA,CAAAZ,IAAA,CAAAyB,GAAA,CAAAE,CAAA,EAAAE,KAAA;cACArC,KAAA,CAAAQ,IAAA,CAAAyB,GAAA,CAAAE,CAAA,EAAAG,KAAA;cACAJ,MAAA,CAAA1B,IAAA;gBACA+B,KAAA,EAAAN,GAAA,CAAAE,CAAA,EAAAG,KAAA;gBACAjC,IAAA,EAAA4B,GAAA,CAAAE,CAAA,EAAAE;cACA;cACA,IAAA5B,MAAA;cACAA,MAAA;gBACA+B,KAAA;kBACAC,IAAA;kBACAC,IAAA;gBACA;gBACAhC,OAAA;kBACAC,OAAA;kBACAW,SAAA;gBACA;gBACAvB,MAAA;kBACAQ,IAAA;kBACAoC,MAAA;kBACAC,MAAA;kBACAzH,IAAA,EAAA+G,MAAA;kBACAW,QAAA;oBACAC,SAAA;sBACAC,UAAA;sBACAC,aAAA;sBACAC,WAAA;oBACA;kBACA;gBACA;cACA;cACA;cACA5D,SAAA,CAAAkC,SAAA,CAAAd,MAAA;cACA;cACAe,MAAA,CAAAC,QAAA;gBACApC,SAAA,CAAAqC,MAAA;cACA;YACA;UACA;QACA;QACA;MACA;IACA;IACA5D,kBAAA,WAAAA,mBAAA;MACA,KAAAoF,wBAAA;MACA,KAAAC,0BAAA;MACA,KAAAC,2BAAA;MACA,KAAAC,0BAAA;MACA,KAAAC,sBAAA;IACA;IACAJ,wBAAA,WAAAA,yBAAA;MAAA,IAAAK,MAAA;MACA,KAAAnE,SAAA;QACAG,QAAA,CAAAiE,gBAAA,wCAAAvD,OAAA,WAAAwD,EAAA;UACA,IAAAC,SAAA;UACA,IAAAH,MAAA,CAAAxG,QAAA,CAAA4G,iBAAA,OACAD,SAAA;UACA,IAAAH,MAAA,CAAAxG,QAAA,CAAA4G,iBAAA,OAAAD,SAAA;UACAD,EAAA,CAAAG,KAAA,CAAAF,SAAA,GAAAA,SAAA;UACAD,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAAN,MAAA,CAAAxG,QAAA,CAAA+G,WAAA;UACAL,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAxG,QAAA,CAAA+G,WAAA;UACAL,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAyC,MAAA,CAAAxG,QAAA,CAAAiH,cAAA;UACAP,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAAV,MAAA,CAAAxG,QAAA,CAAAmH,aAAA;UACAT,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAZ,MAAA,CAAAxG,QAAA,CAAAqH,gBAAA;UACAX,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAd,MAAA,CAAAxG,QAAA,CAAAuH,gBAAA;UACAb,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAhB,MAAA,CAAAxG,QAAA,CAAAyH,gBAAA;UACAf,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAlB,MAAA,CAAAxG,QAAA,CAAA2H,iBAAA;UACAjB,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAApB,MAAA,CAAAxG,QAAA,CAAA6H,YAAA;QACA;QACA,IAAArB,MAAA,CAAAxG,QAAA,CAAA8H,UAAA;UACAtF,QAAA,CAAAiE,gBAAA,4CAAAvD,OAAA,WAAAwD,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAyC,MAAA,CAAAxG,QAAA,CAAA+H,eAAA;YACArB,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAAV,MAAA,CAAAxG,QAAA,CAAAgI,cAAA;YACAtB,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAxG,QAAA,CAAA+G,WAAA;UACA;QACA;QACAkB,UAAA;UACAzF,QAAA,CAAAiE,gBAAA,yCAAAvD,OAAA,WAAAwD,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAyC,MAAA,CAAAxG,QAAA,CAAAkI,cAAA;YACAxB,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAxG,QAAA,CAAA+G,WAAA;UACA;UACAvE,QAAA,CAAAiE,gBAAA,yCAAAvD,OAAA,WAAAwD,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAyC,MAAA,CAAAxG,QAAA,CAAAkI,cAAA;YACAxB,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAxG,QAAA,CAAA+G,WAAA;UACA;UACAvE,QAAA,CAAAiE,gBAAA,uCAAAvD,OAAA,WAAAwD,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAxG,QAAA,CAAA+G,WAAA;UACA;QACA;MACA;IACA;IACA;IACAV,2BAAA,WAAAA,4BAAA;MAAA,IAAA8B,MAAA;MACA,KAAA9F,SAAA;QACAG,QAAA,CAAAiE,gBAAA,2CAAAvD,OAAA,WAAAwD,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAAqB,MAAA,CAAAnI,QAAA,CAAAoI,eAAA;UACA1B,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAoE,MAAA,CAAAnI,QAAA,CAAAqI,kBAAA;UACA3B,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAAiB,MAAA,CAAAnI,QAAA,CAAAsI,iBAAA;UACA5B,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAe,MAAA,CAAAnI,QAAA,CAAAuI,oBAAA;UACA7B,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAa,MAAA,CAAAnI,QAAA,CAAAwI,oBAAA;UACA9B,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAW,MAAA,CAAAnI,QAAA,CAAAyI,oBAAA;UACA/B,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAS,MAAA,CAAAnI,QAAA,CAAA0I,qBAAA;UACAhC,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAAO,MAAA,CAAAnI,QAAA,CAAA2I,gBAAA;QACA;MACA;IACA;IACA;IACAvC,0BAAA,WAAAA,2BAAA;MAAA,IAAAwC,MAAA;MACA,KAAAvG,SAAA;QACAG,QAAA,CAAAiE,gBAAA,0CAAAvD,OAAA,WAAAwD,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAA8B,MAAA,CAAA5I,QAAA,CAAA6I,cAAA;UACAnC,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAA6E,MAAA,CAAA5I,QAAA,CAAA8I,oBAAA;UACApC,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAA0B,MAAA,CAAA5I,QAAA,CAAA+I,gBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAwB,MAAA,CAAA5I,QAAA,CAAAgJ,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAsB,MAAA,CAAA5I,QAAA,CAAAiJ,mBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAoB,MAAA,CAAA5I,QAAA,CAAAkJ,mBAAA;UACAxC,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAkB,MAAA,CAAA5I,QAAA,CAAAmJ,oBAAA;UACAzC,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAAgB,MAAA,CAAA5I,QAAA,CAAAoJ,kBAAA;QACA;QACA5G,QAAA,CAAAiE,gBAAA,yCAAAvD,OAAA,WAAAwD,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAA8B,MAAA,CAAA5I,QAAA,CAAA6I,cAAA;UACAnC,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAA6E,MAAA,CAAA5I,QAAA,CAAAqJ,oBAAA;UACA3C,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAA0B,MAAA,CAAA5I,QAAA,CAAA+I,gBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAwB,MAAA,CAAA5I,QAAA,CAAAgJ,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAsB,MAAA,CAAA5I,QAAA,CAAAiJ,mBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAoB,MAAA,CAAA5I,QAAA,CAAAkJ,mBAAA;UACAxC,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAkB,MAAA,CAAA5I,QAAA,CAAAmJ,oBAAA;UACAzC,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAAgB,MAAA,CAAA5I,QAAA,CAAAsJ,kBAAA;QACA;QACA9G,QAAA,CAAAiE,gBAAA,0CAAAvD,OAAA,WAAAwD,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAA8B,MAAA,CAAA5I,QAAA,CAAA6I,cAAA;UACAnC,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAA6E,MAAA,CAAA5I,QAAA,CAAAuJ,qBAAA;UACA7C,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAA0B,MAAA,CAAA5I,QAAA,CAAA+I,gBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAwB,MAAA,CAAA5I,QAAA,CAAAgJ,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAsB,MAAA,CAAA5I,QAAA,CAAAiJ,mBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAoB,MAAA,CAAA5I,QAAA,CAAAkJ,mBAAA;UACAxC,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAkB,MAAA,CAAA5I,QAAA,CAAAmJ,oBAAA;UACAzC,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAAgB,MAAA,CAAA5I,QAAA,CAAAwJ,mBAAA;QACA;MACA;IACA;IACA;IACAC,QAAA,WAAAA,SAAAC,KAAA;MAAA,IAAAC,GAAA,GAAAD,KAAA,CAAAC,GAAA;QAAAC,QAAA,GAAAF,KAAA,CAAAE,QAAA;MACA,IAAAA,QAAA;QACA,SAAA5J,QAAA,CAAA6J,WAAA;UACA;YAAA9F,KAAA,OAAA/D,QAAA,CAAA8J;UAAA;QACA;MACA;QACA;MACA;IACA;IACAC,SAAA,WAAAA,UAAAC,KAAA;MAAA,IAAAL,GAAA,GAAAK,KAAA,CAAAL,GAAA;QAAAC,QAAA,GAAAI,KAAA,CAAAJ,QAAA;MACA,IAAAA,QAAA;QACA,SAAA5J,QAAA,CAAA6J,WAAA;UACA;YAAAjC,eAAA,OAAA5H,QAAA,CAAAiK;UAAA;QACA;MACA;QACA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAR,GAAA,GAAAQ,KAAA,CAAAR,GAAA;QAAAC,QAAA,GAAAO,KAAA,CAAAP,QAAA;MACA;QAAA7F,KAAA,OAAA/D,QAAA,CAAAoK;MAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA;MAAA,IAAAX,GAAA,GAAAW,KAAA,CAAAX,GAAA;QAAAC,QAAA,GAAAU,KAAA,CAAAV,QAAA;MACA;QAAAhC,eAAA,OAAA5H,QAAA,CAAAuK;MAAA;IACA;IACA;IACAjE,0BAAA,WAAAA,2BAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;IAAA,CACA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MACA,IAAAiE,GAAA;MACA,SAAAxK,QAAA,CAAAyK,SAAA,EAAAD,GAAA,CAAA/G,IAAA;MACA,SAAAzD,QAAA,CAAA0K,SAAA,EAAAF,GAAA,CAAA/G,IAAA;MACA,SAAAzD,QAAA,CAAA2K,YAAA;QACAH,GAAA,CAAA/G,IAAA;QACA,SAAAzD,QAAA,CAAA4K,SAAA,EAAAJ,GAAA,CAAA/G,IAAA;QACA+G,GAAA,CAAA/G,IAAA;MACA;MACA,SAAAzD,QAAA,CAAA6K,UAAA,EAAAL,GAAA,CAAA/G,IAAA;MACA,KAAAxD,OAAA,GAAAuK,GAAA,CAAAM,IAAA;MACA,KAAA9K,QAAA,CAAA+K,WAAA;IACA;IAEAlK,IAAA,WAAAA,KAAA,GACA;IACAmE,MAAA,WAAAA,OAAA;MACA,KAAA1F,SAAA;MACA,KAAAwB,WAAA;IACA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAkK,MAAA;MACA,KAAAvL,eAAA;MACA,IAAAoC,MAAA;QACAoJ,IAAA,OAAA3L,SAAA;QACA4L,KAAA,OAAA3L,QAAA;QACA4L,IAAA;QACAlM,eAAA;MACA;MAGA,SAAAZ,UAAA,CAAA+M,UAAA,eAAA/M,UAAA,CAAA+M,UAAA,IAAAC,SAAA;QACAxJ,MAAA,4BAAAxD,UAAA,CAAA+M,UAAA;MACA;MAEA,SAAA/M,UAAA,CAAAiN,WAAA,eAAAjN,UAAA,CAAAiN,WAAA,IAAAD,SAAA;QACAxJ,MAAA,6BAAAxD,UAAA,CAAAiN,WAAA;MACA;MAEA,SAAAjN,UAAA,CAAAkN,cAAA,eAAAlN,UAAA,CAAAkN,cAAA,IAAAF,SAAA;QACAxJ,MAAA,gCAAAxD,UAAA,CAAAkN,cAAA;MACA;MAEA,SAAAlN,UAAA,CAAAM,SAAA,eAAAN,UAAA,CAAAM,SAAA,IAAA0M,SAAA;QACAxJ,MAAA,2BAAAxD,UAAA,CAAAM,SAAA;MACA;MAEAkD,MAAA;;MAGA,KAAAa,KAAA;QACAC,GAAA;QACAC,MAAA;QACAf,MAAA,EAAAA;MACA,GAAAgB,IAAA,WAAA2I,KAAA;QAAA,IAAApN,IAAA,GAAAoN,KAAA,CAAApN,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2E,IAAA;UACAiI,MAAA,CAAA3L,QAAA,GAAAjB,IAAA,CAAAA,IAAA,CAAAqN,IAAA;UACAT,MAAA,CAAAxL,SAAA,GAAApB,IAAA,CAAAA,IAAA,CAAAmH,KAAA;QACA;UACAyF,MAAA,CAAA3L,QAAA;UACA2L,MAAA,CAAAxL,SAAA;QACA;QACAwL,MAAA,CAAAvL,eAAA;MACA;;MAEA;MACA;IACA;IACA;IACAiM,gBAAA,WAAAA,iBAAArK,GAAA;MACA,KAAA9B,QAAA,GAAA8B,GAAA;MACA,KAAA/B,SAAA;MACA,KAAAwB,WAAA;IACA;IACA;IACA6K,mBAAA,WAAAA,oBAAAtK,GAAA;MACA,KAAA/B,SAAA,GAAA+B,GAAA;MACA,KAAAP,WAAA;IACA;IACA;IACA8K,sBAAA,WAAAA,uBAAAvK,GAAA;MACA,KAAA3B,kBAAA,GAAA2B,GAAA;IACA;IACA;IACAwK,kBAAA,WAAAA,mBAAAnN,EAAA,EAAA8E,IAAA;MAAA,IAAAsI,MAAA;MACA,KAAAnM,QAAA;MACA,KAAAI,eAAA;MACA,KAAAgM,oBAAA;MACA,IAAAvI,IAAA;QACAA,IAAA;MACA;MACA,KAAAnB,SAAA;QACAyJ,MAAA,CAAAE,KAAA,CAAAC,WAAA,CAAApL,IAAA,CAAAnC,EAAA,EAAA8E,IAAA;MACA;IACA;IACA;IACA0I,QAAA,WAAAA,SAAAC,IAAA;MACA1H,MAAA,CAAA2H,IAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAA3N,EAAA;MAAA,IAAA4N,MAAA;MACA,IAAAC,GAAA,GAAA7N,EAAA,IAAA8N,MAAA,CAAA9N,EAAA,UAAAgB,kBAAA,CAAA+M,GAAA,WAAAtJ,IAAA;QACA,OAAAqJ,MAAA,CAAArJ,IAAA,CAAAzE,EAAA;MACA;MAEA,KAAAgO,QAAA,6BAAAC,MAAA,CAAAjO,EAAA;QACAkO,iBAAA;QACAC,gBAAA;QACArJ,IAAA;MACA,GAAAX,IAAA;QACAyJ,MAAA,CAAA5J,KAAA;UACAC,GAAA;UACAC,MAAA;UACAxE,IAAA,EAAAmO;QACA,GAAA1J,IAAA,WAAAiK,KAAA;UAAA,IAAA1O,IAAA,GAAA0O,KAAA,CAAA1O,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2E,IAAA;YACAuJ,MAAA,CAAA1H,QAAA;cACAC,OAAA;cACArB,IAAA;cACAsB,QAAA;cACAC,OAAA,WAAAA,QAAA;gBACAuH,MAAA,CAAAtH,MAAA;cACA;YACA;UACA;YACAsH,MAAA,CAAA1H,QAAA,CAAAmI,KAAA,CAAA3O,IAAA,CAAA4O,GAAA;UACA;QACA;MACA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAA7O,IAAA;MACA,IAAAwD,KAAA;MACAA,KAAA,CAAAc,KAAA;QACAC,GAAA,kCAAAvE,IAAA,CAAA+N,IAAA;QACAvJ,MAAA;MACA,GAAAC,IAAA,WAAAqK,KAAA;QAAA,IAAA9O,IAAA,GAAA8O,KAAA,CAAA9O,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2E,IAAA;UACAnB,KAAA,CAAAgD,QAAA;YACAC,OAAA;YACArB,IAAA;YACAsB,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAnD,KAAA,CAAAoD,MAAA;YACA;UACA;QACA;UACApD,KAAA,CAAAgD,QAAA,CAAAmI,KAAA,CAAA3O,IAAA,CAAA4O,GAAA;QACA;MACA;IAEA;IACA;IACAG,gBAAA,WAAAA,iBAAA/O,IAAA;MACA,KAAAwG,QAAA,CAAAmI,KAAA;IACA;IACA;IACAK,cAAA,WAAAA,eAAA1O,EAAA,EAAAC,SAAA,EAAAG,YAAA;MACA,IAAA8C,KAAA;MACA;MACAA,KAAA,CAAA7C,QAAA,GAAAL,EAAA;MACAkD,KAAA,CAAAtB,UAAA,GAAA3B,SAAA;MACAiD,KAAA,CAAA9C,YAAA,GAAAA,YAAA;MACA;MACA8C,KAAA,CAAArB,iBAAA;MACAqB,KAAA,CAAAxB,uBAAA;MACAwB,KAAA,CAAAvB,2BAAA;;MAGA;MACA,IAAAwB,MAAA;QACAoJ,IAAA;QACAC,KAAA;QACAC,IAAA;QACAlM,eAAA;QACAF,QAAA,EAAA6C,KAAA,CAAA7C;MACA;MACA6C,KAAA,CAAAc,KAAA;QACAC,GAAA;QACAC,MAAA;QACAf,MAAA,EAAAA;MACA,GAAAgB,IAAA,WAAAwK,MAAA;QAAA,IAAAjP,IAAA,GAAAiP,MAAA,CAAAjP,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2E,IAAA;UACAnB,KAAA,CAAAnB,SAAA;UACArC,IAAA,CAAAA,IAAA,CAAAqN,IAAA,CAAAvI,OAAA,WAAAwD,EAAA;YACA,IAAA4G,KAAA;YACAA,KAAA,CAAA5O,EAAA,GAAAgI,EAAA,CAAAhI,EAAA;YACA4O,KAAA,CAAA3O,SAAA,GAAA+H,EAAA,CAAA/H,SAAA;YACA2O,KAAA,CAAA1O,QAAA,GAAA8H,EAAA,CAAA9H,QAAA;YACA0O,KAAA,CAAAlC,UAAA,GAAA1E,EAAA,CAAA0E,UAAA;YACAkC,KAAA,CAAAhC,WAAA,GAAA5E,EAAA,CAAA4E,WAAA;YACAgC,KAAA,CAAA/B,cAAA,GAAA7E,EAAA,CAAA6E,cAAA;YACA+B,KAAA,CAAAC,WAAA,GAAA7G,EAAA,CAAA6G,WAAA;YACAD,KAAA,CAAAE,WAAA,GAAA9G,EAAA,CAAA8G,WAAA;YACAF,KAAA,CAAAG,QAAA,GAAA/G,EAAA,CAAA+G,QAAA;YACAH,KAAA,CAAAlO,UAAA,GAAAsH,EAAA,CAAAtH,UAAA;YACAkO,KAAA,CAAAzO,OAAA,GAAA6H,EAAA,CAAA7H,OAAA;YACAyO,KAAA,CAAA9O,IAAA,GAAAkI,EAAA,CAAAlI,IAAA;YACA8O,KAAA,CAAAI,OAAA,GAAAhH,EAAA,CAAAgH,OAAA;YACAJ,KAAA,CAAAxO,YAAA,GAAA4H,EAAA,CAAA5H,YAAA;YACAwO,KAAA,CAAAvO,QAAA,GAAA2H,EAAA,CAAA3H,QAAA;YACAuO,KAAA,CAAAtO,UAAA,GAAA0H,EAAA,CAAA1H,UAAA;YACAsO,KAAA,CAAArO,eAAA,GAAAyH,EAAA,CAAAzH,eAAA;YACAqO,KAAA,CAAApO,UAAA,GAAAwH,EAAA,CAAAxH,UAAA;YACAoO,KAAA,CAAAnO,UAAA,GAAAuH,EAAA,CAAAvH,UAAA;YACAmO,KAAA,CAAAlO,UAAA,GAAAsH,EAAA,CAAAtH,UAAA;YACAwC,KAAA,CAAAnB,SAAA,CAAAgD,IAAA,CAAA6J,KAAA;UACA;QACA;MACA;IACA;IAEA;IACAK,eAAA,WAAAA,gBAAA7O,YAAA;MACA,IAAA8C,KAAA;MACAA,KAAA,CAAApB,qBAAA,GAAA1B,YAAA;MACA8C,KAAA,CAAAvB,2BAAA;IACA;IACA;IACAuN,eAAA,WAAAA,gBAAAlP,EAAA;MACA,IAAAkD,KAAA;MACA,IAAA2K,GAAA;MACAA,GAAA,CAAA9I,IAAA,CAAA+I,MAAA,CAAA9N,EAAA;MACAkD,KAAA,CAAAc,KAAA;QACAC,GAAA;QACAC,MAAA;QACAxE,IAAA,EAAAmO;MACA,GAAA1J,IAAA,WAAAgL,MAAA;QAAA,IAAAzP,IAAA,GAAAyP,MAAA,CAAAzP,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2E,IAAA;UACAnB,KAAA,CAAAgD,QAAA;YACAC,OAAA;YACArB,IAAA;YACAsB,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAnD,KAAA,CAAAwL,cAAA,CAAAxL,KAAA,CAAA7C,QAAA,EAAA6C,KAAA,CAAAtB,UAAA,EAAAsB,KAAA,CAAA9C,YAAA;YACA;UACA;QACA;UACA8C,KAAA,CAAAgD,QAAA,CAAAmI,KAAA,CAAA3O,IAAA,CAAA4O,GAAA;QACA;MACA;IACA;IAEA;IACAc,UAAA,WAAAA,WAAA;MACA,IAAAlM,KAAA;MACA,IAAAA,KAAA,CAAArB,iBAAA;QACAwN,KAAA;QACA;MACA;MACA,IAAA3P,IAAA;QAAA,YAAAwD,KAAA,CAAA7C,QAAA;QAAA;QAAA,gBAAA6C,KAAA,CAAArB;MAAA;MACAqB,KAAA,CAAAc,KAAA;QACAC,GAAA;QACAC,MAAA;QACAxE,IAAA,EAAAA;MACA,GAAAyE,IAAA,WAAAmL,MAAA;QAAA,IAAA5P,IAAA,GAAA4P,MAAA,CAAA5P,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2E,IAAA;UACAnB,KAAA,CAAAgD,QAAA;YACAC,OAAA;YACArB,IAAA;YACAsB,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAnD,KAAA,CAAAwL,cAAA,CAAAxL,KAAA,CAAA7C,QAAA,EAAA6C,KAAA,CAAAtB,UAAA,EAAAsB,KAAA,CAAA9C,YAAA;YACA;UACA;QACA;UACA8C,KAAA,CAAAgD,QAAA,CAAAmI,KAAA,CAAA3O,IAAA,CAAA4O,GAAA;QACA;MACA;IACA;EAAA;AACA", "ignoreList": []}]}