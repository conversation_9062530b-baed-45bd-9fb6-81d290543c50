{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\forum\\list.vue?vue&type=template&id=702a0c24&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\forum\\list.vue", "mtime": 1642386767096}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}