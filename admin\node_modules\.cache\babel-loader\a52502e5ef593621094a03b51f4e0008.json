{"remainingRequest": "D:\\BaiduNetdiskDownload\\源码合集-springboot和vue或html\\springboot和vue体育馆预约系统\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\BaiduNetdiskDownload\\源码合集-springboot和vue或html\\springboot和vue体育馆预约系统\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\BaiduNetdiskDownload\\源码合集-springboot和vue或html\\springboot和vue体育馆预约系统\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\BaiduNetdiskDownload\\源码合集-springboot和vue或html\\springboot和vue体育馆预约系统\\admin\\src\\views\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\BaiduNetdiskDownload\\源码合集-springboot和vue或html\\springboot和vue体育馆预约系统\\admin\\src\\views\\register.vue", "mtime": 1642386767422}, {"path": "D:\\BaiduNetdiskDownload\\源码合集-springboot和vue或html\\springboot和vue体育馆预约系统\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\BaiduNetdiskDownload\\源码合集-springboot和vue或html\\springboot和vue体育馆预约系统\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\BaiduNetdiskDownload\\源码合集-springboot和vue或html\\springboot和vue体育馆预约系统\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\BaiduNetdiskDownload\\源码合集-springboot和vue或html\\springboot和vue体育馆预约系统\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\BaiduNetdiskDownload\\源码合集-springboot和vue或html\\springboot和vue体育馆预约系统\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "ruleForm", "tableName", "rules", "sexTypesOptions", "mounted", "table", "$storage", "get", "methods", "getUUID", "Date", "getTime", "close", "$router", "push", "path", "login", "_this", "username", "$message", "error", "password", "repetitionPassword", "yo<PERSON><PERSON><PERSON><PERSON>", "yonghuPhone", "$validate", "isMobile", "yonghuIdNumber", "yonghuEmail", "isEmail", "$http", "url", "concat", "method", "then", "_ref", "code", "message", "type", "duration", "onClose", "replace", "msg"], "sources": ["src/views/register.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"container\">\r\n            <div class=\"login-form\" style=\"backgroundColor:rgba(183, 174, 174, 0.5);borderRadius:22px\">\r\n                <h1 class=\"h1\" style=\"color:#000;fontSize:28px;\">体育馆使用预约平台注册</h1>\r\n                <el-form ref=\"rgsForm\" class=\"rgs-form\" :model=\"rgsForm\" label-width=\"120px\">\r\n                        <el-form-item label=\"账号\" class=\"input\">\r\n                            <el-input v-model=\"ruleForm.username\" autocomplete=\"off\" placeholder=\"账号\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"密码\" class=\"input\">\r\n                            <el-input type=\"password\" v-model=\"ruleForm.password\" autocomplete=\"off\" show-password/>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"重复密码\" class=\"input\">\r\n                            <el-input type=\"password\" v-model=\"ruleForm.repetitionPassword\" autocomplete=\"off\" show-password/>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"用户姓名\" class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuName\" autocomplete=\"off\" placeholder=\"用户姓名\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"用户手机号\" class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuPhone\" autocomplete=\"off\" placeholder=\"用户手机号\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"用户身份证号\" class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuIdNumber\" autocomplete=\"off\" placeholder=\"用户身份证号\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"电子邮箱\" class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuEmail\" autocomplete=\"off\" placeholder=\"电子邮箱\"  />\r\n                        </el-form-item>\r\n                        <div style=\"display: flex;flex-wrap: wrap;width: 100%;justify-content: center;\">\r\n                            <el-button class=\"btn\" type=\"primary\" @click=\"login()\">注册</el-button>\r\n                            <el-button class=\"btn close\" type=\"primary\" @click=\"close()\">取消</el-button>\r\n                        </div>\r\n                </el-form>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    export default {\r\n        data() {\r\n            return {\r\n                ruleForm: {\r\n                },\r\n                tableName:\"\",\r\n                rules: {},\r\n                sexTypesOptions : [],\r\n            };\r\n        },\r\n        mounted(){\r\n            let table = this.$storage.get(\"loginTable\");\r\n            this.tableName = table;\r\n        },\r\n        methods: {\r\n            // 获取uuid\r\n            getUUID () {\r\n                return new Date().getTime();\r\n            },\r\n            close(){\r\n                this.$router.push({ path: \"/login\" });\r\n            },\r\n            // 注册\r\n            login() {\r\n\r\n                            if((!this.ruleForm.username)){\r\n                                this.$message.error('账号不能为空');\r\n                                return\r\n                            }\r\n                            if((!this.ruleForm.password)){\r\n                                this.$message.error('密码不能为空');\r\n                                return\r\n                            }\r\n                            if((!this.ruleForm.repetitionPassword)){\r\n                                this.$message.error('重复密码不能为空');\r\n                                return\r\n                            }\r\n                            if(this.ruleForm.repetitionPassword != this.ruleForm.password){\r\n                                this.$message.error('密码和重复密码不一致');\r\n                                return\r\n                            }\r\n                            if((!this.ruleForm.yonghuName)&& 'yonghu'==this.tableName){\r\n                                this.$message.error('用户姓名不能为空');\r\n                                return\r\n                            }\r\n                             if('yonghu' == this.tableName && this.ruleForm.yonghuPhone&&(!this.$validate.isMobile(this.ruleForm.yonghuPhone))){\r\n                                 this.$message.error('手机应输入手机格式');\r\n                                 return\r\n                             }\r\n                            if((!this.ruleForm.yonghuIdNumber)&& 'yonghu'==this.tableName){\r\n                                this.$message.error('用户身份证号不能为空');\r\n                                return\r\n                            }\r\n                            if('yonghu' == this.tableName && this.ruleForm.yonghuEmail&&(!this.$validate.isEmail(this.ruleForm.yonghuEmail))){\r\n                                this.$message.error(\"邮箱应输入邮件格式\");\r\n                                return\r\n                            }\r\n                this.$http({\r\n                    url: `${this.tableName}/register`,\r\n                    method: \"post\",\r\n                    data:this.ruleForm\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                    this.$message({\r\n                        message: \"注册成功,请登录后在个人中心页面补充个人数据\",\r\n                        type: \"success\",\r\n                        duration: 1500,\r\n                        onClose: () => {\r\n                        this.$router.replace({ path: \"/login\" });\r\n                }\r\n                });\r\n                } else {\r\n                    this.$message.error(data.msg);\r\n                }\r\n            });\r\n            }\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n    .el-radio__input.is-checked .el-radio__inner {\r\n        border-color: #00c292;\r\n        background: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked .el-radio__inner {\r\n        border-color: #00c292;\r\n        background: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked .el-radio__inner {\r\n        border-color: #00c292;\r\n        background: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked+.el-radio__label {\r\n        color: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked+.el-radio__label {\r\n        color: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked+.el-radio__label {\r\n        color: #00c292;\r\n    }\r\n\r\n    .h1 {\r\n        margin-top: 10px;\r\n    }\r\n\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n\r\n    // .container {\r\n       //    min-height: 100vh;\r\n       //    text-align: center;\r\n       //    // background-color: #00c292;\r\n       //    padding-top: 20vh;\r\n       //    background-image: url(../assets/img/bg.jpg);\r\n       //    background-size: 100% 100%;\r\n       //    opacity: 0.9;\r\n       //  }\r\n\r\n    // .login-form:before {\r\n       // \tvertical-align: middle;\r\n       // \tdisplay: inline-block;\r\n       // }\r\n\r\n    // .login-form {\r\n       // \tmax-width: 500px;\r\n       // \tpadding: 20px 0;\r\n       // \twidth: 80%;\r\n       // \tposition: relative;\r\n       // \tmargin: 0 auto;\r\n\r\n    // \t.label {\r\n          // \t\tmin-width: 60px;\r\n          // \t}\r\n\r\n    // \t.input-group {\r\n          // \t\tmax-width: 500px;\r\n          // \t\tpadding: 20px 0;\r\n          // \t\twidth: 80%;\r\n          // \t\tposition: relative;\r\n          // \t\tmargin: 0 auto;\r\n          // \t\tdisplay: flex;\r\n          // \t\talign-items: center;\r\n\r\n    // \t\t.input-container {\r\n              // \t\t\tdisplay: inline-block;\r\n              // \t\t\twidth: 100%;\r\n              // \t\t\ttext-align: left;\r\n              // \t\t\tmargin-left: 10px;\r\n              // \t\t}\r\n\r\n    // \t\t.icon {\r\n              // \t\t\twidth: 30px;\r\n              // \t\t\theight: 30px;\r\n              // \t\t}\r\n\r\n    // \t\t.input {\r\n              // \t\t\tposition: relative;\r\n              // \t\t\tz-index: 2;\r\n              // \t\t\tfloat: left;\r\n              // \t\t\twidth: 100%;\r\n              // \t\t\tmargin-bottom: 0;\r\n              // \t\t\tbox-shadow: none;\r\n              // \t\t\tborder-top: 0px solid #ccc;\r\n              // \t\t\tborder-left: 0px solid #ccc;\r\n              // \t\t\tborder-right: 0px solid #ccc;\r\n              // \t\t\tborder-bottom: 1px solid #ccc;\r\n              // \t\t\tpadding: 0px;\r\n              // \t\t\tresize: none;\r\n              // \t\t\tborder-radius: 0px;\r\n              // \t\t\tdisplay: block;\r\n              // \t\t\twidth: 100%;\r\n              // \t\t\theight: 34px;\r\n              // \t\t\tpadding: 6px 12px;\r\n              // \t\t\tfont-size: 14px;\r\n              // \t\t\tline-height: 1.42857143;\r\n              // \t\t\tcolor: #555;\r\n              // \t\t\tbackground-color: #fff;\r\n              // \t\t}\r\n\r\n    // \t}\r\n    // }\r\n\r\n    .nk-navigation {\r\n        margin-top: 15px;\r\n\r\n    a {\r\n        display: inline-block;\r\n        color: #fff;\r\n        background: rgba(255, 255, 255, .2);\r\n        width: 100px;\r\n        height: 50px;\r\n        border-radius: 30px;\r\n        text-align: center;\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        justify-content: center;\r\n        padding: 0 20px;\r\n    }\r\n\r\n    .icon {\r\n        margin-left: 10px;\r\n        width: 30px;\r\n        height: 30px;\r\n    }\r\n    }\r\n\r\n    .register-container {\r\n        margin-top: 10px;\r\n\r\n    a {\r\n        display: inline-block;\r\n        color: #fff;\r\n        max-width: 500px;\r\n        height: 50px;\r\n        border-radius: 30px;\r\n        text-align: center;\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        justify-content: center;\r\n        padding: 0 20px;\r\n\r\n    div {\r\n        margin-left: 10px;\r\n    }\r\n    }\r\n    }\r\n\r\n    .container {\r\n        background-image: url(\"http://codegen.caihongy.cn/20210413/1043288468a242e083bddea1af7df208.jpg\");\r\n        height: 100vh;\r\n        background-position: center center;\r\n        background-size: cover;\r\n        background-repeat: no-repeat;\r\n\r\n    .login-form {\r\n        right: 50%;\r\n        top: 50%;\r\n        height: auto;\r\n        transform: translate3d(50%, -50%, 0);\r\n        border-radius: 10px;\r\n        background-color: rgba(255,255,255,.5);\r\n        width: 420px;\r\n        padding: 30px 30px 40px 30px;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n\r\n    .h1 {\r\n        margin: 0;\r\n        text-align: center;\r\n        line-height: 54px;\r\n        font-size: 24px;\r\n        color: #000;\r\n    }\r\n\r\n    .rgs-form {\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        align-items: center;\r\n\r\n    .input {\r\n        width: 100%;\r\n\r\n    & /deep/ .el-form-item__label {\r\n          line-height: 40px;\r\n          color: rgba(17, 16, 16, 1);\r\n          font-size: #606266;\r\n      }\r\n\r\n    & /deep/ .el-input__inner {\r\n          height: 40px;\r\n          color: rgba(23, 24, 26, 1);\r\n          font-size: 14px;\r\n          border-width: 1px;\r\n          border-style: solid;\r\n          border-color: #606266;\r\n          border-radius: 22px;\r\n          background-color: #fff;\r\n      }\r\n    }\r\n\r\n    .btn {\r\n        margin: 0 10px;\r\n        width: 88px;\r\n        height: 44px;\r\n        color: #fff;\r\n        font-size: 14px;\r\n        border-width: 1px;\r\n        border-style: solid;\r\n        border-color: #409EFF;\r\n        border-radius: 22px;\r\n        background-color: #409EFF;\r\n    }\r\n\r\n    .close {\r\n        margin: 0 10px;\r\n        width: 88px;\r\n        height: 44px;\r\n        color: #409EFF;\r\n        font-size: 14px;\r\n        border-width: 1px;\r\n        border-style: solid;\r\n        border-color: #409EFF;\r\n        border-radius: 22px;\r\n        background-color: #FFF;\r\n    }\r\n\r\n    }\r\n    }\r\n    }\r\n</style>\r\n"], "mappings": ";;;AAqCA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA,GACA;MACAC,SAAA;MACAC,KAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,KAAA,QAAAC,QAAA,CAAAC,GAAA;IACA,KAAAN,SAAA,GAAAI,KAAA;EACA;EACAG,OAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,WAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACA;IACAC,KAAA,WAAAA,MAAA;MAAA,IAAAC,KAAA;MAEA,UAAAjB,QAAA,CAAAkB,QAAA;QACA,KAAAC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAApB,QAAA,CAAAqB,QAAA;QACA,KAAAF,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAApB,QAAA,CAAAsB,kBAAA;QACA,KAAAH,QAAA,CAAAC,KAAA;QACA;MACA;MACA,SAAApB,QAAA,CAAAsB,kBAAA,SAAAtB,QAAA,CAAAqB,QAAA;QACA,KAAAF,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAApB,QAAA,CAAAuB,UAAA,qBAAAtB,SAAA;QACA,KAAAkB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,qBAAAnB,SAAA,SAAAD,QAAA,CAAAwB,WAAA,UAAAC,SAAA,CAAAC,QAAA,MAAA1B,QAAA,CAAAwB,WAAA;QACA,KAAAL,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAApB,QAAA,CAAA2B,cAAA,qBAAA1B,SAAA;QACA,KAAAkB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,qBAAAnB,SAAA,SAAAD,QAAA,CAAA4B,WAAA,UAAAH,SAAA,CAAAI,OAAA,MAAA7B,QAAA,CAAA4B,WAAA;QACA,KAAAT,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAU,KAAA;QACAC,GAAA,KAAAC,MAAA,MAAA/B,SAAA;QACAgC,MAAA;QACAlC,IAAA,OAAAC;MACA,GAAAkC,IAAA,WAAAC,IAAA;QAAA,IAAApC,IAAA,GAAAoC,IAAA,CAAApC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAqC,IAAA;UACAnB,KAAA,CAAAE,QAAA;YACAkB,OAAA;YACAC,IAAA;YACAC,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAvB,KAAA,CAAAJ,OAAA,CAAA4B,OAAA;gBAAA1B,IAAA;cAAA;YACA;UACA;QACA;UACAE,KAAA,CAAAE,QAAA,CAAAC,KAAA,CAAArB,IAAA,CAAA2C,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}