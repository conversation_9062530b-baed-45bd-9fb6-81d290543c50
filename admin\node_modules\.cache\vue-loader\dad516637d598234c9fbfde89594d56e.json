{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\gonggao\\list.vue?vue&type=template&id=4c563f5b", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\gonggao\\list.vue", "mtime": 1750583734488}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}