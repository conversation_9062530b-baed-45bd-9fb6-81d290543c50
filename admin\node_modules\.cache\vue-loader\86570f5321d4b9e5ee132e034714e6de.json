{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\forum\\list.vue?vue&type=template&id=702a0c24", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\forum\\list.vue", "mtime": 1750583734456}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, null]}