{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\common\\Editor.vue?vue&type=style&index=0&id=4c8019d1&lang=css", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\common\\Editor.vue", "mtime": 1642468880096}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Editor.vue"], "names": [], "mappings": ";AAuKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Editor.vue", "sourceRoot": "src/components/common", "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 图片上传组件辅助-->\r\n    <el-upload\r\n      class=\"avatar-uploader\"\r\n      :action=\"getActionUrl\"\r\n      name=\"file\"\r\n      :headers=\"header\"\r\n      :show-file-list=\"false\"\r\n      :on-success=\"uploadSuccess\"\r\n      :on-error=\"uploadError\"\r\n      :before-upload=\"beforeUpload\"\r\n    ></el-upload>\r\n\r\n    <quill-editor\r\n      class=\"editor\"\r\n      v-model=\"content\"\r\n      ref=\"myQuillEditor\"\r\n      :options=\"editorOption\"\r\n      @blur=\"onEditorBlur($event)\"\r\n      @focus=\"onEditorFocus($event)\"\r\n      @change=\"onEditorChange($event)\"\r\n    ></quill-editor>\r\n  </div>\r\n</template>\r\n<script>\r\n// 工具栏配置\r\nconst toolbarOptions = [\r\n  [\"bold\", \"italic\", \"underline\", \"strike\"], // 加粗 斜体 下划线 删除线\r\n  [\"blockquote\", \"code-block\"], // 引用  代码块\r\n  [{ header: 1 }, { header: 2 }], // 1、2 级标题\r\n  [{ list: \"ordered\" }, { list: \"bullet\" }], // 有序、无序列表\r\n  [{ script: \"sub\" }, { script: \"super\" }], // 上标/下标\r\n  [{ indent: \"-1\" }, { indent: \"+1\" }], // 缩进\r\n  // [{'direction': 'rtl'}],                         // 文本方向\r\n  [{ size: [\"small\", false, \"large\", \"huge\"] }], // 字体大小\r\n  [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题\r\n  [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色\r\n  [{ font: [] }], // 字体种类\r\n  [{ align: [] }], // 对齐方式\r\n  [\"clean\"], // 清除文本格式\r\n  [\"link\", \"image\", \"video\"] // 链接、图片、视频\r\n];\r\n\r\nimport { quillEditor } from \"vue-quill-editor\";\r\nimport \"quill/dist/quill.core.css\";\r\nimport \"quill/dist/quill.snow.css\";\r\nimport \"quill/dist/quill.bubble.css\";\r\n\r\nexport default {\r\n  props: {\r\n    /*编辑器的内容*/\r\n    value: {\r\n      type: String\r\n    },\r\n    action: {\r\n      type: String\r\n    },\r\n    /*图片大小*/\r\n    maxSize: {\r\n      type: Number,\r\n      default: 4000 //kb\r\n    }\r\n  },\r\n\r\n  components: {\r\n    quillEditor\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      content: this.value,\r\n      quillUpdateImg: false, // 根据图片上传状态来确定是否显示loading动画，刚开始是false,不显示\r\n      editorOption: {\r\n        placeholder: \"\",\r\n        theme: \"snow\", // or 'bubble'\r\n        modules: {\r\n          toolbar: {\r\n            container: toolbarOptions,\r\n            // container: \"#toolbar\",\r\n            handlers: {\r\n              image: function(value) {\r\n                if (value) {\r\n                  // 触发input框选择图片文件\r\n                  document.querySelector(\".avatar-uploader input\").click();\r\n                } else {\r\n                  this.quill.format(\"image\", false);\r\n                }\r\n              }\r\n              // link: function(value) {\r\n              //   if (value) {\r\n              //     var href = prompt('请输入url');\r\n              //     this.quill.format(\"link\", href);\r\n              //   } else {\r\n              //     this.quill.format(\"link\", false);\r\n              //   }\r\n              // },\r\n            }\r\n          }\r\n        }\r\n      },\r\n      // serverUrl: `${base.url}sys/storage/uploadSwiper?token=${storage.get('token')}`, // 这里写你要上传的图片服务器地址\r\n      header: {\r\n        // token: sessionStorage.token\r\n       'Token': this.$storage.get(\"Token\")\r\n      } // 有的图片服务器要求请求头需要有token\r\n    };\r\n  },\r\n  computed: {\r\n    // 计算属性的 getter\r\n    getActionUrl: function() {\r\n      // return this.$base.url + this.action + \"?token=\" + this.$storage.get(\"token\");\r\n      //   this.value\r\n        this.setContent(this.value);\r\n      return `/${this.$base.name}/` + this.action;\r\n    }\r\n  },\r\n  methods: {\r\n    onEditorBlur() {\r\n      //失去焦点事件\r\n    },\r\n    setContent(value) {\r\n        this.content =value;\r\n    },\r\n    onEditorFocus() {\r\n      //获得焦点事件\r\n    },\r\n    onEditorChange() {\r\n      // console.log(this.content);\r\n      // 内容改变事件\r\n      this.$emit(\"input\", this.content);\r\n    },\r\n    // 富文本图片上传前\r\n    beforeUpload() {\r\n      // 显示loading动画\r\n      this.quillUpdateImg = true;\r\n    },\r\n\r\n    uploadSuccess(res, file) {\r\n      // res为图片服务器返回的数据\r\n      // 获取富文本组件实例\r\n      let quill = this.$refs.myQuillEditor.quill;\r\n      // 如果上传成功\r\n      if (res.code === 0) {\r\n        // 获取光标所在位置\r\n        let length = quill.getSelection().index;\r\n        // 插入图片  res.url为服务器返回的图片地址\r\n        quill.insertEmbed(length, \"image\", this.$base.url+ \"upload/\" +res.file);\r\n        // 调整光标到最后\r\n        quill.setSelection(length + 1);\r\n      } else {\r\n        this.$message.error(\"图片插入失败\");\r\n      }\r\n      // loading动画消失\r\n      this.quillUpdateImg = false;\r\n    },\r\n    // 富文本图片上传失败\r\n    uploadError() {\r\n      // loading动画消失\r\n      this.quillUpdateImg = false;\r\n      this.$message.error(\"图片插入失败\");\r\n    }\r\n  }\r\n};\r\n</script> \r\n\r\n<style>\r\n.editor {\r\n  line-height: normal !important;\r\n}\r\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\r\n  content: \"请输入链接地址:\";\r\n}\r\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\r\n  border-right: 0px;\r\n  content: \"保存\";\r\n  padding-right: 0px;\r\n}\r\n\r\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\r\n  content: \"请输入视频地址:\";\r\n}\r\n.ql-container {\r\n\theight: 400px;\r\n}\r\n\r\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\r\n  content: \"14px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\r\n  content: \"10px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\r\n  content: \"18px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\r\n  content: \"32px\";\r\n}\r\n\r\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\r\n  content: \"文本\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\r\n  content: \"标题1\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\r\n  content: \"标题2\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\r\n  content: \"标题3\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\r\n  content: \"标题4\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\r\n  content: \"标题5\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\r\n  content: \"标题6\";\r\n}\r\n\r\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\r\n  content: \"标准字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\r\n  content: \"衬线字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\r\n  content: \"等宽字体\";\r\n}\r\n</style>"]}]}