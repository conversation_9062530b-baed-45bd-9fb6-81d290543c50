{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\forum\\add-or-update.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\forum\\add-or-update.vue", "mtime": 1642386767099}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AAs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file": "add-or-update.vue", "sourceRoot": "src/views/modules/forum", "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"addEdit-block\">\r\n        <el-form\r\n                class=\"detail-form-content\"\r\n                ref=\"ruleForm\"\r\n                :model=\"ruleForm\"\r\n                :rules=\"rules\"\r\n                label-width=\"80px\"\r\n                :style=\"{backgroundColor:addEditForm.addEditBoxColor}\">\r\n            <el-row>\r\n                <input id=\"updateId\" name=\"id\" type=\"hidden\">\r\n                <el-col :span=\"12\">\r\n                    <el-form-item class=\"select\" v-if=\"type!='info'\"  label=\"帖子类型\" prop=\"forumTypes\">\r\n                        <el-select v-model=\"ruleForm.forumTypes\" placeholder=\"请选择帖子类型\">\r\n                            <el-option\r\n                                    v-for=\"(item,index) in forumTypesOptions\"\r\n                                    v-bind:key=\"item.codeIndex\"\r\n                                    :label=\"item.indexName\"\r\n                                    :value=\"item.codeIndex\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"帖子类型\" prop=\"forumValue\">\r\n                            <el-input v-model=\"ruleForm.forumValue\"\r\n                                      placeholder=\"帖子类型\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"帖子标题\" prop=\"forumName\">\r\n                        <el-input v-model=\"ruleForm.forumName\"\r\n                                  placeholder=\"帖子标题\" clearable  :readonly=\"ro.forumName\"></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"帖子标题\" prop=\"forumName\">\r\n                            <el-input v-model=\"ruleForm.forumName\"\r\n                                      placeholder=\"帖子标题\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"24\">\r\n                    <el-form-item v-if=\"type!='info'\"  label=\"发布内容\" prop=\"forumContent\">\r\n                        <el-input type=\"textarea\" v-model=\"ruleForm.forumContent\"\r\n                                  placeholder=\"发布内容\" clearable  :readonly=\"ro.forumName\"></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item v-if=\"ruleForm.forumContent\" label=\"发布内容\" prop=\"forumContent\">\r\n                            <span v-html=\"ruleForm.forumContent\"></span>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n            </el-row>\r\n            <el-form-item class=\"btn\">\r\n                <el-button v-if=\"type!='info'\" type=\"primary\" class=\"btn-success\" @click=\"onSubmit\">提交</el-button>\r\n                <el-button v-if=\"type!='info'\" class=\"btn-close\" @click=\"back()\">取消</el-button>\r\n                <el-button v-if=\"type=='info'\" class=\"btn-close\" @click=\"back()\">返回</el-button>\r\n            </el-form-item>\r\n        </el-form>\r\n    </div>\r\n</template>\r\n<script>\r\n    import styleJs from \"../../../utils/style.js\";\r\n    // 数字，邮件，手机，url，身份证校验\r\n    import { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                addEditForm:null,\r\n                id: '',\r\n                type: '',\r\n                sessionTable : \"\",//登录账户所在表名\r\n                role : \"\",//权限\r\n                yonghuForm: {},\r\n                ro:{\r\n                    forumName: false,\r\n                    yonghuId: false,\r\n                    usersId: false,\r\n                    forumContent: false,\r\n                    superIds: false,\r\n                    forumTypes: false,\r\n                    forumStateTypes: false,\r\n                    insertTime: false,\r\n                    updateTime: false,\r\n                },\r\n                ruleForm: {\r\n                    forumName: '',\r\n                    yonghuId: '',\r\n                    usersId: '',\r\n                    forumContent: '',\r\n                    superIds: '',\r\n                    forumTypes: '',\r\n                    forumStateTypes: 1,\r\n                    insertTime: '',\r\n                    updateTime: '',\r\n                },\r\n                forumTypesOptions : [],\r\n                forumStateTypesOptions : [],\r\n                yonghuOptions : [],\r\n                usersOptions : [],\r\n                rules: {\r\n                   forumName: [\r\n                              { required: true, message: '帖子标题不能为空', trigger: 'blur' },\r\n                          ],\r\n                   yonghuId: [\r\n                              { required: true, message: '用户不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   usersId: [\r\n                              { required: true, message: '管理员不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   forumContent: [\r\n                              { required: true, message: '发布内容不能为空', trigger: 'blur' },\r\n                          ],\r\n                   superIds: [\r\n                              { required: true, message: '父id不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   forumTypes: [\r\n                              { required: true, message: '帖子类型不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   forumStateTypes: [\r\n                              { required: true, message: '帖子状态不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   insertTime: [\r\n                              { required: true, message: '发帖时间不能为空', trigger: 'blur' },\r\n                          ],\r\n                   updateTime: [\r\n                              { required: true, message: '修改时间不能为空', trigger: 'blur' },\r\n                          ],\r\n                }\r\n            };\r\n        },\r\n        props: [\"parent\"],\r\n        computed: {\r\n        },\r\n        created() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n            if (this.role != \"管理员\"){\r\n            }\r\n            this.addEditForm = styleJs.addStyle();\r\n            this.addEditStyleChange()\r\n            this.addEditUploadStyleChange()\r\n            //获取下拉框信息\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=forum_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.forumTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=forum_state_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.forumStateTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n\r\n         this.$http({\r\n             url: `yonghu/page?page=1&limit=100`,\r\n             method: \"get\"\r\n         }).then(({ data }) => {\r\n             if (data && data.code === 0) {\r\n                this.yonghuOptions = data.data.list;\r\n            }\r\n         });\r\n\r\n        },\r\n        mounted() {\r\n        },\r\n        methods: {\r\n            // 下载\r\n            download(file){\r\n                window.open(`${file}`)\r\n            },\r\n            // 初始化\r\n            init(id,type) {\r\n                if (id) {\r\n                    this.id = id;\r\n                    this.type = type;\r\n                }\r\n                if(this.type=='info'||this.type=='else'){\r\n                    this.info(id);\r\n                }else if(this.type=='cross'){\r\n                    var obj = this.$storage.getObj('crossObj');\r\n                    for (var o in obj){\r\n\r\n                      if(o=='forumName'){\r\n                          this.ruleForm.forumName = obj[o];\r\n                          this.ro.forumName = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='yonghuId'){\r\n                          this.ruleForm.yonghuId = obj[o];\r\n                          this.ro.yonghuId = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='usersId'){\r\n                          this.ruleForm.usersId = obj[o];\r\n                          this.ro.usersId = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='forumContent'){\r\n                          this.ruleForm.forumContent = obj[o];\r\n                          this.ro.forumContent = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='superIds'){\r\n                          this.ruleForm.superIds = obj[o];\r\n                          this.ro.superIds = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='forumTypes'){\r\n                          this.ruleForm.forumTypes = obj[o];\r\n                          this.ro.forumTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='forumStateTypes'){\r\n                          this.ruleForm.forumStateTypes = obj[o];\r\n                          this.ro.forumStateTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='insertTime'){\r\n                          this.ruleForm.insertTime = obj[o];\r\n                          this.ro.insertTime = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='updateTime'){\r\n                          this.ruleForm.updateTime = obj[o];\r\n                          this.ro.updateTime = true;\r\n                          continue;\r\n                      }\r\n                    }\r\n                }\r\n                // 获取用户信息\r\n                this.$http({\r\n                    url:`${this.$storage.get(\"sessionTable\")}/session`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        var json = data.data;\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            yonghuChange(id){\r\n                this.$http({\r\n                    url: `yonghu/info/`+id,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.yonghuForm = data.data;\r\n                    }\r\n                });\r\n            },\r\n            // 多级联动参数\r\n            info(id) {\r\n                this.$http({\r\n                    url: `forum/info/${id}`,\r\n                    method: 'get'\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.ruleForm = data.data;\r\n                        this.yonghuChange(data.data.yonghuId)\r\n                        //解决前台上传图片后台不显示的问题\r\n                        let reg=new RegExp('../../../upload','g')//g代表全部\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            // 提交\r\n            onSubmit() {\r\n                this.$refs[\"ruleForm\"].validate(valid => {\r\n                    if (valid) {\r\n                        this.$http({\r\n                            url:`forum/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n                            method: \"post\",\r\n                            data: this.ruleForm\r\n                        }).then(({ data }) => {\r\n                            if (data && data.code === 0) {\r\n                                this.$message({\r\n                                    message: \"操作成功\",\r\n                                    type: \"success\",\r\n                                    duration: 1500,\r\n                                    onClose: () => {\r\n                                        this.parent.showFlag = true;\r\n                                        this.parent.addOrUpdateFlag = false;\r\n                                        this.parent.forumCrossAddOrUpdateFlag = false;\r\n                                        this.parent.search();\r\n                                        this.parent.contentStyleChange();\r\n                                    }\r\n                                });\r\n                            } else {\r\n                                this.$message.error(data.msg);\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            },\r\n            // 获取uuid\r\n            getUUID () {\r\n                return new Date().getTime();\r\n            },\r\n            // 返回\r\n            back() {\r\n                this.parent.showFlag = true;\r\n                this.parent.addOrUpdateFlag = false;\r\n                this.parent.forumCrossAddOrUpdateFlag = false;\r\n                this.parent.contentStyleChange();\r\n            },\r\n            //图片\r\n\r\n            addEditStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    // input\r\n                    document.querySelectorAll('.addEdit-block .input .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputFontColor\r\n                        el.style.fontSize = this.addEditForm.inputFontSize\r\n                        el.style.borderWidth = this.addEditForm.inputBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.inputBorderStyle\r\n                        el.style.borderColor = this.addEditForm.inputBorderColor\r\n                        el.style.borderRadius = this.addEditForm.inputBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.inputBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .input .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputLableColor\r\n                        el.style.fontSize = this.addEditForm.inputLableFontSize\r\n                    })\r\n                    // select\r\n                    document.querySelectorAll('.addEdit-block .select .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectFontColor\r\n                        el.style.fontSize = this.addEditForm.selectFontSize\r\n                        el.style.borderWidth = this.addEditForm.selectBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.selectBorderStyle\r\n                        el.style.borderColor = this.addEditForm.selectBorderColor\r\n                        el.style.borderRadius = this.addEditForm.selectBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.selectBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectLableColor\r\n                        el.style.fontSize = this.addEditForm.selectLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-select__caret').forEach(el=>{\r\n                        el.style.color = this.addEditForm.selectIconFontColor\r\n                        el.style.fontSize = this.addEditForm.selectIconFontSize\r\n                    })\r\n                    // date\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateFontColor\r\n                        el.style.fontSize = this.addEditForm.dateFontSize\r\n                        el.style.borderWidth = this.addEditForm.dateBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.dateBorderStyle\r\n                        el.style.borderColor = this.addEditForm.dateBorderColor\r\n                        el.style.borderRadius = this.addEditForm.dateBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.dateBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateLableColor\r\n                        el.style.fontSize = this.addEditForm.dateLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__icon').forEach(el=>{\r\n                        el.style.color = this.addEditForm.dateIconFontColor\r\n                        el.style.fontSize = this.addEditForm.dateIconFontSize\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                    })\r\n                    // upload\r\n                    let iconLineHeight = parseInt(this.addEditForm.uploadHeight) - parseInt(this.addEditForm.uploadBorderWidth) * 2 + 'px'\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload--picture-card').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.uploadHeight\r\n                        el.style.color = this.addEditForm.uploadLableColor\r\n                        el.style.fontSize = this.addEditForm.uploadLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-icon-plus').forEach(el=>{\r\n                        el.style.color = this.addEditForm.uploadIconFontColor\r\n                        el.style.fontSize = this.addEditForm.uploadIconFontSize\r\n                        el.style.lineHeight = iconLineHeight\r\n                        el.style.display = 'block'\r\n                    })\r\n                    // 多文本输入框\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-textarea__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaFontColor\r\n                        el.style.fontSize = this.addEditForm.textareaFontSize\r\n                        el.style.borderWidth = this.addEditForm.textareaBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.textareaBorderStyle\r\n                        el.style.borderColor = this.addEditForm.textareaBorderColor\r\n                        el.style.borderRadius = this.addEditForm.textareaBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.textareaBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-form-item__label').forEach(el=>{\r\n                        // el.style.lineHeight = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaLableColor\r\n                        el.style.fontSize = this.addEditForm.textareaLableFontSize\r\n                    })\r\n                    // 保存\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-success').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnSaveWidth\r\n                        el.style.height = this.addEditForm.btnSaveHeight\r\n                        el.style.color = this.addEditForm.btnSaveFontColor\r\n                        el.style.fontSize = this.addEditForm.btnSaveFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnSaveBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnSaveBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnSaveBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnSaveBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnSaveBgColor\r\n                    })\r\n                    // 返回\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-close').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnCancelWidth\r\n                        el.style.height = this.addEditForm.btnCancelHeight\r\n                        el.style.color = this.addEditForm.btnCancelFontColor\r\n                        el.style.fontSize = this.addEditForm.btnCancelFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnCancelBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnCancelBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnCancelBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnCancelBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnCancelBgColor\r\n                    })\r\n                })\r\n            },\r\n            addEditUploadStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload-list--picture-card .el-upload-list__item').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                })\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\">\r\n.editor{\r\n  height: 500px;\r\n\r\n  & /deep/ .ql-container {\r\n\t  height: 310px;\r\n  }\r\n}\r\n.amap-wrapper {\r\n  width: 100%;\r\n  height: 500px;\r\n}\r\n.search-box {\r\n  position: absolute;\r\n}\r\n.addEdit-block {\r\n\tmargin: -10px;\r\n}\r\n.detail-form-content {\r\n\tpadding: 12px;\r\n}\r\n.btn .el-button {\r\n  padding: 0;\r\n}</style>\r\n\r\n"]}]}