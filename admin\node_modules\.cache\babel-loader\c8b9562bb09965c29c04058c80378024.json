{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdiCollection\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdiCollection\\list.vue", "mtime": 1642387833310}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["AddOrUpdate", "styleJs", "data", "searchForm", "key", "sessionTable", "role", "changdiTypesSelectSearch", "form", "id", "changdiId", "yonghuId", "changdiCollectionTypes", "insertTime", "createTime", "dataList", "pageIndex", "pageSize", "totalPage", "dataListLoading", "dataListSelections", "showFlag", "sfshVisiable", "shForm", "chartVisiable", "addOrUpdateFlag", "contents", "layouts", "echartsDate", "Date", "json_fields", "created", "listStyle", "init", "getDataList", "contentStyleChange", "mounted", "$storage", "get", "filters", "htmlfilter", "val", "replace", "components", "computed", "methods", "chartDialog", "_this2", "_this", "params", "dateFormat", "riqi", "getFullYear", "thisTable", "tableName", "sumColum", "date", "$nextTick", "statistic", "$echarts", "document", "getElementById", "$http", "url", "method", "then", "_ref", "code", "series", "yAxis", "for<PERSON>ach", "item", "index", "tempMap", "name", "legend", "type", "push", "option", "tooltip", "trigger", "axisPointer", "crossStyle", "color", "toolbox", "feature", "magicType", "show", "saveAsImage", "xAxis", "axisLabel", "formatter", "setOption", "window", "onresize", "resize", "$message", "message", "duration", "onClose", "search", "_ref2", "res", "p<PERSON><PERSON>y", "i", "length", "xinzi", "total", "value", "title", "text", "left", "radius", "center", "emphasis", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "contentSearchStyleChange", "contentBtnAdAllStyleChange", "contentSearchBtnStyleChange", "contentTableBtnStyleChange", "contentPageStyleChange", "_this3", "querySelectorAll", "el", "textAlign", "inputFontPosition", "style", "height", "inputHeight", "lineHeight", "inputFontColor", "fontSize", "inputFontSize", "borderWidth", "inputBorderWidth", "borderStyle", "inputBorderStyle", "borderColor", "inputBorderColor", "borderRadius", "inputBorderRadius", "backgroundColor", "inputBgColor", "inputTitle", "inputTitleColor", "inputTitleSize", "setTimeout", "inputIconColor", "_this4", "searchBtnHeight", "searchBtnFontColor", "searchBtnFontSize", "searchBtnBorderWidth", "searchBtnBorderStyle", "searchBtnBorderColor", "searchBtnBorderRadius", "searchBtnBgColor", "_this5", "btnAdAllHeight", "btnAdAllAddFontColor", "btnAdAllFontSize", "btnAdAllBorderWidth", "btnAdAllBorderStyle", "btnAdAllBorderColor", "btnAdAllBorderRadius", "btnAdAllAddBgColor", "btnAdAllDelFontColor", "btnAdAllDelBgColor", "btnAdAllWarnFontColor", "btnAdAllWarnBgColor", "rowStyle", "_ref3", "row", "rowIndex", "tableStripe", "tableStripeFontColor", "cellStyle", "_ref4", "tableStripeBgColor", "headerRowStyle", "_ref5", "tableHeaderFontColor", "headerCellStyle", "_ref6", "tableHeaderBgColor", "arr", "pageTotal", "pageSizes", "pagePrevNext", "pagePager", "pageJumper", "join", "pageEachNum", "_this6", "page", "limit", "sort", "changdiName", "undefined", "changdiTypes", "yo<PERSON><PERSON><PERSON><PERSON>", "yonghuPhone", "yonghuIdNumber", "_ref7", "list", "_ref8", "sizeChangeHandle", "currentChangeHandle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addOrUpdateHandler", "_this7", "crossAddOrUpdateFlag", "$refs", "addOrUpdate", "download", "file", "open", "delete<PERSON><PERSON><PERSON>", "_this8", "ids", "Number", "map", "$confirm", "concat", "confirmButtonText", "cancelButtonText", "_ref9", "error", "msg", "changdiCollectionUploadSuccess", "_ref10", "changdiCollectionUploadError"], "sources": ["src/views/modules/changdiCollection/list.vue"], "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"main-content\">\r\n\r\n        <!-- 条件查询 -->\r\n        <div v-if=\"showFlag\">\r\n            <el-form :inline=\"true\" :model=\"searchForm\" class=\"form-content\">\r\n                <el-row :gutter=\"20\" class=\"slt\" :style=\"{justifyContent:contents.searchBoxPosition=='1'?'flex-start':contents.searchBoxPosition=='2'?'center':'flex-end'}\">\r\n                                                \r\n                                                             \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '场地名称' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.changdiName\" placeholder=\"场地名称\" clearable></el-input>\r\n                    </el-form-item>\r\n                                         \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '场地类型' : ''\">\r\n                        <el-select v-model=\"searchForm.changdiTypes\" placeholder=\"请选择场地类型\">\r\n                            <el-option label=\"=-请选择-=\" value=\"\"></el-option>\r\n                            <el-option\r\n                                    v-for=\"(item,index) in changdiTypesSelectSearch\"\r\n                                    v-bind:key=\"index\"\r\n                                    :label=\"item.indexName\"\r\n                                    :value=\"item.codeIndex\">\r\n                                <!--lable是要展示的名称-->\r\n                                <!--value是值-->\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                                                                                                                                                                                                                                                                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户姓名' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuName\" placeholder=\"用户姓名\" clearable></el-input>\r\n                    </el-form-item>\r\n                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户手机号' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuPhone\" placeholder=\"用户手机号\" clearable></el-input>\r\n                    </el-form-item>\r\n                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户身份证号' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuIdNumber\" placeholder=\"用户身份证号\" clearable></el-input>\r\n                    </el-form-item>\r\n                                                                                \r\n\r\n                    <el-form-item>\r\n                        <el-button type=\"success\" @click=\"search()\">查询<i class=\"el-icon-search el-icon--right\"/></el-button>\r\n                    </el-form-item>\r\n                </el-row>\r\n                <el-row class=\"ad\" :style=\"{justifyContent:contents.btnAdAllBoxPosition=='1'?'flex-start':contents.btnAdAllBoxPosition=='2'?'center':'flex-end'}\">\r\n                    <el-form-item>\r\n                        <el-button\r\n                                v-if=\"isAuth('changdiCollection','新增')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-plus\"\r\n                                @click=\"addOrUpdateHandler()\"\r\n                        >新增</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('changdiCollection','删除')\"\r\n                                :disabled=\"dataListSelections.length <= 0\"\r\n                                type=\"danger\"\r\n                                icon=\"el-icon-delete\"\r\n                                @click=\"deleteHandler()\"\r\n                        >删除</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('changdiCollection','报表')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-pie-chart\"\r\n                                @click=\"chartDialog()\"\r\n                        >报表</el-button>\r\n                        &nbsp;\r\n                        <a style=\"text-decoration:none\" class=\"el-button el-button--success\"\r\n                           v-if=\"isAuth('changdiCollection','导入导出')\"\r\n                           icon=\"el-icon-download\"\r\n                           href=\"http://localhost:8080/tiyuguan/upload/changdiCollectionMuBan.xls\"\r\n                        >批量导入场地收藏数据模板</a>\r\n                        &nbsp;\r\n                        <el-upload\r\n                                v-if=\"isAuth('changdiCollection','导入导出')\"\r\n                                style=\"display: inline-block\"\r\n                                action=\"tiyuguan/file/upload\"\r\n                                :on-success=\"changdiCollectionUploadSuccess\"\r\n                                :on-error=\"changdiCollectionUploadError\"\r\n                                :show-file-list = false>\r\n                            <el-button\r\n                                    v-if=\"isAuth('changdiCollection','导入导出')\"\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-upload2\"\r\n                            >批量导入场地收藏数据</el-button>\r\n                        </el-upload>\r\n                        &nbsp;\r\n                        <!-- 导出excel -->\r\n                        <download-excel v-if=\"isAuth('changdiCollection','导入导出')\" style=\"display: inline-block\" class = \"export-excel-wrapper\" :data = \"dataList\" :fields = \"json_fields\" name = \"changdiCollection.xls\">\r\n                            <!-- 导出excel -->\r\n                            <el-button\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-download\"\r\n                            >导出</el-button>\r\n                        </download-excel>\r\n                        &nbsp;\r\n                    </el-form-item>\r\n                </el-row>\r\n            </el-form>\r\n            <div class=\"table-content\">\r\n                <el-table class=\"tables\" :size=\"contents.tableSize\" :show-header=\"contents.tableShowHeader\"\r\n                          :header-row-style=\"headerRowStyle\" :header-cell-style=\"headerCellStyle\"\r\n                          :border=\"contents.tableBorder\"\r\n                          :fit=\"contents.tableFit\"\r\n                          :stripe=\"contents.tableStripe\"\r\n                          :row-style=\"rowStyle\"\r\n                          :cell-style=\"cellStyle\"\r\n                          :style=\"{width: '100%',fontSize:contents.tableContentFontSize,color:contents.tableContentFontColor}\"\r\n                          v-if=\"isAuth('changdiCollection','查看')\"\r\n                          :data=\"dataList\"\r\n                          v-loading=\"dataListLoading\"\r\n                          @selection-change=\"selectionChangeHandler\">\r\n                    <el-table-column  v-if=\"contents.tableSelection\"\r\n                                      type=\"selection\"\r\n                                      header-align=\"center\"\r\n                                      align=\"center\"\r\n                                      width=\"50\">\r\n                    </el-table-column>\r\n                    <el-table-column label=\"索引\" v-if=\"contents.tableIndex\" type=\"index\" width=\"50\" />\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiUuidNumber\"\r\n                                      header-align=\"center\"\r\n                                      label=\"场地编号\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiUuidNumber}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiName\"\r\n                                      header-align=\"center\"\r\n                                      label=\"场地名称\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\" prop=\"changdiPhoto\"\r\n                               header-align=\"center\"\r\n                               width=\"200\"\r\n                               label=\"场地照片\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div v-if=\"scope.row.changdiPhoto\">\r\n                                <img :src=\"scope.row.changdiPhoto\" width=\"100\" height=\"100\">\r\n                            </div>\r\n                            <div v-else>无图片</div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiTypes\"\r\n                                      header-align=\"center\"\r\n                                      label=\"场地类型\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiValue}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuName\"\r\n                                      header-align=\"center\"\r\n                                      label=\"用户姓名\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuPhone\"\r\n                                      header-align=\"center\"\r\n                                      label=\"用户手机号\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuPhone}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuIdNumber\"\r\n                                      header-align=\"center\"\r\n                                      label=\"用户身份证号\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuIdNumber}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\" prop=\"yonghuPhoto\"\r\n                               header-align=\"center\"\r\n                               width=\"200\"\r\n                               label=\"用户头像\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div v-if=\"scope.row.yonghuPhoto\">\r\n                                <img :src=\"scope.row.yonghuPhoto\" width=\"100\" height=\"100\">\r\n                            </div>\r\n                            <div v-else>无图片</div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiCollectionTypes\"\r\n                                      header-align=\"center\"\r\n                                      label=\"类型\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiCollectionValue}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"insertTime\"\r\n                                   header-align=\"center\"\r\n                                   label=\"收藏时间\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.insertTime}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column width=\"300\" :align=\"contents.tableAlign\"\r\n                                     header-align=\"center\"\r\n                                     label=\"操作\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button v-if=\"isAuth('changdiCollection','查看')\" type=\"success\" icon=\"el-icon-tickets\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id,'info')\">详情</el-button>\r\n                            <el-button v-if=\"isAuth('changdiCollection','修改')\" type=\"primary\" icon=\"el-icon-edit\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id)\">修改</el-button>\r\n                            <el-button v-if=\"isAuth('changdiCollection','删除')\" type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteHandler(scope.row.id)\">删除</el-button>\r\n\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n                <el-pagination\r\n                        clsss=\"pages\"\r\n                        :layout=\"layouts\"\r\n                        @size-change=\"sizeChangeHandle\"\r\n                        @current-change=\"currentChangeHandle\"\r\n                        :current-page=\"pageIndex\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"Number(contents.pageEachNum)\"\r\n                        :total=\"totalPage\"\r\n                        :small=\"contents.pageStyle\"\r\n                        class=\"pagination-content\"\r\n                        :background=\"contents.pageBtnBG\"\r\n                        :style=\"{textAlign:contents.pagePosition==1?'left':contents.pagePosition==2?'center':'right'}\"\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <!-- 添加/修改页面  将父组件的search方法传递给子组件-->\r\n        <add-or-update v-if=\"addOrUpdateFlag\" :parent=\"this\" ref=\"addOrUpdate\"></add-or-update>\r\n\r\n\r\n\r\n        <el-dialog title=\"统计报表\" :visible.sync=\"chartVisiable\" width=\"800\">\r\n            <el-date-picker\r\n                    v-model=\"echartsDate\"\r\n                    type=\"year\"\r\n                    placeholder=\"选择年\">\r\n            </el-date-picker>\r\n            <el-button @click=\"chartDialog()\">查询</el-button>\r\n            <div id=\"statistic\" style=\"width:100%;height:600px;\"></div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"chartVisiable = false\">返回</el-button>\r\n\t\t\t</span>\r\n        </el-dialog>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import AddOrUpdate from \"./add-or-update\";\r\n    import styleJs from \"../../../utils/style.js\";\r\n    export default {\r\n        data() {\r\n        return {\r\n            searchForm: {\r\n                key: \"\"\r\n            },\r\n            sessionTable : \"\",//登录账户所在表名\r\n            role : \"\",//权限\r\n    //级联表下拉框搜索条件\r\n              changdiTypesSelectSearch : [],\r\n    //当前表下拉框搜索条件\r\n            form:{\r\n                id : null,\r\n                changdiId : null,\r\n                yonghuId : null,\r\n                changdiCollectionTypes : null,\r\n                insertTime : null,\r\n                createTime : null,\r\n            },\r\n            dataList: [],\r\n            pageIndex: 1,\r\n            pageSize: 10,\r\n            totalPage: 0,\r\n            dataListLoading: false,\r\n            dataListSelections: [],\r\n            showFlag: true,\r\n            sfshVisiable: false,\r\n            shForm: {},\r\n            chartVisiable: false,\r\n            addOrUpdateFlag:false,\r\n            contents:null,\r\n            layouts: '',\r\n\r\n            echartsDate: new Date(),//echarts的时间查询字段\r\n\r\n            //导出excel\r\n            json_fields: {\r\n                //级联表字段\r\n                     '场地编号': 'changdiUuidNumber',\r\n                     '场地名称': 'changdiName',\r\n                     '场地照片': 'changdiPhoto',\r\n                     '场地类型': 'changdiTypes',\r\n                     '场地原价': 'changdiOldMoney',\r\n                     '场地现价': 'changdiNewMoney',\r\n                     '时间段': 'shijianduan',\r\n                     '人数': 'shijianduanRen',\r\n                     '点击次数': 'changdiClicknum',\r\n                     '半全场': 'banquanTypes',\r\n                     '是否上架': 'shangxiaTypes',\r\n                     '推荐吃饭地点': 'tuijian',\r\n                     '用户姓名': 'yonghuName',\r\n                     '用户手机号': 'yonghuPhone',\r\n                     '用户身份证号': 'yonghuIdNumber',\r\n                     '用户头像': 'yonghuPhoto',\r\n                     '电子邮箱': 'yonghuEmail',\r\n                     '余额': 'newMoney',\r\n                //本表字段\r\n                     '类型': \"changdiCollectionTypes\",\r\n                     '收藏时间': \"insertTime\",\r\n            },\r\n\r\n            };\r\n        },\r\n        created() {\r\n            this.contents = styleJs.listStyle();\r\n            this.init();\r\n            this.getDataList();\r\n            this.contentStyleChange()\r\n        },\r\n        mounted() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n        },\r\n        filters: {\r\n            htmlfilter: function (val) {\r\n                return val.replace(/<[^>]*>/g).replace(/undefined/g,'');\r\n            }\r\n        },\r\n        components: {\r\n            AddOrUpdate,\r\n        },\r\n        computed: {\r\n        },\r\n        methods: {\r\n            chartDialog() {\r\n                let _this = this;\r\n                let params = {\r\n                    dateFormat :\"%Y\"\r\n                    ,riqi :_this.echartsDate.getFullYear()\r\n                    ,thisTable : {//当前表\r\n                        tableName :\"shangdian_shouyin\"//当前表表名\r\n                        ,sumColum : 'shangdian_shouyin_true_price' //求和字段\r\n                        ,date : 'insert_time'//分组日期字段\r\n                        // ,string : 'name,leixing'//分组字符串字段\r\n                        // ,types : 'shangdian_shouyin_types'//分组下拉框字段\r\n                    }\r\n                    // ,joinTable : {//级联表（可以不存在）\r\n                    //     tableName :\"yuangong\"//级联表表名\r\n                    //     // ,date : 'insert_time'//分组日期字段\r\n                    //     ,string : 'yuangong_name'//分组字符串字段\r\n                    //     // ,types : 'insertTime'//分组下拉框字段\r\n                    // }\r\n                }\r\n                _this.chartVisiable = true;\r\n                _this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"barSum\",\r\n                        method: \"get\",\r\n                        params: params\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n\r\n\r\n                            //柱状图 求和 已成功使用\r\n                            //start\r\n                            let series = [];//具体数据值\r\n                            data.data.yAxis.forEach(function (item,index) {\r\n                                let tempMap = {};\r\n                                tempMap.name=data.data.legend[index];\r\n                                tempMap.type='bar';\r\n                                tempMap.data=item;\r\n                                series.push(tempMap);\r\n\r\n                            })\r\n\r\n                            var option = {\r\n                                tooltip: {\r\n                                    trigger: 'axis',\r\n                                    axisPointer: {\r\n                                        type: 'cross',\r\n                                        crossStyle: {\r\n                                            color: '#999'\r\n                                        }\r\n                                    }\r\n                                },\r\n                                toolbox: {\r\n                                    feature: {\r\n                                        // dataView: { show: true, readOnly: false },  // 数据查看\r\n                                        magicType: { show: true, type: ['line', 'bar'] },//切换图形展示方式\r\n                                        // restore: { show: true }, // 刷新\r\n                                        saveAsImage: { show: true }//保存\r\n                                    }\r\n                                },\r\n                                legend: {\r\n                                    data: data.data.legend//标题  可以点击导致某一列数据消失\r\n                                },\r\n                                xAxis: [\r\n                                    {\r\n                                        type: 'category',\r\n                                        name: '月份',\r\n                                        data: data.data.xAxis,\r\n                                        axisPointer: {\r\n                                            type: 'shadow'\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                yAxis: [\r\n                                    {\r\n                                        type: 'value',//不能改\r\n                                        name: '元',//y轴单位\r\n                                        axisLabel: {\r\n                                            formatter: '{value} 元' // 后缀\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                series:series//具体数据\r\n                            };\r\n                            // 使用刚指定的配置项和数据显示图表。\r\n                            statistic.setOption(option);\r\n                            //根据窗口的大小变动图表\r\n                            window.onresize = function () {\r\n                                statistic.resize();\r\n                            };\r\n                            //end\r\n\r\n\r\n\r\n                            //饼状图 原先自带的 未修改过\r\n                            //start\r\n                            /*let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表。\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图表\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }*/\r\n\r\n                            //end\r\n                        }else {\r\n                            this.$message({\r\n                                message: \"报表未查询到数据\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n                this.chartVisiable = !this.chartVisiable;\r\n                this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"group/xinzitongji/xinzi\",\r\n                        method: \"get\",\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表。\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图表\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }\r\n                        }\r\n                    });\r\n                // xcolumn ycolumn\r\n                });\r\n            },\r\n            contentStyleChange() {\r\n                this.contentSearchStyleChange()\r\n                this.contentBtnAdAllStyleChange()\r\n                this.contentSearchBtnStyleChange()\r\n                this.contentTableBtnStyleChange()\r\n                this.contentPageStyleChange()\r\n            },\r\n            contentSearchStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-input__inner').forEach(el => {\r\n                        let textAlign = 'left'\r\n                        if(this.contents.inputFontPosition == 2)\r\n                            textAlign = 'center'\r\n                            if (this.contents.inputFontPosition == 3) textAlign = 'right'\r\n                                el.style.textAlign = textAlign\r\n                            el.style.height = this.contents.inputHeight\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                            el.style.color = this.contents.inputFontColor\r\n                            el.style.fontSize = this.contents.inputFontSize\r\n                            el.style.borderWidth = this.contents.inputBorderWidth\r\n                            el.style.borderStyle = this.contents.inputBorderStyle\r\n                            el.style.borderColor = this.contents.inputBorderColor\r\n                            el.style.borderRadius = this.contents.inputBorderRadius\r\n                            el.style.backgroundColor = this.contents.inputBgColor\r\n                    })\r\n                    if (this.contents.inputTitle) {\r\n                        document.querySelectorAll('.form-content .slt .el-form-item__label').forEach(el => {\r\n                            el.style.color = this.contents.inputTitleColor\r\n                            el.style.fontSize = this.contents.inputTitleSize\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }\r\n                    setTimeout(() => {\r\n                        document.querySelectorAll('.form-content .slt .el-input__prefix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__suffix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__icon').forEach(el => {\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }, 10 )\r\n                })\r\n            },\r\n            // 搜索按钮\r\n            contentSearchBtnStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.searchBtnHeight\r\n                        el.style.color = this.contents.searchBtnFontColor\r\n                        el.style.fontSize = this.contents.searchBtnFontSize\r\n                        el.style.borderWidth = this.contents.searchBtnBorderWidth\r\n                        el.style.borderStyle = this.contents.searchBtnBorderStyle\r\n                        el.style.borderColor = this.contents.searchBtnBorderColor\r\n                        el.style.borderRadius = this.contents.searchBtnBorderRadius\r\n                        el.style.backgroundColor = this.contents.searchBtnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 新增、批量删除\r\n            contentBtnAdAllStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .ad .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllAddFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllAddBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--danger').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllDelFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllDelBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--warning').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllWarnFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllWarnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 表格\r\n            rowStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {color: this.contents.tableStripeFontColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            cellStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {backgroundColor: this.contents.tableStripeBgColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            headerRowStyle({row, rowIndex}) {\r\n                return {color: this.contents.tableHeaderFontColor}\r\n            },\r\n            headerCellStyle({row, rowIndex}) {\r\n                return {backgroundColor: this.contents.tableHeaderBgColor}\r\n            },\r\n            // 表格按钮\r\n            contentTableBtnStyleChange() {\r\n                // this.$nextTick(()=>{\r\n                //   setTimeout(()=>{\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--success').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDetailFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDetailBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--primary').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnEditFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnEditBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--danger').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDelFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDelBgColor\r\n                //     })\r\n\r\n                //   }, 50)\r\n                // })\r\n            },\r\n            // 分页\r\n            contentPageStyleChange() {\r\n                let arr = []\r\n                if (this.contents.pageTotal) arr.push('total')\r\n                if (this.contents.pageSizes) arr.push('sizes')\r\n                if (this.contents.pagePrevNext) {\r\n                    arr.push('prev')\r\n                    if (this.contents.pagePager) arr.push('pager')\r\n                    arr.push('next')\r\n                }\r\n                if (this.contents.pageJumper) arr.push('jumper')\r\n                this.layouts = arr.join()\r\n                this.contents.pageEachNum = 10\r\n            },\r\n\r\n            init() {\r\n            },\r\n            search() {\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 获取数据列表\r\n            getDataList() {\r\n                this.dataListLoading = true;\r\n                let params = {\r\n                    page: this.pageIndex,\r\n                    limit: this.pageSize,\r\n                    sort: 'id',\r\n                    changdiCollectionTypes: 1,\r\n                }\r\n\r\n                                                             \r\n                if (this.searchForm.changdiName!= '' && this.searchForm.changdiName!= undefined) {\r\n                    params['changdiName'] = '%' + this.searchForm.changdiName + '%'\r\n                }\r\n                                         \r\n                if (this.searchForm.changdiTypes!= '' && this.searchForm.changdiTypes!= undefined) {\r\n                    params['changdiTypes'] = this.searchForm.changdiTypes\r\n                }\r\n                                                                                                                                                                                                                                                                     \r\n                if (this.searchForm.yonghuName!= '' && this.searchForm.yonghuName!= undefined) {\r\n                    params['yonghuName'] = '%' + this.searchForm.yonghuName + '%'\r\n                }\r\n                     \r\n                if (this.searchForm.yonghuPhone!= '' && this.searchForm.yonghuPhone!= undefined) {\r\n                    params['yonghuPhone'] = '%' + this.searchForm.yonghuPhone + '%'\r\n                }\r\n                     \r\n                if (this.searchForm.yonghuIdNumber!= '' && this.searchForm.yonghuIdNumber!= undefined) {\r\n                    params['yonghuIdNumber'] = '%' + this.searchForm.yonghuIdNumber + '%'\r\n                }\r\n                                                                                                                                \r\n                params['changdiCollectionDelete'] = 1// 逻辑删除字段 1 未删除 2 删除\r\n\r\n\r\n                this.$http({\r\n                    url: \"changdiCollection/page\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.dataList = data.data.list;\r\n                        this.totalPage = data.data.total;\r\n                    }else{\r\n                        this.dataList = [];\r\n                        this.totalPage = 0;\r\n                    }\r\n                    this.dataListLoading = false;\r\n                });\r\n\r\n                //查询级联表搜索条件所有列表\r\n                this.$http({\r\n                    url: \"dictionary/page?dicCode=changdi_types&page=1&limit=100\",\r\n                    method: \"get\",\r\n                    page: 1,\r\n                    limit: 100,\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.changdiTypesSelectSearch = data.data.list;\r\n                    }\r\n                });\r\n                //查询当前表搜索条件所有列表\r\n            },\r\n            //每页数\r\n            sizeChangeHandle(val) {\r\n                this.pageSize = val;\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 当前页\r\n            currentChangeHandle(val) {\r\n                this.pageIndex = val;\r\n                this.getDataList();\r\n            },\r\n            // 多选\r\n            selectionChangeHandler(val) {\r\n                this.dataListSelections = val;\r\n            },\r\n            // 添加/修改\r\n            addOrUpdateHandler(id, type) {\r\n                this.showFlag = false;\r\n                this.addOrUpdateFlag = true;\r\n                this.crossAddOrUpdateFlag = false;\r\n                if (type != 'info') {\r\n                    type = 'else';\r\n                }\r\n                this.$nextTick(() => {\r\n                    this.$refs.addOrUpdate.init(id, type);\r\n                });\r\n            },\r\n            // 下载\r\n            download(file) {\r\n                window.open(\" ${file} \")\r\n            },\r\n            // 删除\r\n            deleteHandler(id) {\r\n                var ids = id ? [Number(id)] : this.dataListSelections.map(item => {\r\n                    return Number(item.id);\r\n                });\r\n\r\n                this.$confirm(`确定进行[${id ? \"删除\" : \"批量删除\"}]操作?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: \"changdiCollection/delete\",\r\n                        method: \"post\",\r\n                        data: ids\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            this.$message({\r\n                                message: \"操作成功\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }else{\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n            // 导入功能上传文件成功后调用导入方法\r\n            changdiCollectionUploadSuccess(data){\r\n                let _this = this;\r\n                _this.$http({\r\n                    url: \"changdiCollection/batchInsert?fileName=\" + data.file,\r\n                    method: \"get\"\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        _this.$message({\r\n                            message: \"导入场地收藏数据成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.search();\r\n                            }\r\n                        });\r\n                    }else{\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n\r\n            },\r\n            // 导入功能上传文件失败后调用导入方法\r\n            changdiCollectionUploadError(data){\r\n                this.$message.error('上传失败');\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.slt {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .ad {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .pages {\r\n    & /deep/ el-pagination__sizes{\r\n      & /deep/ el-input__inner {\r\n        height: 22px;\r\n        line-height: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .el-button+.el-button {\r\n    margin:0;\r\n  }\r\n\r\n  .tables {\r\n\t& /deep/ .el-button--success {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(121, 244, 164, 0.54);\r\n\t}\r\n\r\n\t& /deep/ .el-button--primary {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(169, 115, 232, 0.65);\r\n\t}\r\n\r\n\t& /deep/ .el-button--danger {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(241, 120, 186, 1);\r\n\t}\r\n\r\n    & /deep/ .el-button {\r\n      margin: 4px;\r\n    }\r\n  }\r\n</style>\r\n\r\n\r\n"], "mappings": ";;;;;;;;;;;AAwQA,OAAAA,WAAA;AACA,OAAAC,OAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;QACAC,GAAA;MACA;MACAC,YAAA;MAAA;MACAC,IAAA;MAAA;MACA;MACAC,wBAAA;MACA;MACAC,IAAA;QACAC,EAAA;QACAC,SAAA;QACAC,QAAA;QACAC,sBAAA;QACAC,UAAA;QACAC,UAAA;MACA;MACAC,QAAA;MACAC,SAAA;MACAC,QAAA;MACAC,SAAA;MACAC,eAAA;MACAC,kBAAA;MACAC,QAAA;MACAC,YAAA;MACAC,MAAA;MACAC,aAAA;MACAC,eAAA;MACAC,QAAA;MACAC,OAAA;MAEAC,WAAA,MAAAC,IAAA;MAAA;;MAEA;MACAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAL,QAAA,GAAAzB,OAAA,CAAA+B,SAAA;IACA,KAAAC,IAAA;IACA,KAAAC,WAAA;IACA,KAAAC,kBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAA/B,YAAA,QAAAgC,QAAA,CAAAC,GAAA;IACA,KAAAhC,IAAA,QAAA+B,QAAA,CAAAC,GAAA;EAEA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,OAAA,aAAAA,OAAA;IACA;EACA;EACAC,UAAA;IACA3C,WAAA,EAAAA;EACA;EACA4C,QAAA,GACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,KAAA;MACA,IAAAC,MAAA;QACAC,UAAA;QACAC,IAAA,EAAAH,KAAA,CAAApB,WAAA,CAAAwB,WAAA;QACAC,SAAA;UAAA;UACAC,SAAA;UAAA;UACAC,QAAA;UAAA;UACAC,IAAA;UACA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAR,KAAA,CAAAxB,aAAA;MACAwB,KAAA,CAAAS,SAAA;QACA,IAAAC,SAAA,GAAAX,MAAA,CAAAY,QAAA,CAAA1B,IAAA,CAAA2B,QAAA,CAAAC,cAAA;QACAd,MAAA,CAAAe,KAAA;UACAC,GAAA;UACAC,MAAA;UACAf,MAAA,EAAAA;QACA,GAAAgB,IAAA,WAAAC,IAAA;UAAA,IAAAhE,IAAA,GAAAgE,IAAA,CAAAhE,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;YAGA;YACA;YACA,IAAAC,MAAA;YACAlE,IAAA,CAAAA,IAAA,CAAAmE,KAAA,CAAAC,OAAA,WAAAC,IAAA,EAAAC,KAAA;cACA,IAAAC,OAAA;cACAA,OAAA,CAAAC,IAAA,GAAAxE,IAAA,CAAAA,IAAA,CAAAyE,MAAA,CAAAH,KAAA;cACAC,OAAA,CAAAG,IAAA;cACAH,OAAA,CAAAvE,IAAA,GAAAqE,IAAA;cACAH,MAAA,CAAAS,IAAA,CAAAJ,OAAA;YAEA;YAEA,IAAAK,MAAA;cACAC,OAAA;gBACAC,OAAA;gBACAC,WAAA;kBACAL,IAAA;kBACAM,UAAA;oBACAC,KAAA;kBACA;gBACA;cACA;cACAC,OAAA;gBACAC,OAAA;kBACA;kBACAC,SAAA;oBAAAC,IAAA;oBAAAX,IAAA;kBAAA;kBAAA;kBACA;kBACAY,WAAA;oBAAAD,IAAA;kBAAA;gBACA;cACA;cACAZ,MAAA;gBACAzE,IAAA,EAAAA,IAAA,CAAAA,IAAA,CAAAyE,MAAA;cACA;cACAc,KAAA,GACA;gBACAb,IAAA;gBACAF,IAAA;gBACAxE,IAAA,EAAAA,IAAA,CAAAA,IAAA,CAAAuF,KAAA;gBACAR,WAAA;kBACAL,IAAA;gBACA;cACA,EACA;cACAP,KAAA,GACA;gBACAO,IAAA;gBAAA;gBACAF,IAAA;gBAAA;gBACAgB,SAAA;kBACAC,SAAA;gBACA;cACA,EACA;cACAvB,MAAA,EAAAA,MAAA;YACA;YACA;YACAV,SAAA,CAAAkC,SAAA,CAAAd,MAAA;YACA;YACAe,MAAA,CAAAC,QAAA;cACApC,SAAA,CAAAqC,MAAA;YACA;YACA;;YAIA;YACA;YACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;YAEA;UACA;YACAhD,MAAA,CAAAiD,QAAA;cACAC,OAAA;cACArB,IAAA;cACAsB,QAAA;cACAC,OAAA,WAAAA,QAAA;gBACApD,MAAA,CAAAqD,MAAA;cACA;YACA;UACA;QACA;MACA;MACA,KAAA5E,aAAA,SAAAA,aAAA;MACA,KAAAiC,SAAA;QACA,IAAAC,SAAA,GAAAX,MAAA,CAAAY,QAAA,CAAA1B,IAAA,CAAA2B,QAAA,CAAAC,cAAA;QACAd,MAAA,CAAAe,KAAA;UACAC,GAAA;UACAC,MAAA;QACA,GAAAC,IAAA,WAAAoC,KAAA;UAAA,IAAAnG,IAAA,GAAAmG,KAAA,CAAAnG,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;YACA,IAAAmC,GAAA,GAAApG,IAAA,CAAAA,IAAA;YACA,IAAAuF,KAAA;YACA,IAAApB,KAAA;YACA,IAAAkC,MAAA;YACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,GAAA,CAAAG,MAAA,EAAAD,CAAA;cACAf,KAAA,CAAAZ,IAAA,CAAAyB,GAAA,CAAAE,CAAA,EAAAE,KAAA;cACArC,KAAA,CAAAQ,IAAA,CAAAyB,GAAA,CAAAE,CAAA,EAAAG,KAAA;cACAJ,MAAA,CAAA1B,IAAA;gBACA+B,KAAA,EAAAN,GAAA,CAAAE,CAAA,EAAAG,KAAA;gBACAjC,IAAA,EAAA4B,GAAA,CAAAE,CAAA,EAAAE;cACA;cACA,IAAA5B,MAAA;cACAA,MAAA;gBACA+B,KAAA;kBACAC,IAAA;kBACAC,IAAA;gBACA;gBACAhC,OAAA;kBACAC,OAAA;kBACAW,SAAA;gBACA;gBACAvB,MAAA;kBACAQ,IAAA;kBACAoC,MAAA;kBACAC,MAAA;kBACA/G,IAAA,EAAAqG,MAAA;kBACAW,QAAA;oBACAC,SAAA;sBACAC,UAAA;sBACAC,aAAA;sBACAC,WAAA;oBACA;kBACA;gBACA;cACA;cACA;cACA5D,SAAA,CAAAkC,SAAA,CAAAd,MAAA;cACA;cACAe,MAAA,CAAAC,QAAA;gBACApC,SAAA,CAAAqC,MAAA;cACA;YACA;UACA;QACA;QACA;MACA;IACA;IACA5D,kBAAA,WAAAA,mBAAA;MACA,KAAAoF,wBAAA;MACA,KAAAC,0BAAA;MACA,KAAAC,2BAAA;MACA,KAAAC,0BAAA;MACA,KAAAC,sBAAA;IACA;IACAJ,wBAAA,WAAAA,yBAAA;MAAA,IAAAK,MAAA;MACA,KAAAnE,SAAA;QACAG,QAAA,CAAAiE,gBAAA,wCAAAvD,OAAA,WAAAwD,EAAA;UACA,IAAAC,SAAA;UACA,IAAAH,MAAA,CAAAlG,QAAA,CAAAsG,iBAAA,OACAD,SAAA;UACA,IAAAH,MAAA,CAAAlG,QAAA,CAAAsG,iBAAA,OAAAD,SAAA;UACAD,EAAA,CAAAG,KAAA,CAAAF,SAAA,GAAAA,SAAA;UACAD,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAAN,MAAA,CAAAlG,QAAA,CAAAyG,WAAA;UACAL,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAlG,QAAA,CAAAyG,WAAA;UACAL,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAyC,MAAA,CAAAlG,QAAA,CAAA2G,cAAA;UACAP,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAAV,MAAA,CAAAlG,QAAA,CAAA6G,aAAA;UACAT,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAZ,MAAA,CAAAlG,QAAA,CAAA+G,gBAAA;UACAX,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAd,MAAA,CAAAlG,QAAA,CAAAiH,gBAAA;UACAb,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAhB,MAAA,CAAAlG,QAAA,CAAAmH,gBAAA;UACAf,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAlB,MAAA,CAAAlG,QAAA,CAAAqH,iBAAA;UACAjB,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAApB,MAAA,CAAAlG,QAAA,CAAAuH,YAAA;QACA;QACA,IAAArB,MAAA,CAAAlG,QAAA,CAAAwH,UAAA;UACAtF,QAAA,CAAAiE,gBAAA,4CAAAvD,OAAA,WAAAwD,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAyC,MAAA,CAAAlG,QAAA,CAAAyH,eAAA;YACArB,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAAV,MAAA,CAAAlG,QAAA,CAAA0H,cAAA;YACAtB,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAlG,QAAA,CAAAyG,WAAA;UACA;QACA;QACAkB,UAAA;UACAzF,QAAA,CAAAiE,gBAAA,yCAAAvD,OAAA,WAAAwD,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAyC,MAAA,CAAAlG,QAAA,CAAA4H,cAAA;YACAxB,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAlG,QAAA,CAAAyG,WAAA;UACA;UACAvE,QAAA,CAAAiE,gBAAA,yCAAAvD,OAAA,WAAAwD,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAyC,MAAA,CAAAlG,QAAA,CAAA4H,cAAA;YACAxB,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAlG,QAAA,CAAAyG,WAAA;UACA;UACAvE,QAAA,CAAAiE,gBAAA,uCAAAvD,OAAA,WAAAwD,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAAG,UAAA,GAAAR,MAAA,CAAAlG,QAAA,CAAAyG,WAAA;UACA;QACA;MACA;IACA;IACA;IACAV,2BAAA,WAAAA,4BAAA;MAAA,IAAA8B,MAAA;MACA,KAAA9F,SAAA;QACAG,QAAA,CAAAiE,gBAAA,2CAAAvD,OAAA,WAAAwD,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAAqB,MAAA,CAAA7H,QAAA,CAAA8H,eAAA;UACA1B,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAAoE,MAAA,CAAA7H,QAAA,CAAA+H,kBAAA;UACA3B,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAAiB,MAAA,CAAA7H,QAAA,CAAAgI,iBAAA;UACA5B,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAe,MAAA,CAAA7H,QAAA,CAAAiI,oBAAA;UACA7B,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAa,MAAA,CAAA7H,QAAA,CAAAkI,oBAAA;UACA9B,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAW,MAAA,CAAA7H,QAAA,CAAAmI,oBAAA;UACA/B,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAS,MAAA,CAAA7H,QAAA,CAAAoI,qBAAA;UACAhC,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAAO,MAAA,CAAA7H,QAAA,CAAAqI,gBAAA;QACA;MACA;IACA;IACA;IACAvC,0BAAA,WAAAA,2BAAA;MAAA,IAAAwC,MAAA;MACA,KAAAvG,SAAA;QACAG,QAAA,CAAAiE,gBAAA,0CAAAvD,OAAA,WAAAwD,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAA8B,MAAA,CAAAtI,QAAA,CAAAuI,cAAA;UACAnC,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAA6E,MAAA,CAAAtI,QAAA,CAAAwI,oBAAA;UACApC,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAA0B,MAAA,CAAAtI,QAAA,CAAAyI,gBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAwB,MAAA,CAAAtI,QAAA,CAAA0I,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAsB,MAAA,CAAAtI,QAAA,CAAA2I,mBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAoB,MAAA,CAAAtI,QAAA,CAAA4I,mBAAA;UACAxC,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAkB,MAAA,CAAAtI,QAAA,CAAA6I,oBAAA;UACAzC,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAAgB,MAAA,CAAAtI,QAAA,CAAA8I,kBAAA;QACA;QACA5G,QAAA,CAAAiE,gBAAA,yCAAAvD,OAAA,WAAAwD,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAA8B,MAAA,CAAAtI,QAAA,CAAAuI,cAAA;UACAnC,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAA6E,MAAA,CAAAtI,QAAA,CAAA+I,oBAAA;UACA3C,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAA0B,MAAA,CAAAtI,QAAA,CAAAyI,gBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAwB,MAAA,CAAAtI,QAAA,CAAA0I,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAsB,MAAA,CAAAtI,QAAA,CAAA2I,mBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAoB,MAAA,CAAAtI,QAAA,CAAA4I,mBAAA;UACAxC,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAkB,MAAA,CAAAtI,QAAA,CAAA6I,oBAAA;UACAzC,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAAgB,MAAA,CAAAtI,QAAA,CAAAgJ,kBAAA;QACA;QACA9G,QAAA,CAAAiE,gBAAA,0CAAAvD,OAAA,WAAAwD,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,GAAA8B,MAAA,CAAAtI,QAAA,CAAAuI,cAAA;UACAnC,EAAA,CAAAG,KAAA,CAAA9C,KAAA,GAAA6E,MAAA,CAAAtI,QAAA,CAAAiJ,qBAAA;UACA7C,EAAA,CAAAG,KAAA,CAAAK,QAAA,GAAA0B,MAAA,CAAAtI,QAAA,CAAAyI,gBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAO,WAAA,GAAAwB,MAAA,CAAAtI,QAAA,CAAA0I,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAS,WAAA,GAAAsB,MAAA,CAAAtI,QAAA,CAAA2I,mBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAW,WAAA,GAAAoB,MAAA,CAAAtI,QAAA,CAAA4I,mBAAA;UACAxC,EAAA,CAAAG,KAAA,CAAAa,YAAA,GAAAkB,MAAA,CAAAtI,QAAA,CAAA6I,oBAAA;UACAzC,EAAA,CAAAG,KAAA,CAAAe,eAAA,GAAAgB,MAAA,CAAAtI,QAAA,CAAAkJ,mBAAA;QACA;MACA;IACA;IACA;IACAC,QAAA,WAAAA,SAAAC,KAAA;MAAA,IAAAC,GAAA,GAAAD,KAAA,CAAAC,GAAA;QAAAC,QAAA,GAAAF,KAAA,CAAAE,QAAA;MACA,IAAAA,QAAA;QACA,SAAAtJ,QAAA,CAAAuJ,WAAA;UACA;YAAA9F,KAAA,OAAAzD,QAAA,CAAAwJ;UAAA;QACA;MACA;QACA;MACA;IACA;IACAC,SAAA,WAAAA,UAAAC,KAAA;MAAA,IAAAL,GAAA,GAAAK,KAAA,CAAAL,GAAA;QAAAC,QAAA,GAAAI,KAAA,CAAAJ,QAAA;MACA,IAAAA,QAAA;QACA,SAAAtJ,QAAA,CAAAuJ,WAAA;UACA;YAAAjC,eAAA,OAAAtH,QAAA,CAAA2J;UAAA;QACA;MACA;QACA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAR,GAAA,GAAAQ,KAAA,CAAAR,GAAA;QAAAC,QAAA,GAAAO,KAAA,CAAAP,QAAA;MACA;QAAA7F,KAAA,OAAAzD,QAAA,CAAA8J;MAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA;MAAA,IAAAX,GAAA,GAAAW,KAAA,CAAAX,GAAA;QAAAC,QAAA,GAAAU,KAAA,CAAAV,QAAA;MACA;QAAAhC,eAAA,OAAAtH,QAAA,CAAAiK;MAAA;IACA;IACA;IACAjE,0BAAA,WAAAA,2BAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;IAAA,CACA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MACA,IAAAiE,GAAA;MACA,SAAAlK,QAAA,CAAAmK,SAAA,EAAAD,GAAA,CAAA/G,IAAA;MACA,SAAAnD,QAAA,CAAAoK,SAAA,EAAAF,GAAA,CAAA/G,IAAA;MACA,SAAAnD,QAAA,CAAAqK,YAAA;QACAH,GAAA,CAAA/G,IAAA;QACA,SAAAnD,QAAA,CAAAsK,SAAA,EAAAJ,GAAA,CAAA/G,IAAA;QACA+G,GAAA,CAAA/G,IAAA;MACA;MACA,SAAAnD,QAAA,CAAAuK,UAAA,EAAAL,GAAA,CAAA/G,IAAA;MACA,KAAAlD,OAAA,GAAAiK,GAAA,CAAAM,IAAA;MACA,KAAAxK,QAAA,CAAAyK,WAAA;IACA;IAEAlK,IAAA,WAAAA,KAAA,GACA;IACAmE,MAAA,WAAAA,OAAA;MACA,KAAApF,SAAA;MACA,KAAAkB,WAAA;IACA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAkK,MAAA;MACA,KAAAjL,eAAA;MACA,IAAA8B,MAAA;QACAoJ,IAAA,OAAArL,SAAA;QACAsL,KAAA,OAAArL,QAAA;QACAsL,IAAA;QACA3L,sBAAA;MACA;MAGA,SAAAT,UAAA,CAAAqM,WAAA,eAAArM,UAAA,CAAAqM,WAAA,IAAAC,SAAA;QACAxJ,MAAA,6BAAA9C,UAAA,CAAAqM,WAAA;MACA;MAEA,SAAArM,UAAA,CAAAuM,YAAA,eAAAvM,UAAA,CAAAuM,YAAA,IAAAD,SAAA;QACAxJ,MAAA,wBAAA9C,UAAA,CAAAuM,YAAA;MACA;MAEA,SAAAvM,UAAA,CAAAwM,UAAA,eAAAxM,UAAA,CAAAwM,UAAA,IAAAF,SAAA;QACAxJ,MAAA,4BAAA9C,UAAA,CAAAwM,UAAA;MACA;MAEA,SAAAxM,UAAA,CAAAyM,WAAA,eAAAzM,UAAA,CAAAyM,WAAA,IAAAH,SAAA;QACAxJ,MAAA,6BAAA9C,UAAA,CAAAyM,WAAA;MACA;MAEA,SAAAzM,UAAA,CAAA0M,cAAA,eAAA1M,UAAA,CAAA0M,cAAA,IAAAJ,SAAA;QACAxJ,MAAA,gCAAA9C,UAAA,CAAA0M,cAAA;MACA;MAEA5J,MAAA;;MAGA,KAAAa,KAAA;QACAC,GAAA;QACAC,MAAA;QACAf,MAAA,EAAAA;MACA,GAAAgB,IAAA,WAAA6I,KAAA;QAAA,IAAA5M,IAAA,GAAA4M,KAAA,CAAA5M,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;UACAiI,MAAA,CAAArL,QAAA,GAAAb,IAAA,CAAAA,IAAA,CAAA6M,IAAA;UACAX,MAAA,CAAAlL,SAAA,GAAAhB,IAAA,CAAAA,IAAA,CAAAyG,KAAA;QACA;UACAyF,MAAA,CAAArL,QAAA;UACAqL,MAAA,CAAAlL,SAAA;QACA;QACAkL,MAAA,CAAAjL,eAAA;MACA;;MAEA;MACA,KAAA2C,KAAA;QACAC,GAAA;QACAC,MAAA;QACAqI,IAAA;QACAC,KAAA;MACA,GAAArI,IAAA,WAAA+I,KAAA;QAAA,IAAA9M,IAAA,GAAA8M,KAAA,CAAA9M,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;UACAiI,MAAA,CAAA7L,wBAAA,GAAAL,IAAA,CAAAA,IAAA,CAAA6M,IAAA;QACA;MACA;MACA;IACA;IACA;IACAE,gBAAA,WAAAA,iBAAAxK,GAAA;MACA,KAAAxB,QAAA,GAAAwB,GAAA;MACA,KAAAzB,SAAA;MACA,KAAAkB,WAAA;IACA;IACA;IACAgL,mBAAA,WAAAA,oBAAAzK,GAAA;MACA,KAAAzB,SAAA,GAAAyB,GAAA;MACA,KAAAP,WAAA;IACA;IACA;IACAiL,sBAAA,WAAAA,uBAAA1K,GAAA;MACA,KAAArB,kBAAA,GAAAqB,GAAA;IACA;IACA;IACA2K,kBAAA,WAAAA,mBAAA3M,EAAA,EAAAmE,IAAA;MAAA,IAAAyI,MAAA;MACA,KAAAhM,QAAA;MACA,KAAAI,eAAA;MACA,KAAA6L,oBAAA;MACA,IAAA1I,IAAA;QACAA,IAAA;MACA;MACA,KAAAnB,SAAA;QACA4J,MAAA,CAAAE,KAAA,CAAAC,WAAA,CAAAvL,IAAA,CAAAxB,EAAA,EAAAmE,IAAA;MACA;IACA;IACA;IACA6I,QAAA,WAAAA,SAAAC,IAAA;MACA7H,MAAA,CAAA8H,IAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAAnN,EAAA;MAAA,IAAAoN,MAAA;MACA,IAAAC,GAAA,GAAArN,EAAA,IAAAsN,MAAA,CAAAtN,EAAA,UAAAW,kBAAA,CAAA4M,GAAA,WAAAzJ,IAAA;QACA,OAAAwJ,MAAA,CAAAxJ,IAAA,CAAA9D,EAAA;MACA;MAEA,KAAAwN,QAAA,6BAAAC,MAAA,CAAAzN,EAAA;QACA0N,iBAAA;QACAC,gBAAA;QACAxJ,IAAA;MACA,GAAAX,IAAA;QACA4J,MAAA,CAAA/J,KAAA;UACAC,GAAA;UACAC,MAAA;UACA9D,IAAA,EAAA4N;QACA,GAAA7J,IAAA,WAAAoK,KAAA;UAAA,IAAAnO,IAAA,GAAAmO,KAAA,CAAAnO,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;YACA0J,MAAA,CAAA7H,QAAA;cACAC,OAAA;cACArB,IAAA;cACAsB,QAAA;cACAC,OAAA,WAAAA,QAAA;gBACA0H,MAAA,CAAAzH,MAAA;cACA;YACA;UACA;YACAyH,MAAA,CAAA7H,QAAA,CAAAsI,KAAA,CAAApO,IAAA,CAAAqO,GAAA;UACA;QACA;MACA;IACA;IACA;IACAC,8BAAA,WAAAA,+BAAAtO,IAAA;MACA,IAAA8C,KAAA;MACAA,KAAA,CAAAc,KAAA;QACAC,GAAA,8CAAA7D,IAAA,CAAAwN,IAAA;QACA1J,MAAA;MACA,GAAAC,IAAA,WAAAwK,MAAA;QAAA,IAAAvO,IAAA,GAAAuO,MAAA,CAAAvO,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;UACAnB,KAAA,CAAAgD,QAAA;YACAC,OAAA;YACArB,IAAA;YACAsB,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAnD,KAAA,CAAAoD,MAAA;YACA;UACA;QACA;UACApD,KAAA,CAAAgD,QAAA,CAAAsI,KAAA,CAAApO,IAAA,CAAAqO,GAAA;QACA;MACA;IAEA;IACA;IACAG,4BAAA,WAAAA,6BAAAxO,IAAA;MACA,KAAA8F,QAAA,CAAAsI,KAAA;IACA;EACA;AACA", "ignoreList": []}]}