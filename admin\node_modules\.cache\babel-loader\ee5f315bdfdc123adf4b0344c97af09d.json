{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\common\\FileUpload.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\common\\FileUpload.vue", "mtime": 1642386765154}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["storage", "base", "data", "dialogVisible", "dialogImageUrl", "fileList", "fileUrlList", "myHeaders", "props", "mounted", "init", "get", "watch", "fileUrls", "val", "oldVal", "computed", "getActionUrl", "concat", "$base", "name", "action", "methods", "split", "fileArray", "for<PERSON>ach", "item", "index", "url", "file", "push", "setFileList", "handleBeforeUpload", "handleUploadSuccess", "res", "code", "length", "response", "$emit", "join", "$message", "error", "msg", "handleUploadErr", "err", "handleRemove", "handleUploadPreview", "handleExceed", "files", "warning", "limit", "fileUrlArray", "token"], "sources": ["src/components/common/FileUpload.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 上传文件组件 -->\r\n    <el-upload\r\n      ref=\"upload\"\r\n      :action=\"getActionUrl\"\r\n      list-type=\"picture-card\"\r\n      :multiple=\"multiple\"\r\n      :limit=\"limit\"\r\n      :headers=\"myHeaders\"\r\n      :file-list=\"fileList\"\r\n      :on-exceed=\"handleExceed\"\r\n      :on-preview=\"handleUploadPreview\"\r\n      :on-remove=\"handleRemove\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :on-error=\"handleUploadErr\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n    >\r\n      <i class=\"el-icon-plus\"></i>\r\n      <div slot=\"tip\" class=\"el-upload__tip\" style=\"color:#838fa1;\">{{tip}}</div>\r\n    </el-upload>\r\n    <el-dialog :visible.sync=\"dialogVisible\" size=\"tiny\" append-to-body>\r\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport storage from \"@/utils/storage\";\r\nimport base from \"@/utils/base\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 查看大图\r\n      dialogVisible: false,\r\n      // 查看大图\r\n      dialogImageUrl: \"\",\r\n      // 组件渲染图片的数组字段，有特殊格式要求\r\n      fileList: [],\r\n      fileUrlList: [],\r\n      myHeaders:{}\r\n    };\r\n  },\r\n  props: [\"tip\", \"action\", \"limit\", \"multiple\", \"fileUrls\"],\r\n  mounted() {\r\n    this.init();\r\n    this.myHeaders= {\r\n      'Token':storage.get(\"Token\")\r\n    }\r\n  },\r\n  watch: {\r\n    fileUrls: function(val, oldVal) {\r\n      //   console.log(\"new: %s, old: %s\", val, oldVal);\r\n      this.init();\r\n    }\r\n  },\r\n  computed: {\r\n    // 计算属性的 getter\r\n    getActionUrl: function() {\r\n      // return base.url + this.action + \"?token=\" + storage.get(\"token\");\r\n      return `/${this.$base.name}/` + this.action;\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化\r\n    init() {\r\n      //   console.log(this.fileUrls);\r\n      if (this.fileUrls) {\r\n        this.fileUrlList = this.fileUrls.split(\",\");\r\n        let fileArray = [];\r\n        this.fileUrlList.forEach(function(item, index) {\r\n          var url = item;\r\n          var name = index;\r\n          var file = {\r\n            name: name,\r\n            url: url\r\n          };\r\n          fileArray.push(file);\r\n        });\r\n        this.setFileList(fileArray);\r\n      }\r\n    },\r\n    handleBeforeUpload(file) {\r\n\t\r\n    },\r\n    // 上传文件成功后执行\r\n    handleUploadSuccess(res, file, fileList) {\r\n      if (res && res.code === 0) {\r\n        fileList[fileList.length - 1][\"url\"] =\r\n          this.$base.url + \"upload/\" + file.response.file;\r\n        this.setFileList(fileList);\r\n        this.$emit(\"change\", this.fileUrlList.join(\",\"));\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    // 图片上传失败\r\n    handleUploadErr(err, file, fileList) {\r\n      this.$message.error(\"文件上传失败\");\r\n    },\r\n    // 移除图片\r\n    handleRemove(file, fileList) {\r\n      this.setFileList(fileList);\r\n      this.$emit(\"change\", this.fileUrlList.join(\",\"));\r\n    },\r\n    // 查看大图\r\n    handleUploadPreview(file) {\r\n      this.dialogImageUrl = file.url;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 限制图片数量\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning(`最多上传${this.limit}张图片`);\r\n    },\r\n    // 重新对fileList进行赋值\r\n    setFileList(fileList) {\r\n      var fileArray = [];\r\n      var fileUrlArray = [];\r\n      // 有些图片不是公开的，所以需要携带token信息做权限校验\r\n      var token = storage.get(\"token\");\r\n      fileList.forEach(function(item, index) {\r\n        var url = item.url.split(\"?\")[0];\r\n        var name = item.name;\r\n        var file = {\r\n          name: name,\r\n          url: url + \"?token=\" + token\r\n        };\r\n        fileArray.push(file);\r\n        fileUrlArray.push(url);\r\n      });\r\n      this.fileList = fileArray;\r\n      this.fileUrlList = fileUrlArray;\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n"], "mappings": ";;;;;;AA2BA,OAAAA,OAAA;AACA,OAAAC,IAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,aAAA;MACA;MACAC,cAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;IACA,KAAAH,SAAA;MACA,SAAAP,OAAA,CAAAW,GAAA;IACA;EACA;EACAC,KAAA;IACAC,QAAA,WAAAA,SAAAC,GAAA,EAAAC,MAAA;MACA;MACA,KAAAL,IAAA;IACA;EACA;EACAM,QAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA;MACA,WAAAC,MAAA,MAAAC,KAAA,CAAAC,IAAA,cAAAC,MAAA;IACA;EACA;EACAC,OAAA;IACA;IACAZ,IAAA,WAAAA,KAAA;MACA;MACA,SAAAG,QAAA;QACA,KAAAP,WAAA,QAAAO,QAAA,CAAAU,KAAA;QACA,IAAAC,SAAA;QACA,KAAAlB,WAAA,CAAAmB,OAAA,WAAAC,IAAA,EAAAC,KAAA;UACA,IAAAC,GAAA,GAAAF,IAAA;UACA,IAAAN,IAAA,GAAAO,KAAA;UACA,IAAAE,IAAA;YACAT,IAAA,EAAAA,IAAA;YACAQ,GAAA,EAAAA;UACA;UACAJ,SAAA,CAAAM,IAAA,CAAAD,IAAA;QACA;QACA,KAAAE,WAAA,CAAAP,SAAA;MACA;IACA;IACAQ,kBAAA,WAAAA,mBAAAH,IAAA,GAEA;IACA;IACAI,mBAAA,WAAAA,oBAAAC,GAAA,EAAAL,IAAA,EAAAxB,QAAA;MACA,IAAA6B,GAAA,IAAAA,GAAA,CAAAC,IAAA;QACA9B,QAAA,CAAAA,QAAA,CAAA+B,MAAA,eACA,KAAAjB,KAAA,CAAAS,GAAA,eAAAC,IAAA,CAAAQ,QAAA,CAAAR,IAAA;QACA,KAAAE,WAAA,CAAA1B,QAAA;QACA,KAAAiC,KAAA,gBAAAhC,WAAA,CAAAiC,IAAA;MACA;QACA,KAAAC,QAAA,CAAAC,KAAA,CAAAP,GAAA,CAAAQ,GAAA;MACA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAAC,GAAA,EAAAf,IAAA,EAAAxB,QAAA;MACA,KAAAmC,QAAA,CAAAC,KAAA;IACA;IACA;IACAI,YAAA,WAAAA,aAAAhB,IAAA,EAAAxB,QAAA;MACA,KAAA0B,WAAA,CAAA1B,QAAA;MACA,KAAAiC,KAAA,gBAAAhC,WAAA,CAAAiC,IAAA;IACA;IACA;IACAO,mBAAA,WAAAA,oBAAAjB,IAAA;MACA,KAAAzB,cAAA,GAAAyB,IAAA,CAAAD,GAAA;MACA,KAAAzB,aAAA;IACA;IACA;IACA4C,YAAA,WAAAA,aAAAC,KAAA,EAAA3C,QAAA;MACA,KAAAmC,QAAA,CAAAS,OAAA,4BAAA/B,MAAA,MAAAgC,KAAA;IACA;IACA;IACAnB,WAAA,WAAAA,YAAA1B,QAAA;MACA,IAAAmB,SAAA;MACA,IAAA2B,YAAA;MACA;MACA,IAAAC,KAAA,GAAApD,OAAA,CAAAW,GAAA;MACAN,QAAA,CAAAoB,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACA,IAAAC,GAAA,GAAAF,IAAA,CAAAE,GAAA,CAAAL,KAAA;QACA,IAAAH,IAAA,GAAAM,IAAA,CAAAN,IAAA;QACA,IAAAS,IAAA;UACAT,IAAA,EAAAA,IAAA;UACAQ,GAAA,EAAAA,GAAA,eAAAwB;QACA;QACA5B,SAAA,CAAAM,IAAA,CAAAD,IAAA;QACAsB,YAAA,CAAArB,IAAA,CAAAF,GAAA;MACA;MACA,KAAAvB,QAAA,GAAAmB,SAAA;MACA,KAAAlB,WAAA,GAAA6C,YAAA;IACA;EACA;AACA", "ignoreList": []}]}