{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\center.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\center.vue", "mtime": 1642386767421}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["isNumber", "isIntNumer", "isEmail", "isMobile", "isPhone", "isURL", "checkIdCard", "data", "ruleForm", "flag", "usersFlag", "sexTypesOptions", "mounted", "_this", "table", "$storage", "get", "sessionTable", "role", "$http", "url", "concat", "method", "then", "_ref", "code", "$message", "error", "msg", "_ref2", "list", "methods", "yonghuPhotoUploadChange", "fileUrls", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onUpdateHandler", "_this2", "yo<PERSON><PERSON><PERSON><PERSON>", "yonghuPhone", "yonghuIdNumber", "yonghuEmail", "sexTypes", "username", "trim", "length", "_ref3", "message", "type", "duration", "onClose"], "sources": ["src/views/center.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      class=\"detail-form-content\"\r\n      ref=\"ruleForm\"\r\n      :model=\"ruleForm\"\r\n      label-width=\"80px\"\r\n    >  \r\n     <el-row>\r\n                    <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='用户姓名' prop=\"yonghuName\">\r\n               <el-input v-model=\"ruleForm.yonghuName\"  placeholder='用户姓名' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='用户手机号' prop=\"yonghuPhone\">\r\n               <el-input v-model=\"ruleForm.yonghuPhone\"  placeholder='用户手机号' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='用户身份证号' prop=\"yonghuIdNumber\">\r\n               <el-input v-model=\"ruleForm.yonghuIdNumber\"  placeholder='用户身份证号' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-col :span=\"24\">\r\n             <el-form-item v-if=\"flag=='yonghu'\" label='用户头像' prop=\"yonghuPhoto\">\r\n                 <file-upload\r\n                         tip=\"点击上传照片\"\r\n                         action=\"file/upload\"\r\n                         :limit=\"3\"\r\n                         :multiple=\"true\"\r\n                         :fileUrls=\"ruleForm.yonghuPhoto?ruleForm.yonghuPhoto:''\"\r\n                         @change=\"yonghuPhotoUploadChange\"\r\n                 ></file-upload>\r\n             </el-form-item>\r\n         </el-col>\r\n         <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='电子邮箱' prop=\"yonghuEmail\">\r\n               <el-input v-model=\"ruleForm.yonghuEmail\"  placeholder='电子邮箱' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-form-item v-if=\"flag=='users'\" label=\"用户名\" prop=\"username\">\r\n             <el-input v-model=\"ruleForm.username\"\r\n                       placeholder=\"用户名\"></el-input>\r\n         </el-form-item>\r\n         <el-col :span=\"12\">\r\n             <el-form-item v-if=\"flag!='users'\"  label=\"性别\" prop=\"sexTypes\">\r\n                 <el-select v-model=\"ruleForm.sexTypes\" placeholder=\"请选择性别\">\r\n                     <el-option\r\n                             v-for=\"(item,index) in sexTypesOptions\"\r\n                             v-bind:key=\"item.codeIndex\"\r\n                             :label=\"item.indexName\"\r\n                             :value=\"item.codeIndex\">\r\n                     </el-option>\r\n                 </el-select>\r\n             </el-form-item>\r\n         </el-col>\r\n         <el-col :span=\"24\">\r\n             <el-form-item>\r\n                 <el-button type=\"primary\" @click=\"onUpdateHandler\">修 改</el-button>\r\n             </el-form-item>\r\n         </el-col>\r\n     </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isMobile,isPhone,isURL,checkIdCard } from \"@/utils/validate\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      ruleForm: {},\r\n      flag: '',\r\n      usersFlag: false,\r\n      sexTypesOptions : [],\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n    };\r\n  },\r\n  mounted() {\r\n    //获取当前登录用户的信息\r\n    var table = this.$storage.get(\"sessionTable\");\r\n    this.sessionTable = this.$storage.get(\"sessionTable\");\r\n    this.role = this.$storage.get(\"role\");\r\n    if (this.role != \"管理员\"){\r\n    }\r\n\r\n    this.flag = table;\r\n    this.$http({\r\n      url: `${this.$storage.get(\"sessionTable\")}/session`,\r\n      method: \"get\"\r\n    }).then(({ data }) => {\r\n      if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n      } else {\r\n        this.$message.error(data.msg);\r\n      }\r\n    });\r\n      this.$http({\r\n          url: `dictionary/page?page=1&limit=100&sort=&order=&dicCode=sex_types`,\r\n          method: \"get\"\r\n      }).then(({ data }) => {\r\n          if (data && data.code === 0) {\r\n          this.sexTypesOptions = data.data.list;\r\n      } else {\r\n          this.$message.error(data.msg);\r\n      }\r\n  });\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  },\r\n  methods: {\r\n    yonghuPhotoUploadChange(fileUrls) {\r\n        this.ruleForm.yonghuPhoto = fileUrls;\r\n    },\r\n\r\n    onUpdateHandler() {\r\n                         if((!this.ruleForm.yonghuName)&& 'yonghu'==this.flag){\r\n                             this.$message.error('用户姓名不能为空');\r\n                             return\r\n                         }\r\n\r\n                             if( 'yonghu' ==this.flag && this.ruleForm.yonghuPhone&&(!isMobile(this.ruleForm.yonghuPhone))){\r\n                                 this.$message.error(`手机应输入手机格式`);\r\n                                 return\r\n                             }\r\n                         if((!this.ruleForm.yonghuIdNumber)&& 'yonghu'==this.flag){\r\n                             this.$message.error('用户身份证号不能为空');\r\n                             return\r\n                         }\r\n\r\n                         if((!this.ruleForm.yonghuPhoto)&& 'yonghu'==this.flag){\r\n                             this.$message.error('用户头像不能为空');\r\n                             return\r\n                         }\r\n\r\n                             if( 'yonghu' ==this.flag && this.ruleForm.yonghuEmail&&(!isEmail(this.ruleForm.yonghuEmail))){\r\n                                 this.$message.error(`邮箱应输入邮箱格式`);\r\n                                 return\r\n                             }\r\n        if((!this.ruleForm.sexTypes)&& this.flag !='users'){\r\n            this.$message.error('性别不能为空');\r\n            return\r\n        }\r\n      if('users'==this.flag && this.ruleForm.username.trim().length<1) {\r\n        this.$message.error(`用户名不能为空`);\r\n        return\t\r\n      }\r\n      this.$http({\r\n        url: `${this.$storage.get(\"sessionTable\")}/update`,\r\n        method: \"post\",\r\n        data: this.ruleForm\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n          this.$message({\r\n            message: \"修改信息成功\",\r\n            type: \"success\",\r\n            duration: 1500,\r\n            onClose: () => {\r\n            }\r\n          });\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n"], "mappings": ";AAuEA;AACA,SAAAA,QAAA,EAAAC,UAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,OAAA,EAAAC,KAAA,EAAAC,WAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,SAAA;MACAC,eAAA;IASA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,IAAAC,KAAA,QAAAC,QAAA,CAAAC,GAAA;IACA,KAAAC,YAAA,QAAAF,QAAA,CAAAC,GAAA;IACA,KAAAE,IAAA,QAAAH,QAAA,CAAAC,GAAA;IACA,SAAAE,IAAA,YACA;IAEA,KAAAT,IAAA,GAAAK,KAAA;IACA,KAAAK,KAAA;MACAC,GAAA,KAAAC,MAAA,MAAAN,QAAA,CAAAC,GAAA;MACAM,MAAA;IACA,GAAAC,IAAA,WAAAC,IAAA;MAAA,IAAAjB,IAAA,GAAAiB,IAAA,CAAAjB,IAAA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAkB,IAAA;QACAZ,KAAA,CAAAL,QAAA,GAAAD,IAAA,CAAAA,IAAA;MACA;QACAM,KAAA,CAAAa,QAAA,CAAAC,KAAA,CAAApB,IAAA,CAAAqB,GAAA;MACA;IACA;IACA,KAAAT,KAAA;MACAC,GAAA;MACAE,MAAA;IACA,GAAAC,IAAA,WAAAM,KAAA;MAAA,IAAAtB,IAAA,GAAAsB,KAAA,CAAAtB,IAAA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAkB,IAAA;QACAZ,KAAA,CAAAF,eAAA,GAAAJ,IAAA,CAAAA,IAAA,CAAAuB,IAAA;MACA;QACAjB,KAAA,CAAAa,QAAA,CAAAC,KAAA,CAAApB,IAAA,CAAAqB,GAAA;MACA;IACA;EASA;EACAG,OAAA;IACAC,uBAAA,WAAAA,wBAAAC,QAAA;MACA,KAAAzB,QAAA,CAAA0B,WAAA,GAAAD,QAAA;IACA;IAEAE,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,UAAA5B,QAAA,CAAA6B,UAAA,qBAAA5B,IAAA;QACA,KAAAiB,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,qBAAAlB,IAAA,SAAAD,QAAA,CAAA8B,WAAA,KAAAnC,QAAA,MAAAK,QAAA,CAAA8B,WAAA;QACA,KAAAZ,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAnB,QAAA,CAAA+B,cAAA,qBAAA9B,IAAA;QACA,KAAAiB,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,UAAAnB,QAAA,CAAA0B,WAAA,qBAAAzB,IAAA;QACA,KAAAiB,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,qBAAAlB,IAAA,SAAAD,QAAA,CAAAgC,WAAA,KAAAtC,OAAA,MAAAM,QAAA,CAAAgC,WAAA;QACA,KAAAd,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAnB,QAAA,CAAAiC,QAAA,SAAAhC,IAAA;QACA,KAAAiB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,oBAAAlB,IAAA,SAAAD,QAAA,CAAAkC,QAAA,CAAAC,IAAA,GAAAC,MAAA;QACA,KAAAlB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAR,KAAA;QACAC,GAAA,KAAAC,MAAA,MAAAN,QAAA,CAAAC,GAAA;QACAM,MAAA;QACAf,IAAA,OAAAC;MACA,GAAAe,IAAA,WAAAsB,KAAA;QAAA,IAAAtC,IAAA,GAAAsC,KAAA,CAAAtC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAkB,IAAA;UACAW,MAAA,CAAAV,QAAA;YACAoB,OAAA;YACAC,IAAA;YACAC,QAAA;YACAC,OAAA,WAAAA,QAAA,GACA;UACA;QACA;UACAb,MAAA,CAAAV,QAAA,CAAAC,KAAA,CAAApB,IAAA,CAAAqB,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}