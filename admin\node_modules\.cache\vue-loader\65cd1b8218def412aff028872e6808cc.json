{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexAsideStatic.vue?vue&type=style&index=0&id=0175fa3e&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexAsideStatic.vue", "mtime": 1642386767436}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5pbmRleC1hc2lkZSB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIG92ZXJmbG93OiBoaWRkZW47CgogIC5tZW51bGlzdEltZyB7CiAgICBwYWRkaW5nOiAyNHB4IDA7CiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94OwoKICAgIC5lbC1pbWFnZSB7CiAgICAgIG1hcmdpbjogMCBhdXRvOwogICAgICB3aWR0aDogMTAwcHg7CiAgICAgIGhlaWdodDogMTAwcHg7CiAgICAgIGJvcmRlci1yYWRpdXM6IDEwMCU7CiAgICAgIGRpc3BsYXk6IGJsb2NrOwogICAgfQogIH0KCiAgLmluZGV4LWFzaWRlLWlubmVyIHsKICAgIGhlaWdodDogMTAwJTsKICAgIG1hcmdpbi1yaWdodDogLTE3cHg7CiAgICBtYXJnaW4tYm90dG9tOiAtMTdweDsKICAgIG92ZXJmbG93OiBzY3JvbGw7CiAgICBvdmVyZmxvdy14OiBoaWRkZW4gIWltcG9ydGFudDsKICAgIHBhZGRpbmctdG9wOiA2MHB4OwogICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKCiAgICAmOmZvY3VzIHsKICAgICAgb3V0bGluZTogbm9uZTsKICAgIH0KCiAgICAuZWwtbWVudSB7CiAgICAgIGJvcmRlcjogMDsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["IndexAsideStatic.vue"], "names": [], "mappings": ";AAiPA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "IndexAsideStatic.vue", "sourceRoot": "src/components/index", "sourcesContent": ["<template>\r\n  <el-aside class=\"index-aside\" height=\"100vh\" width=\"250px\">\r\n    <div class=\"index-aside-inner menulist\" style=\"height:100%\">\r\n      <div v-for=\"item in menuList\" :key=\"item.roleName\" v-if=\"role==item.roleName\" class=\"menulist-item\" style=\"height:100%;broder:0;background-color:#FFB3A7\">\r\n        <div class=\"menulistImg\" style=\"backgroundColor:#ff0000;padding:25px 0\" v-if=\"false && menulistStyle == 'vertical'\">\r\n          <el-image v-if=\"'http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg'\" src=\"http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg\" fit=\"cover\" />\r\n        </div>\r\n        <el-menu mode=\"vertical\" :unique-opened=\"true\" class=\"el-menu-demo\" style=\"height:100%;\" background-color=\"#FFB3A7\" text-color=\"#ffffff\" active-text-color=\"#EEF749\" default-active=\"0\">\r\n          <el-menu-item index=\"(0).toString()\" :style=\"menulistBorderBottom\" @click=\"menuHandler('')\"><i v-if=\"true\" class=\"el-icon-s-home\" />首页</el-menu-item>\r\n          <el-submenu :index=\"(1).toString()\" :style=\"menulistBorderBottom\">\r\n            <template slot=\"title\">\r\n              <i v-if=\"true\" class=\"el-icon-user-solid\" />\r\n              <span>个人中心</span>\r\n            </template>\r\n            <el-menu-item :index=\"(1-2).toString()\" @click=\"menuHandler('updatePassword')\">修改密码</el-menu-item>\r\n            <el-menu-item :index=\"(1-2).toString()\" @click=\"menuHandler('center')\">个人信息</el-menu-item>\r\n          </el-submenu>\r\n          <el-submenu :style=\"menulistBorderBottom\" v-for=\" (menu,index) in item.backMenu\" :key=\"menu.menu\" :index=\"(index+2).toString()\">\r\n            <template slot=\"title\">\r\n              <i v-if=\"true\" :class=\"icons[index]\" />\r\n              <span>{{ menu.menu }}</span>\r\n            </template>\r\n            <el-menu-item v-for=\" (child,sort) in menu.child\" :key=\"sort\" :index=\"((index+2)+'-'+sort).toString()\" @click=\"menuHandler(child.tableName)\">{{ child.menu }}</el-menu-item>\r\n          </el-submenu>\r\n        </el-menu>\r\n\r\n      </div>\r\n    </div>\r\n  </el-aside>\r\n</template>\r\n<script>\r\nimport menu from '@/utils/menu'\r\nexport default {\r\n  data() {\r\n    return {\r\n      menuList: [],\r\n      dynamicMenuRoutes: [],\r\n      role: '',\r\n      icons: [\r\n        'el-icon-s-cooperation',\r\n        'el-icon-s-order',\r\n        'el-icon-s-platform',\r\n        'el-icon-s-fold',\r\n        'el-icon-s-unfold',\r\n        'el-icon-s-operation',\r\n        'el-icon-s-promotion',\r\n        'el-icon-s-release',\r\n        'el-icon-s-ticket',\r\n        'el-icon-s-management',\r\n        'el-icon-s-open',\r\n        'el-icon-s-shop',\r\n        'el-icon-s-marketing',\r\n        'el-icon-s-flag',\r\n        'el-icon-s-comment',\r\n        'el-icon-s-finance',\r\n        'el-icon-s-claim',\r\n        'el-icon-s-custom',\r\n        'el-icon-s-opportunity',\r\n        'el-icon-s-data',\r\n        'el-icon-s-check',\r\n        'el-icon-s-grid',\r\n        'el-icon-menu',\r\n        'el-icon-chat-dot-square',\r\n        'el-icon-message',\r\n        'el-icon-postcard',\r\n        'el-icon-position',\r\n        'el-icon-microphone',\r\n        'el-icon-close-notification',\r\n        'el-icon-bangzhu',\r\n        'el-icon-time',\r\n        'el-icon-odometer',\r\n        'el-icon-crop',\r\n        'el-icon-aim',\r\n        'el-icon-switch-button',\r\n        'el-icon-full-screen',\r\n        'el-icon-copy-document',\r\n        'el-icon-mic',\r\n        'el-icon-stopwatch',\r\n      ],\r\n      menulistStyle: 'vertical',\r\n\t  menulistBorderBottom: {},\r\n    }\r\n  },\r\n  mounted() {\r\n    const menus = menu.list()\r\n    this.menuList = menus\r\n    this.role = this.$storage.get('role')\r\n  },\r\n  created(){\r\n    setTimeout(()=>{\r\n      this.menulistStyleChange()\r\n    },10)\r\n    this.icons.sort(()=>{\r\n      return (0.5-Math.random())\r\n    })\r\n\tthis.lineBorder()\r\n  },\r\n  methods: {\r\n\tlineBorder() {\r\n\t\tlet style = 'vertical'\r\n\t\tlet w = '2px'\r\n\t\tlet s = 'solid'\r\n\t\tlet c = '#D6CFCF'\r\n\t\tif(style == 'vertical') {\r\n\t\t\tthis.menulistBorderBottom = {\r\n\t\t\t\tborderBottomWidth: w,\r\n\t\t\t\tborderBottomStyle: s,\r\n\t\t\t\tborderBottomColor: c\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tthis.menulistBorderBottom = {\r\n\t\t\t\tborderRightWidth: w,\r\n\t\t\t\tborderRightStyle: s,\r\n\t\t\t\tborderRightColor: c\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n    menuHandler(name) {\r\n      let router = this.$router\r\n      name = '/'+name\r\n      router.push(name).catch(err => err)\r\n    },\r\n    // 菜单\r\n    setMenulistHoverColor(){\r\n      let that = this\r\n      this.$nextTick(()=>{\r\n        document.querySelectorAll('.menulist .el-menu-item').forEach(el=>{\r\n          el.addEventListener(\"mouseenter\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"rgba(103, 228, 224, 1)\"\r\n          })\r\n          el.addEventListener(\"mouseleave\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"#FFB3A7\"\r\n          })\r\n          el.addEventListener(\"focus\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"rgba(103, 228, 224, 1)\"\r\n          })\r\n        })\r\n        document.querySelectorAll('.menulist .el-submenu__title').forEach(el=>{\r\n          el.addEventListener(\"mouseenter\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"rgba(103, 228, 224, 1)\"\r\n          })\r\n          el.addEventListener(\"mouseleave\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"#FFB3A7\"\r\n          })\r\n        })\r\n      })\r\n    },\r\n    setMenulistIconColor() {\r\n      this.$nextTick(()=>{\r\n        document.querySelectorAll('.menulist .el-submenu__title .el-submenu__icon-arrow').forEach(el=>{\r\n          el.style.color = \"rgba(247, 11, 11, 1)\"\r\n        })\r\n      })\r\n    },\r\n    menulistStyleChange() {\r\n      this.setMenulistIconColor()\r\n      this.setMenulistHoverColor()\r\n      this.setMenulistStyleHeightChange()\r\n      let str = \"vertical\"\r\n      if(\"horizontal\" === str) {\r\n        this.$nextTick(()=>{\r\n          document.querySelectorAll('.el-container .el-container').forEach(el=>{\r\n            el.style.display = \"block\"\r\n            el.style.paddingTop = \"60px\" // header 高度\r\n          })\r\n          document.querySelectorAll('.el-aside').forEach(el=>{\r\n            el.style.width = \"100%\"\r\n            el.style.height = \"60px\"\r\n            el.style.paddingTop = '0'\r\n          })\r\n          document.querySelectorAll('.index-aside .index-aside-inner').forEach(el=>{\r\n            el.style.paddingTop = '0'\r\n          })\r\n        })\r\n      }\r\n      if(\"vertical\" === str) {\r\n        this.$nextTick(()=>{\r\n          document.querySelectorAll('.index-aside .index-aside-inner').forEach(el=>{\r\n            el.style.paddingTop = \"60px\"\r\n          })\r\n        })\r\n      }\r\n    },\r\n    setMenulistStyleHeightChange() {\r\n      this.$nextTick(()=>{\r\n        document.querySelectorAll('.menulist-item>.el-menu--horizontal>.el-menu-item').forEach(el=>{\r\n          el.style.height = \"60px\"\r\n          el.style.lineHeight = \"60px\"\r\n        })\r\n        document.querySelectorAll('.menulist-item>.el-menu--horizontal>.el-submenu>.el-submenu__title').forEach(el=>{\r\n          el.style.height = \"60px\"\r\n          el.style.lineHeight = \"60px\"\r\n        })\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n  .index-aside {\r\n    position: relative;\r\n    overflow: hidden;\r\n\r\n    .menulistImg {\r\n      padding: 24px 0;\r\n      box-sizing: border-box;\r\n\r\n      .el-image {\r\n        margin: 0 auto;\r\n        width: 100px;\r\n        height: 100px;\r\n        border-radius: 100%;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .index-aside-inner {\r\n      height: 100%;\r\n      margin-right: -17px;\r\n      margin-bottom: -17px;\r\n      overflow: scroll;\r\n      overflow-x: hidden !important;\r\n      padding-top: 60px;\r\n      box-sizing: border-box;\r\n\r\n      &:focus {\r\n        outline: none;\r\n      }\r\n\r\n      .el-menu {\r\n        border: 0;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"]}]}