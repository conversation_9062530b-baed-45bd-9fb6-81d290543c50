{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\login.vue", "mtime": 1750584072910}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["menu", "data", "rulesForm", "username", "password", "role", "code", "menus", "tableName", "codes", "num", "color", "rotate", "size", "mounted", "list", "created", "setInputColor", "getRandCode", "methods", "$nextTick", "document", "querySelectorAll", "for<PERSON>ach", "el", "style", "backgroundColor", "height", "lineHeight", "borderRadius", "setTimeout", "register", "$storage", "set", "$router", "push", "path", "login", "_this", "i", "$message", "error", "toLowerCase", "length", "<PERSON><PERSON><PERSON>", "$http", "url", "concat", "method", "then", "_ref", "token", "replace", "msg", "len", "arguments", "undefined", "randomString", "chars", "colors", "sizes", "output", "key", "Math", "floor", "random", "j", "plus"], "sources": ["src/views/login.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"container loginIn\" style=\"backgroundImage: url(/tiyuguan/img/back-img-bg.jpg)\">\r\n\r\n            <div :class=\"2 == 1 ? 'left' : 2 == 2 ? 'left center' : 'left right'\" style=\"backgroundColor: rgba(244, 169, 188, 0.12)\">\r\n                <el-form class=\"login-form\" label-position=\"left\" :label-width=\"2 == 3 ? '56px' : '0px'\">\r\n                    <div class=\"title-container\"><h3 class=\"title\" style=\"color: rgba(85, 85, 76, 0.82)\">体育馆使用预约平�?/h3></div>\r\n                    <el-form-item :label=\"2 == 3 ? '用户�? : ''\" :class=\"'style'+2\">\r\n                        <span v-if=\"2 != 3\" class=\"svg-container\" style=\"color:rgba(59, 160, 215, 1);line-height:44px\"><svg-icon icon-class=\"user\" /></span>\r\n                        <el-input placeholder=\"请输入用户名\" name=\"username\" type=\"text\" v-model=\"rulesForm.username\" />\r\n                    </el-form-item>\r\n                    <el-form-item :label=\"2 == 3 ? '密码' : ''\" :class=\"'style'+2\">\r\n                        <span v-if=\"2 != 3\" class=\"svg-container\" style=\"color:rgba(59, 160, 215, 1);line-height:44px\"><svg-icon icon-class=\"password\" /></span>\r\n                        <el-input placeholder=\"请输入密码\" name=\"password\" type=\"password\" v-model=\"rulesForm.password\" />\r\n                    </el-form-item>\r\n                    <el-form-item v-if=\"0 == '1'\" class=\"code\" :label=\"2 == 3 ? '验证码' : ''\" :class=\"'style'+2\">\r\n                        <span v-if=\"2 != 3\" class=\"svg-container\" style=\"color:rgba(59, 160, 215, 1);line-height:44px\"><svg-icon icon-class=\"code\" /></span>\r\n                        <el-input placeholder=\"请输入验证码\" name=\"code\" type=\"text\" v-model=\"rulesForm.code\" />\r\n                        <div class=\"getCodeBt\" @click=\"getRandCode(4)\" style=\"height:44px;line-height:44px\">\r\n                            <span v-for=\"(item, index) in codes\" :key=\"index\" :style=\"{color:item.color,transform:item.rotate,fontSize:item.size}\">{{ item.num }}</span>\r\n                        </div>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"角色\" prop=\"loginInRole\" class=\"role\">\r\n                        <el-radio\r\n                                v-for=\"item in menus\"\r\n                                v-if=\"item.hasBackLogin=='�?\"\r\n                                v-bind:key=\"item.roleName\"\r\n                                v-model=\"rulesForm.role\"\r\n                                :label=\"item.roleName\"\r\n                        >{{item.roleName}}</el-radio>\r\n                    </el-form-item>\r\n                    <el-button type=\"primary\" @click=\"login()\" class=\"loginInBt\" style=\"padding:0;font-size:16px;border-radius:30px;height:44px;line-height:44px;width:100%;backgroundColor:rgba(64, 158, 255, 1); borderColor:rgba(64, 158, 255, 1); color:rgba(255, 255, 255, 1)\">{{'1' == '1' ? '登录' : 'login'}}</el-button>\r\n                    <el-form-item class=\"setting\">\r\n                                                                                                                        \t\t\t\t<div style=\"color:rgba(25, 169, 123, 1)\" class=\"register\" @click=\"register('yonghu')\">用户注册</div>\r\n                                                                                                                                                                                                                                                                                                                                            <!-- <div style=\"color:rgba(14, 14, 14, 0.95)\" class=\"reset\">修改密码</div> -->\r\n                    </el-form-item>\r\n                </el-form>\r\n            </div>\r\n\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    import menu from \"@/utils/menu\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                rulesForm: {\r\n                    username: \"\",\r\n                    password: \"\",\r\n                    role: \"\",\r\n                    code: '',\r\n                },\r\n                menus: [],\r\n                tableName: \"\",\r\n                codes: [{\r\n                    num: 1,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 2,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 3,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 4,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                }],\r\n            };\r\n        },\r\n        mounted() {\r\n            let menus = menu.list();\r\n            this.menus = menus;\r\n        },\r\n        created() {\r\n            this.setInputColor()\r\n            this.getRandCode()\r\n        },\r\n        methods: {\r\n            setInputColor(){\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.loginIn .el-input__inner').forEach(el=>{\r\n                        el.style.backgroundColor = \"rgba(255, 255, 255, 1)\"\r\n                        el.style.color = \"rgba(51, 51, 51, 1)\"\r\n                        el.style.height = \"44px\"\r\n                        el.style.lineHeight = \"44px\"\r\n                        el.style.borderRadius = \"30px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .style3 .el-form-item__label').forEach(el=>{\r\n                        el.style.height = \"44px\"\r\n                        el.style.lineHeight = \"44px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .el-form-item__label').forEach(el=>{\r\n                        el.style.color = \"rgba(59, 160, 215, 1)\"\r\n                    })\r\n                    setTimeout(()=>{\r\n                        document.querySelectorAll('.loginIn .role .el-radio__label').forEach(el=>{\r\n                            el.style.color = \"#fff\"\r\n                        })\r\n                    },350)\r\n                })\r\n\r\n            },\r\n            register(tableName){\r\n                this.$storage.set(\"loginTable\", tableName);\r\n                this.$router.push({path:'/register'})\r\n            },\r\n            // 登陆\r\n            login() {\r\n                let code = ''\r\n                for(let i in this.codes) {\r\n                    code += this.codes[i].num\r\n                }\r\n                if ('0' == '1' && !this.rulesForm.code) {\r\n                    this.$message.error(\"请输入验证码\");\r\n                    return;\r\n                }\r\n                if ('0' == '1' && this.rulesForm.code.toLowerCase() != code.toLowerCase()) {\r\n                    this.$message.error(\"验证码输入有误\");\r\n                    this.getRandCode()\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.username) {\r\n                    this.$message.error(\"请输入用户名\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.password) {\r\n                    this.$message.error(\"请输入密码\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.role) {\r\n                    this.$message.error(\"请选择角色\");\r\n                    return;\r\n                }\r\n                let menus = this.menus;\r\n                for (let i = 0; i < menus.length; i++) {\r\n                    if (menus[i].roleName == this.rulesForm.role) {\r\n                        this.tableName = menus[i].tableName;\r\n                    }\r\n                }\r\n                this.$http({\r\n                    url: `${this.tableName}/login?username=${this.rulesForm.username}&password=${this.rulesForm.password}`,\r\n                    method: \"post\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.$storage.set(\"Token\", data.token);\r\n                        this.$storage.set(\"role\", this.rulesForm.role);\r\n                        this.$storage.set(\"sessionTable\", this.tableName);\r\n                        this.$storage.set(\"adminName\", this.rulesForm.username);\r\n                        this.$router.replace({ path: \"/index/\" });\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            getRandCode(len = 4){\r\n                this.randomString(len)\r\n            },\r\n            randomString(len = 4) {\r\n                let chars = [\r\n                    \"a\", \"b\", \"c\", \"d\", \"e\", \"f\", \"g\", \"h\", \"i\", \"j\", \"k\",\r\n                    \"l\", \"m\", \"n\", \"o\", \"p\", \"q\", \"r\", \"s\", \"t\", \"u\", \"v\",\r\n                    \"w\", \"x\", \"y\", \"z\", \"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\",\r\n                    \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\",\r\n                    \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\", \"0\", \"1\", \"2\",\r\n                    \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"\r\n                ]\r\n                let colors = [\"0\", \"1\", \"2\",\"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"a\", \"b\", \"c\", \"d\", \"e\", \"f\"]\r\n                let sizes = ['14', '15', '16', '17', '18']\r\n\r\n                let output = [];\r\n                for (let i = 0; i < len; i++) {\r\n                    // 随机验证码\r\n                    let key = Math.floor(Math.random()*chars.length)\r\n                    this.codes[i].num = chars[key]\r\n                    // 随机验证码颜�?\r\n                    let code = '#'\r\n                    for (let j = 0; j < 6; j++) {\r\n                        let key = Math.floor(Math.random()*colors.length)\r\n                        code += colors[key]\r\n                    }\r\n                    this.codes[i].color = code\r\n                    // 随机验证码方�?\r\n                    let rotate = Math.floor(Math.random()*60)\r\n                    let plus = Math.floor(Math.random()*2)\r\n                    if(plus == 1) rotate = '-'+rotate\r\n                    this.codes[i].rotate = 'rotate('+rotate+'deg)'\r\n                    // 随机验证码字体大�?\r\n                    let size = Math.floor(Math.random()*sizes.length)\r\n                    this.codes[i].size = sizes[size]+'px'\r\n                }\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n    .loginIn {\r\n        min-height: 100vh;\r\n        position: relative;\r\n        background-repeat: no-repeat;\r\n        background-position: center center;\r\n        background-size: cover;\r\n\r\n    .left {\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        width: 360px;\r\n        height: 100%;\r\n\r\n    .login-form {\r\n        background-color: transparent;\r\n        width: 100%;\r\n        right: inherit;\r\n        padding: 0 12px;\r\n        box-sizing: border-box;\r\n        display: flex;\r\n        justify-content: center;\r\n        flex-direction: column;\r\n    }\r\n\r\n    .title-container {\r\n        text-align: center;\r\n        font-size: 24px;\r\n\r\n    .title {\r\n        margin: 20px 0;\r\n    }\r\n    }\r\n\r\n    .el-form-item {\r\n        position: relative;\r\n\r\n    .svg-container {\r\n        padding: 6px 5px 6px 15px;\r\n        color: #889aa4;\r\n        vertical-align: middle;\r\n        display: inline-block;\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        z-index: 1;\r\n        padding: 0;\r\n        line-height: 40px;\r\n        width: 30px;\r\n        text-align: center;\r\n    }\r\n\r\n    .el-input {\r\n        display: inline-block;\r\n        height: 40px;\r\n        width: 100%;\r\n\r\n    & ::v-deep input {\r\n          background: transparent;\r\n          border: 0px;\r\n          -webkit-appearance: none;\r\n          padding: 0 15px 0 30px;\r\n          color: #fff;\r\n          height: 40px;\r\n      }\r\n    }\r\n\r\n    }\r\n\r\n\r\n    }\r\n\r\n    .center {\r\n        position: absolute;\r\n        left: 50%;\r\n        top: 50%;\r\n        width: 360px;\r\n        transform: translate3d(-50%,-50%,0);\r\n        height: 446px;\r\n        border-radius: 8px;\r\n    }\r\n\r\n    .right {\r\n        position: absolute;\r\n        left: inherit;\r\n        right: 0;\r\n        top: 0;\r\n        width: 360px;\r\n        height: 100%;\r\n    }\r\n\r\n    .code {\r\n    .el-form-item__content {\r\n        position: relative;\r\n\r\n    .getCodeBt {\r\n        position: absolute;\r\n        right: 0;\r\n        top: 0;\r\n        line-height: 40px;\r\n        width: 100px;\r\n        background-color: rgba(51,51,51,0.4);\r\n        color: #fff;\r\n        text-align: center;\r\n        border-radius: 0 4px 4px 0;\r\n        height: 40px;\r\n        overflow: hidden;\r\n\r\n    span {\r\n        padding: 0 5px;\r\n        display: inline-block;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n    }\r\n    }\r\n\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 130px 0 30px;\r\n      }\r\n    }\r\n    }\r\n    }\r\n\r\n    .setting {\r\n    & ::v-deep .el-form-item__content {\r\n          padding: 0 15px;\r\n          box-sizing: border-box;\r\n          line-height: 32px;\r\n          height: 32px;\r\n          font-size: 14px;\r\n          color: #999;\r\n          margin: 0 !important;\r\n\r\n    .register {\r\n        float: left;\r\n        width: 50%;\r\n    }\r\n\r\n    .reset {\r\n        float: right;\r\n        width: 50%;\r\n        text-align: right;\r\n    }\r\n    }\r\n    }\r\n\r\n    .style2 {\r\n        padding-left: 30px;\r\n\r\n    .svg-container {\r\n        left: -30px !important;\r\n    }\r\n\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 15px !important;\r\n      }\r\n    }\r\n    }\r\n\r\n    .code.style2, .code.style3 {\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 115px 0 15px;\r\n      }\r\n    }\r\n    }\r\n\r\n    .style3 {\r\n    & ::v-deep .el-form-item__label {\r\n          padding-right: 6px;\r\n      }\r\n\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 15px !important;\r\n      }\r\n    }\r\n    }\r\n\r\n    .role {\r\n    & ::v-deep .el-form-item__label {\r\n          width: 56px !important;\r\n      }\r\n\r\n    & ::v-deep .el-radio {\r\n          margin-right: 12px;\r\n      }\r\n    }\r\n\r\n    }\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;AA2CA,OAAAA,IAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,KAAA;MACAC,SAAA;MACAC,KAAA;QACAC,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;MACA;QACAH,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;MACA;QACAH,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;MACA;QACAH,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAP,KAAA,GAAAP,IAAA,CAAAe,IAAA;IACA,KAAAR,KAAA,GAAAA,KAAA;EACA;EACAS,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAF,aAAA,WAAAA,cAAA;MACA,KAAAG,SAAA;QACAC,QAAA,CAAAC,gBAAA,8BAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAC,eAAA;UACAF,EAAA,CAAAC,KAAA,CAAAd,KAAA;UACAa,EAAA,CAAAC,KAAA,CAAAE,MAAA;UACAH,EAAA,CAAAC,KAAA,CAAAG,UAAA;UACAJ,EAAA,CAAAC,KAAA,CAAAI,YAAA;QACA;QACAR,QAAA,CAAAC,gBAAA,0CAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAE,MAAA;UACAH,EAAA,CAAAC,KAAA,CAAAG,UAAA;QACA;QACAP,QAAA,CAAAC,gBAAA,kCAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAd,KAAA;QACA;QACAmB,UAAA;UACAT,QAAA,CAAAC,gBAAA,oCAAAC,OAAA,WAAAC,EAAA;YACAA,EAAA,CAAAC,KAAA,CAAAd,KAAA;UACA;QACA;MACA;IAEA;IACAoB,QAAA,WAAAA,SAAAvB,SAAA;MACA,KAAAwB,QAAA,CAAAC,GAAA,eAAAzB,SAAA;MACA,KAAA0B,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACA;IACAC,KAAA,WAAAA,MAAA;MAAA,IAAAC,KAAA;MACA,IAAAhC,IAAA;MACA,SAAAiC,CAAA,SAAA9B,KAAA;QACAH,IAAA,SAAAG,KAAA,CAAA8B,CAAA,EAAA7B,GAAA;MACA;MACA,wBAAAR,SAAA,CAAAI,IAAA;QACA,KAAAkC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,uBAAAvC,SAAA,CAAAI,IAAA,CAAAoC,WAAA,MAAApC,IAAA,CAAAoC,WAAA;QACA,KAAAF,QAAA,CAAAC,KAAA;QACA,KAAAvB,WAAA;QACA;MACA;MACA,UAAAhB,SAAA,CAAAC,QAAA;QACA,KAAAqC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAvC,SAAA,CAAAE,QAAA;QACA,KAAAoC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAvC,SAAA,CAAAG,IAAA;QACA,KAAAmC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,IAAAlC,KAAA,QAAAA,KAAA;MACA,SAAAgC,EAAA,MAAAA,EAAA,GAAAhC,KAAA,CAAAoC,MAAA,EAAAJ,EAAA;QACA,IAAAhC,KAAA,CAAAgC,EAAA,EAAAK,QAAA,SAAA1C,SAAA,CAAAG,IAAA;UACA,KAAAG,SAAA,GAAAD,KAAA,CAAAgC,EAAA,EAAA/B,SAAA;QACA;MACA;MACA,KAAAqC,KAAA;QACAC,GAAA,KAAAC,MAAA,MAAAvC,SAAA,sBAAAuC,MAAA,MAAA7C,SAAA,CAAAC,QAAA,gBAAA4C,MAAA,MAAA7C,SAAA,CAAAE,QAAA;QACA4C,MAAA;MACA,GAAAC,IAAA,WAAAC,IAAA;QAAA,IAAAjD,IAAA,GAAAiD,IAAA,CAAAjD,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAK,IAAA;UACAgC,KAAA,CAAAN,QAAA,CAAAC,GAAA,UAAAhC,IAAA,CAAAkD,KAAA;UACAb,KAAA,CAAAN,QAAA,CAAAC,GAAA,SAAAK,KAAA,CAAApC,SAAA,CAAAG,IAAA;UACAiC,KAAA,CAAAN,QAAA,CAAAC,GAAA,iBAAAK,KAAA,CAAA9B,SAAA;UACA8B,KAAA,CAAAN,QAAA,CAAAC,GAAA,cAAAK,KAAA,CAAApC,SAAA,CAAAC,QAAA;UACAmC,KAAA,CAAAJ,OAAA,CAAAkB,OAAA;YAAAhB,IAAA;UAAA;QACA;UACAE,KAAA,CAAAE,QAAA,CAAAC,KAAA,CAAAxC,IAAA,CAAAoD,GAAA;QACA;MACA;IACA;IACAnC,WAAA,WAAAA,YAAA;MAAA,IAAAoC,GAAA,GAAAC,SAAA,CAAAZ,MAAA,QAAAY,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,KAAAE,YAAA,CAAAH,GAAA;IACA;IACAG,YAAA,WAAAA,aAAA;MAAA,IAAAH,GAAA,GAAAC,SAAA,CAAAZ,MAAA,QAAAY,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAG,KAAA,IACA,uDACA,uDACA,uDACA,uDACA,uDACA,kCACA;MACA,IAAAC,MAAA;MACA,IAAAC,KAAA;MAEA,IAAAC,MAAA;MACA,SAAAtB,CAAA,MAAAA,CAAA,GAAAe,GAAA,EAAAf,CAAA;QACA;QACA,IAAAuB,GAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAP,KAAA,CAAAf,MAAA;QACA,KAAAlC,KAAA,CAAA8B,CAAA,EAAA7B,GAAA,GAAAgD,KAAA,CAAAI,GAAA;QACA;QACA,IAAAxD,IAAA;QACA,SAAA4D,CAAA,MAAAA,CAAA,MAAAA,CAAA;UACA,IAAAJ,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAN,MAAA,CAAAhB,MAAA;UACArC,IAAA,IAAAqD,MAAA,CAAAG,IAAA;QACA;QACA,KAAArD,KAAA,CAAA8B,CAAA,EAAA5B,KAAA,GAAAL,IAAA;QACA;QACA,IAAAM,MAAA,GAAAmD,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;QACA,IAAAE,IAAA,GAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;QACA,IAAAE,IAAA,OAAAvD,MAAA,SAAAA,MAAA;QACA,KAAAH,KAAA,CAAA8B,CAAA,EAAA3B,MAAA,eAAAA,MAAA;QACA;QACA,IAAAC,IAAA,GAAAkD,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAL,KAAA,CAAAjB,MAAA;QACA,KAAAlC,KAAA,CAAA8B,CAAA,EAAA1B,IAAA,GAAA+C,KAAA,CAAA/C,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}