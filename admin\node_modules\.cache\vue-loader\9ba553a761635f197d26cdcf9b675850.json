{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexMain.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexMain.vue", "mtime": 1642386767437}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoJaW1wb3J0IG1lbnUgZnJvbSAiQC91dGlscy9tZW51IjsNCglleHBvcnQgZGVmYXVsdCB7DQoJCWRhdGEoKSB7DQoJCQlyZXR1cm4gew0KCQkJCW1lbnVMaXN0OiBbXSwNCgkJCQlyb2xlOiAiIiwNCgkJCQljdXJyZW50SW5kZXg6IC0yLA0KCQkJCWl0ZW1NZW51OiBbXSwNCgkJCQl0aXRsZTogJycNCgkJCX07DQoJCX0sDQoJCW1vdW50ZWQoKSB7DQoJCQlsZXQgbWVudXMgPSBtZW51Lmxpc3QoKTsNCgkJCXRoaXMubWVudUxpc3QgPSBtZW51czsNCgkJCXRoaXMucm9sZSA9IHRoaXMuJHN0b3JhZ2UuZ2V0KCJyb2xlIik7DQoJCX0sDQoJCW1ldGhvZHM6IHsNCgkJCW1lbnVIYW5kbGVyKG1lbnUpIHsNCgkJCQl0aGlzLiRyb3V0ZXIucHVzaCh7DQoJCQkJCW5hbWU6IG1lbnUudGFibGVOYW1lDQoJCQkJfSk7DQoJCQkJdGhpcy50aXRsZSA9IG1lbnUubWVudTsNCgkJCX0sDQoJCQl0aXRsZUNoYW5nZShpbmRleCwgbWVudXMpIHsNCgkJCQl0aGlzLmN1cnJlbnRJbmRleCA9IGluZGV4DQoJCQkJdGhpcy5pdGVtTWVudSA9IG1lbnVzOw0KCQkJCWNvbnNvbGUubG9nKG1lbnVzKTsNCgkJCX0sDQoJCQlob21lQ2hhbmdlKGluZGV4KSB7DQoJCQkJdGhpcy5pdGVtTWVudSA9IFtdOw0KCQkJCXRoaXMudGl0bGUgPSAiIg0KCQkJCXRoaXMuY3VycmVudEluZGV4ID0gaW5kZXgNCgkJCQl0aGlzLiRyb3V0ZXIucHVzaCh7DQoJCQkJCW5hbWU6ICdob21lJw0KCQkJCX0pOw0KCQkJfSwNCgkJCWNlbnRlckNoYW5nZShpbmRleCkgew0KCQkJCXRoaXMuaXRlbU1lbnUgPSBbew0KCQkJCQkiYnV0dG9ucyI6IFsi5paw5aKeIiwgIuafpeeciyIsICLkv67mlLkiLCAi5Yig6ZmkIl0sDQoJCQkJCSJtZW51IjogIuS/ruaUueWvhueggSIsDQoJCQkJCSJ0YWJsZU5hbWUiOiAidXBkYXRlUGFzc3dvcmQiDQoJCQkJfSwgew0KCQkJCQkiYnV0dG9ucyI6IFsi5paw5aKeIiwgIuafpeeciyIsICLkv67mlLkiLCAi5Yig6ZmkIl0sDQoJCQkJCSJtZW51IjogIuS4quS6uuS/oeaBryIsDQoJCQkJCSJ0YWJsZU5hbWUiOiAiY2VudGVyIg0KCQkJCX1dOw0KCQkJCXRoaXMudGl0bGUgPSAiIg0KCQkJCXRoaXMuY3VycmVudEluZGV4ID0gaW5kZXgNCgkJCQl0aGlzLiRyb3V0ZXIucHVzaCh7DQoJCQkJCW5hbWU6ICdob21lJw0KCQkJCX0pOw0KCQkJfQ0KCQl9DQoJfTsNCg=="}, {"version": 3, "sources": ["IndexMain.vue"], "names": [], "mappings": ";AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IndexMain.vue", "sourceRoot": "src/components/index", "sourcesContent": ["<template>\r\n\t<el-main>\r\n\t\t<bread-crumbs :title=\"title\" class=\"bread-crumbs\"></bread-crumbs>\r\n\t\t<router-view class=\"router-view\"></router-view>\r\n\t</el-main>\r\n</template>\r\n<script>\r\n\timport menu from \"@/utils/menu\";\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmenuList: [],\r\n\t\t\t\trole: \"\",\r\n\t\t\t\tcurrentIndex: -2,\r\n\t\t\t\titemMenu: [],\r\n\t\t\t\ttitle: ''\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tlet menus = menu.list();\r\n\t\t\tthis.menuList = menus;\r\n\t\t\tthis.role = this.$storage.get(\"role\");\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tmenuHandler(menu) {\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tname: menu.tableName\r\n\t\t\t\t});\r\n\t\t\t\tthis.title = menu.menu;\r\n\t\t\t},\r\n\t\t\ttitleChange(index, menus) {\r\n\t\t\t\tthis.currentIndex = index\r\n\t\t\t\tthis.itemMenu = menus;\r\n\t\t\t\tconsole.log(menus);\r\n\t\t\t},\r\n\t\t\thomeChange(index) {\r\n\t\t\t\tthis.itemMenu = [];\r\n\t\t\t\tthis.title = \"\"\r\n\t\t\t\tthis.currentIndex = index\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tname: 'home'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcenterChange(index) {\r\n\t\t\t\tthis.itemMenu = [{\r\n\t\t\t\t\t\"buttons\": [\"新增\", \"查看\", \"修改\", \"删除\"],\r\n\t\t\t\t\t\"menu\": \"修改密码\",\r\n\t\t\t\t\t\"tableName\": \"updatePassword\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\t\"buttons\": [\"新增\", \"查看\", \"修改\", \"删除\"],\r\n\t\t\t\t\t\"menu\": \"个人信息\",\r\n\t\t\t\t\t\"tableName\": \"center\"\r\n\t\t\t\t}];\r\n\t\t\t\tthis.title = \"\"\r\n\t\t\t\tthis.currentIndex = index\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tname: 'home'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\ta {\r\n\t\ttext-decoration: none;\r\n\t\tcolor: #555;\r\n\t}\r\n\r\n\ta:hover {\r\n\t\tbackground: #00c292;\r\n\t}\r\n\r\n\t.nav-list {\r\n\t\twidth: 100%;\r\n\t\tmargin: 0 auto;\r\n\t\ttext-align: left;\r\n\t\tmargin-top: 20px;\r\n\r\n\t\t.nav-title {\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tfont-size: 15px;\r\n\t\t\tcolor: #333;\r\n\t\t\tpadding: 15px 25px;\r\n\t\t\tborder: none;\r\n\t\t}\r\n\r\n\t\t.nav-title.active {\r\n\t\t\tcolor: #555;\r\n\t\t\tcursor: default;\r\n\t\t\tbackground-color: #fff;\r\n\t\t}\r\n\t}\r\n\r\n\t.nav-item {\r\n\t\tmargin-top: 20px;\r\n\t\tbackground: #FFFFFF;\r\n\t\tpadding: 15px 0;\r\n\r\n\t\t.menu {\r\n\t\t\tpadding: 15px 25px;\r\n\t\t}\r\n\t}\r\n\r\n\t.el-main {\r\n\t\tbackground-color: #F6F8FA;\r\n\t\tpadding: 0 24px;\r\n\t\t// padding-top: 60px;\r\n\t}\r\n\r\n\t.router-view {\r\n\t\tpadding: 10px;\r\n\t\tmargin-top: 10px;\r\n\t\tbackground: #FFFFFF;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.bread-crumbs {\r\n\t\twidth: 100%;\r\n\t\t// border-bottom: 1px solid #e9eef3;\r\n\t\t// border-top: 1px solid #e9eef3;\r\n\t\tmargin-top: 10px;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n</style>\r\n"]}]}