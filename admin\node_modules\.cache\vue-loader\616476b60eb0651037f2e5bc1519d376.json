{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdiOrder\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdiOrder\\list.vue", "mtime": 1750584101353}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";AAg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file": "list.vue", "sourceRoot": "src/views/modules/changdiOrder", "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"main-content\">\r\n\r\n        <!-- 条件查询 -->\r\n        <div v-if=\"showFlag\">\r\n            <el-form :inline=\"true\" :model=\"searchForm\" class=\"form-content\">\r\n                <el-row :gutter=\"20\" class=\"slt\" :style=\"{justifyContent:contents.searchBoxPosition=='1'?'flex-start':contents.searchBoxPosition=='2'?'center':'flex-end'}\">\r\n                                                                                \r\n                                                             \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '场地名称' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.changdiName\" placeholder=\"场地名称\" clearable></el-input>\r\n                    </el-form-item>\r\n                                         \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '场地类型' : ''\">\r\n                        <el-select v-model=\"searchForm.changdiTypes\" placeholder=\"请选择场地类型\">\r\n                            <el-option label=\"=-请选择-=\" value=\"\"></el-option>\r\n                            <el-option\r\n                                    v-for=\"(item,index) in changdiTypesSelectSearch\"\r\n                                    v-bind:key=\"index\"\r\n                                    :label=\"item.indexName\"\r\n                                    :value=\"item.codeIndex\">\r\n                                <!--lable是要展示的名�?->\r\n                                <!--value是�?->\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                                                                                                                                                                                                                                                                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户姓名' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuName\" placeholder=\"用户姓名\" clearable></el-input>\r\n                    </el-form-item>\r\n                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户手机�? : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuPhone\" placeholder=\"用户手机�? clearable></el-input>\r\n                    </el-form-item>\r\n                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户身份证号' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuIdNumber\" placeholder=\"用户身份证号\" clearable></el-input>\r\n                    </el-form-item>\r\n                                                                                \r\n\r\n                    <el-form-item>\r\n                        <el-button type=\"success\" @click=\"search()\">查询<i class=\"el-icon-search el-icon--right\"/></el-button>\r\n                    </el-form-item>\r\n                </el-row>\r\n                <el-row class=\"ad\" :style=\"{justifyContent:contents.btnAdAllBoxPosition=='1'?'flex-start':contents.btnAdAllBoxPosition=='2'?'center':'flex-end'}\">\r\n                    <el-form-item>\r\n                        <el-button\r\n                                v-if=\"isAuth('changdiOrder','新增')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-plus\"\r\n                                @click=\"addOrUpdateHandler()\"\r\n                        >新增</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('changdiOrder','删除')\"\r\n                                :disabled=\"dataListSelections.length <= 0\"\r\n                                type=\"danger\"\r\n                                icon=\"el-icon-delete\"\r\n                                @click=\"deleteHandler()\"\r\n                        >删除</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('changdiOrder','报表')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-pie-chart\"\r\n                                @click=\"chartDialog()\"\r\n                        >报表</el-button>\r\n                        &nbsp;\r\n                        <a style=\"text-decoration:none\" class=\"el-button el-button--success\"\r\n                           v-if=\"isAuth('changdiOrder','导入导出')\"\r\n                           icon=\"el-icon-download\"\r\n                           href=\"http://localhost:8080/tiyuguan/upload/changdiOrderMuBan.xls\"\r\n                        >批量导入场地预约数据模板</a>\r\n                        &nbsp;\r\n                        <el-upload\r\n                                v-if=\"isAuth('changdiOrder','导入导出')\"\r\n                                style=\"display: inline-block\"\r\n                                action=\"tiyuguan/file/upload\"\r\n                                :on-success=\"changdiOrderUploadSuccess\"\r\n                                :on-error=\"changdiOrderUploadError\"\r\n                                :show-file-list = false>\r\n                            <el-button\r\n                                    v-if=\"isAuth('changdiOrder','导入导出')\"\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-upload2\"\r\n                            >批量导入场地预约数据</el-button>\r\n                        </el-upload>\r\n                        &nbsp;\r\n                        <!-- 导出excel -->\r\n                        <download-excel v-if=\"isAuth('changdiOrder','导入导出')\" style=\"display: inline-block\" class = \"export-excel-wrapper\" :data = \"dataList\" :fields = \"json_fields\" name = \"changdiOrder.xls\">\r\n                            <!-- 导出excel -->\r\n                            <el-button\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-download\"\r\n                            >导出</el-button>\r\n                        </download-excel>\r\n                        &nbsp;\r\n                    </el-form-item>\r\n                </el-row>\r\n            </el-form>\r\n            <div class=\"table-content\">\r\n                <el-table class=\"tables\" :size=\"contents.tableSize\" :show-header=\"contents.tableShowHeader\"\r\n                          :header-row-style=\"headerRowStyle\" :header-cell-style=\"headerCellStyle\"\r\n                          :border=\"contents.tableBorder\"\r\n                          :fit=\"contents.tableFit\"\r\n                          :stripe=\"contents.tableStripe\"\r\n                          :row-style=\"rowStyle\"\r\n                          :cell-style=\"cellStyle\"\r\n                          :style=\"{width: '100%',fontSize:contents.tableContentFontSize,color:contents.tableContentFontColor}\"\r\n                          v-if=\"isAuth('changdiOrder','查看')\"\r\n                          :data=\"dataList\"\r\n                          v-loading=\"dataListLoading\"\r\n                          @selection-change=\"selectionChangeHandler\">\r\n                    <el-table-column  v-if=\"contents.tableSelection\"\r\n                                      type=\"selection\"\r\n                                      header-align=\"center\"\r\n                                      align=\"center\"\r\n                                      width=\"50\">\r\n                    </el-table-column>\r\n                    <el-table-column label=\"索引\" v-if=\"contents.tableIndex\" type=\"index\" width=\"50\" />\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiUuidNumber\"\r\n                                      header-align=\"center\"\r\n                                      label=\"场地编号\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiUuidNumber}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiName\"\r\n                                      header-align=\"center\"\r\n                                      label=\"场地名称\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\" prop=\"changdiPhoto\"\r\n                               header-align=\"center\"\r\n                               width=\"200\"\r\n                               label=\"场地照片\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div v-if=\"scope.row.changdiPhoto\">\r\n                                <img :src=\"scope.row.changdiPhoto\" width=\"100\" height=\"100\">\r\n                            </div>\r\n                            <div v-else>无图�?/div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuName\"\r\n                                      header-align=\"center\"\r\n                                      label=\"用户姓名\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuPhone\"\r\n                                      header-align=\"center\"\r\n                                      label=\"用户手机�?>\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuPhone}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"changdiOrderUuidNumber\"\r\n                                   header-align=\"center\"\r\n                                   label=\"订单�?>\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiOrderUuidNumber}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"changdiOrderTruePrice\"\r\n                                   header-align=\"center\"\r\n                                   label=\"实付价格\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiOrderTruePrice}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiOrderTypes\"\r\n                                      header-align=\"center\"\r\n                                      label=\"订单类型\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiOrderValue}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"shijianduan\"\r\n                                   header-align=\"center\"\r\n                                   label=\"预约时间�?>\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.shijianduan}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"buyTime\"\r\n                                   header-align=\"center\"\r\n                                   label=\"预约日期\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.buyTime}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"insertTime\"\r\n                                   header-align=\"center\"\r\n                                   label=\"订单创建时间\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.insertTime}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column width=\"300\" :align=\"contents.tableAlign\"\r\n                                     header-align=\"center\"\r\n                                     label=\"操作\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button v-if=\"isAuth('changdiOrder','查看')\" type=\"success\" icon=\"el-icon-tickets\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id,'info')\">详情</el-button>\r\n                            <el-button v-if=\"isAuth('changdiOrder','修改')\" type=\"primary\" icon=\"el-icon-edit\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id)\">修改</el-button>\r\n                            <el-button v-if=\"isAuth('changdiOrder','删除')\" type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteHandler(scope.row.id)\">删除</el-button>\r\n                            <el-button v-if=\"isAuth('changdiOrder','订单') && scope.row.changdiOrderTypes == 1 && role == '用户'\" type=\"primary\" icon=\"el-icon-sold-out\" size=\"mini\" @click=\"refund(scope.row.id)\">取消预约</el-button>\r\n\r\n\r\n\r\n\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n                <el-pagination\r\n                        clsss=\"pages\"\r\n                        :layout=\"layouts\"\r\n                        @size-change=\"sizeChangeHandle\"\r\n                        @current-change=\"currentChangeHandle\"\r\n                        :current-page=\"pageIndex\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"Number(contents.pageEachNum)\"\r\n                        :total=\"totalPage\"\r\n                        :small=\"contents.pageStyle\"\r\n                        class=\"pagination-content\"\r\n                        :background=\"contents.pageBtnBG\"\r\n                        :style=\"{textAlign:contents.pagePosition==1?'left':contents.pagePosition==2?'center':'right'}\"\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <!-- 添加/修改页面  将父组件的search方法传递给子组�?->\r\n        <add-or-update v-if=\"addOrUpdateFlag\" :parent=\"this\" ref=\"addOrUpdate\"></add-or-update>\r\n\r\n        <!-- 评价 -->\r\n\t\t<el-dialog\r\n                title=\"评价\"\r\n                :visible.sync=\"commentbackVisible\"\r\n                width=\"30%\">\r\n            <span>评价内容</span>\r\n            <el-input type=\"textarea\" v-model=\"commentbackContent\"></el-input>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t<el-button @click=\"commentbackVisible = false\">�?�?/el-button>\r\n\t\t\t<el-button type=\"primary\" @click=\"commentback()\">�?�?/el-button>\r\n\t\t  </span>\r\n        </el-dialog>\r\n\r\n        <el-dialog title=\"统计报表\" :visible.sync=\"chartVisiable\" width=\"800\">\r\n            <el-date-picker\r\n                    v-model=\"echartsDate\"\r\n                    type=\"year\"\r\n                    placeholder=\"选择�?>\r\n            </el-date-picker>\r\n            <el-button @click=\"chartDialog()\">查询</el-button>\r\n            <div id=\"statistic\" style=\"width:100%;height:600px;\"></div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"chartVisiable = false\">返回</el-button>\r\n\t\t\t</span>\r\n        </el-dialog>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import AddOrUpdate from \"./add-or-update\";\r\n    import styleJs from \"../../../utils/style.js\";\r\n    export default {\r\n        data() {\r\n        return {\r\n            searchForm: {\r\n                key: \"\"\r\n            },\r\n            sessionTable : \"\",//登录账户所在表�?\r\n            role : \"\",//权限\r\n    //级联表下拉框搜索条件\r\n              changdiTypesSelectSearch : [],\r\n    //当前表下拉框搜索条件\r\n            form:{\r\n                id : null,\r\n                changdiOrderUuidNumber : null,\r\n                changdiId : null,\r\n                yonghuId : null,\r\n                changdiOrderTruePrice : null,\r\n                changdiOrderTypes : null,\r\n                shijianduan : null,\r\n                buyTime : null,\r\n                insertTime : null,\r\n                createTime : null,\r\n            },\r\n            dataList: [],\r\n            pageIndex: 1,\r\n            pageSize: 10,\r\n            totalPage: 0,\r\n            dataListLoading: false,\r\n            dataListSelections: [],\r\n            showFlag: true,\r\n            sfshVisiable: false,\r\n            shForm: {},\r\n            chartVisiable: false,\r\n            addOrUpdateFlag:false,\r\n            contents:null,\r\n            layouts: '',\r\n\r\n            echartsDate: new Date(),//echarts的时间查询字�?\r\n\r\n            //导出excel\r\n            json_fields: {\r\n                //级联表字�?\r\n                     '场地编号': 'changdiUuidNumber',\r\n                     '场地名称': 'changdiName',\r\n                     '场地照片': 'changdiPhoto',\r\n                     '场地类型': 'changdiTypes',\r\n                     '场地原价': 'changdiOldMoney',\r\n                     '场地现价': 'changdiNewMoney',\r\n                     '时间段': 'shijianduan',\r\n                     '人数': 'shijianduanRen',\r\n                     '点击次数': 'changdiClicknum',\r\n                     '半全场': 'banquanTypes',\r\n                     '是否上架': 'shangxiaTypes',\r\n                     '推荐吃饭地点': 'tuijian',\r\n                     '用户姓名': 'yonghuName',\r\n                     '用户手机�?: 'yonghuPhone',\r\n                     '用户身份证号': 'yonghuIdNumber',\r\n                     '用户头像': 'yonghuPhoto',\r\n                     '电子邮箱': 'yonghuEmail',\r\n                     '余额': 'newMoney',\r\n                //本表字段\r\n                     '订单�?: \"changdiOrderUuidNumber\",\r\n                     '实付价格': \"changdiOrderTruePrice\",\r\n                     '订单类型': \"changdiOrderTypes\",\r\n                     '预约时间�?: \"shijianduan\",\r\n                     '预约日期': \"buyTime\",\r\n                     '订单创建时间': \"insertTime\",\r\n            },\r\n\r\n        //评价\r\n\t\t\t//操作数据id\r\n\t\t\tcommentbackId:null,\r\n\t\t\t//评价内容\r\n\t\t\tcommentbackContent:null,\r\n\t\t\t//模态框状�?\r\n\t\t\tcommentbackVisible:false,            };\r\n        },\r\n        created() {\r\n            this.contents = styleJs.listStyle();\r\n            this.init();\r\n            this.getDataList();\r\n            this.contentStyleChange()\r\n        },\r\n        mounted() {\r\n            //获取当前登录用户的信�?\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n        },\r\n        filters: {\r\n            htmlfilter: function (val) {\r\n                return val.replace(/<[^>]*>/g).replace(/undefined/g,'');\r\n            }\r\n        },\r\n        components: {\r\n            AddOrUpdate,\r\n        },\r\n        computed: {\r\n        },\r\n        methods: {\r\n            chartDialog() {\r\n                let _this = this;\r\n                let params = {\r\n                    dateFormat :\"%Y\"\r\n                    ,riqi :_this.echartsDate.getFullYear()\r\n                    ,thisTable : {//当前�?\r\n                        tableName :\"shangdian_shouyin\"//当前表表�?\r\n                        ,sumColum : 'shangdian_shouyin_true_price' //求和字段\r\n                        ,date : 'insert_time'//分组日期字段\r\n                        // ,string : 'name,leixing'//分组字符串字�?\r\n                        // ,types : 'shangdian_shouyin_types'//分组下拉框字�?\r\n                    }\r\n                    // ,joinTable : {//级联表（可以不存在）\r\n                    //     tableName :\"yuangong\"//级联表表�?\r\n                    //     // ,date : 'insert_time'//分组日期字段\r\n                    //     ,string : 'yuangong_name'//分组字符串字�?\r\n                    //     // ,types : 'insertTime'//分组下拉框字�?\r\n                    // }\r\n                }\r\n                _this.chartVisiable = true;\r\n                _this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"barSum\",\r\n                        method: \"get\",\r\n                        params: params\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n\r\n\r\n                            //柱状�?求和 已成功使�?\r\n                            //start\r\n                            let series = [];//具体数据�?\r\n                            data.data.yAxis.forEach(function (item,index) {\r\n                                let tempMap = {};\r\n                                tempMap.name=data.data.legend[index];\r\n                                tempMap.type='bar';\r\n                                tempMap.data=item;\r\n                                series.push(tempMap);\r\n\r\n                            })\r\n\r\n                            var option = {\r\n                                tooltip: {\r\n                                    trigger: 'axis',\r\n                                    axisPointer: {\r\n                                        type: 'cross',\r\n                                        crossStyle: {\r\n                                            color: '#999'\r\n                                        }\r\n                                    }\r\n                                },\r\n                                toolbox: {\r\n                                    feature: {\r\n                                        // dataView: { show: true, readOnly: false },  // 数据查看\r\n                                        magicType: { show: true, type: ['line', 'bar'] },//切换图形展示方式\r\n                                        // restore: { show: true }, // 刷新\r\n                                        saveAsImage: { show: true }//保存\r\n                                    }\r\n                                },\r\n                                legend: {\r\n                                    data: data.data.legend//标题  可以点击导致某一列数据消�?\r\n                                },\r\n                                xAxis: [\r\n                                    {\r\n                                        type: 'category',\r\n                                        name: '月份',\r\n                                        data: data.data.xAxis,\r\n                                        axisPointer: {\r\n                                            type: 'shadow'\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                yAxis: [\r\n                                    {\r\n                                        type: 'value',//不能�?\r\n                                        name: '�?,//y轴单�?\r\n                                        axisLabel: {\r\n                                            formatter: '{value} �? // 后缀\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                series:series//具体数据\r\n                            };\r\n                            // 使用刚指定的配置项和数据显示图表�?\r\n                            statistic.setOption(option);\r\n                            //根据窗口的大小变动图�?\r\n                            window.onresize = function () {\r\n                                statistic.resize();\r\n                            };\r\n                            //end\r\n\r\n\r\n\r\n                            //饼状�?原先自带�?未修改过\r\n                            //start\r\n                            /*let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表�?\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图�?\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }*/\r\n\r\n                            //end\r\n                        }else {\r\n                            this.$message({\r\n                                message: \"报表未查询到数据\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n                this.chartVisiable = !this.chartVisiable;\r\n                this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"group/xinzitongji/xinzi\",\r\n                        method: \"get\",\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表�?\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图�?\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }\r\n                        }\r\n                    });\r\n                // xcolumn ycolumn\r\n                });\r\n            },\r\n            contentStyleChange() {\r\n                this.contentSearchStyleChange()\r\n                this.contentBtnAdAllStyleChange()\r\n                this.contentSearchBtnStyleChange()\r\n                this.contentTableBtnStyleChange()\r\n                this.contentPageStyleChange()\r\n            },\r\n            contentSearchStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-input__inner').forEach(el => {\r\n                        let textAlign = 'left'\r\n                        if(this.contents.inputFontPosition == 2)\r\n                            textAlign = 'center'\r\n                            if (this.contents.inputFontPosition == 3) textAlign = 'right'\r\n                                el.style.textAlign = textAlign\r\n                            el.style.height = this.contents.inputHeight\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                            el.style.color = this.contents.inputFontColor\r\n                            el.style.fontSize = this.contents.inputFontSize\r\n                            el.style.borderWidth = this.contents.inputBorderWidth\r\n                            el.style.borderStyle = this.contents.inputBorderStyle\r\n                            el.style.borderColor = this.contents.inputBorderColor\r\n                            el.style.borderRadius = this.contents.inputBorderRadius\r\n                            el.style.backgroundColor = this.contents.inputBgColor\r\n                    })\r\n                    if (this.contents.inputTitle) {\r\n                        document.querySelectorAll('.form-content .slt .el-form-item__label').forEach(el => {\r\n                            el.style.color = this.contents.inputTitleColor\r\n                            el.style.fontSize = this.contents.inputTitleSize\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }\r\n                    setTimeout(() => {\r\n                        document.querySelectorAll('.form-content .slt .el-input__prefix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__suffix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__icon').forEach(el => {\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }, 10 )\r\n                })\r\n            },\r\n            // 搜索按钮\r\n            contentSearchBtnStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.searchBtnHeight\r\n                        el.style.color = this.contents.searchBtnFontColor\r\n                        el.style.fontSize = this.contents.searchBtnFontSize\r\n                        el.style.borderWidth = this.contents.searchBtnBorderWidth\r\n                        el.style.borderStyle = this.contents.searchBtnBorderStyle\r\n                        el.style.borderColor = this.contents.searchBtnBorderColor\r\n                        el.style.borderRadius = this.contents.searchBtnBorderRadius\r\n                        el.style.backgroundColor = this.contents.searchBtnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 新增、批量删�?\r\n            contentBtnAdAllStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .ad .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllAddFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllAddBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--danger').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllDelFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllDelBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--warning').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllWarnFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllWarnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 表格\r\n            rowStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {color: this.contents.tableStripeFontColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            cellStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {backgroundColor: this.contents.tableStripeBgColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            headerRowStyle({row, rowIndex}) {\r\n                return {color: this.contents.tableHeaderFontColor}\r\n            },\r\n            headerCellStyle({row, rowIndex}) {\r\n                return {backgroundColor: this.contents.tableHeaderBgColor}\r\n            },\r\n            // 表格按钮\r\n            contentTableBtnStyleChange() {\r\n                // this.$nextTick(()=>{\r\n                //   setTimeout(()=>{\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--success').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDetailFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDetailBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--primary').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnEditFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnEditBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--danger').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDelFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDelBgColor\r\n                //     })\r\n\r\n                //   }, 50)\r\n                // })\r\n            },\r\n            // 分页\r\n            contentPageStyleChange() {\r\n                let arr = []\r\n                if (this.contents.pageTotal) arr.push('total')\r\n                if (this.contents.pageSizes) arr.push('sizes')\r\n                if (this.contents.pagePrevNext) {\r\n                    arr.push('prev')\r\n                    if (this.contents.pagePager) arr.push('pager')\r\n                    arr.push('next')\r\n                }\r\n                if (this.contents.pageJumper) arr.push('jumper')\r\n                this.layouts = arr.join()\r\n                this.contents.pageEachNum = 10\r\n            },\r\n\r\n            init() {\r\n            },\r\n            search() {\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 获取数据列表\r\n            getDataList() {\r\n                this.dataListLoading = true;\r\n                let params = {\r\n                    page: this.pageIndex,\r\n                    limit: this.pageSize,\r\n                    sort: 'id',\r\n                }\r\n\r\n                                                             \r\n                if (this.searchForm.changdiName!= '' && this.searchForm.changdiName!= undefined) {\r\n                    params['changdiName'] = '%' + this.searchForm.changdiName + '%'\r\n                }\r\n                                         \r\n                if (this.searchForm.changdiTypes!= '' && this.searchForm.changdiTypes!= undefined) {\r\n                    params['changdiTypes'] = this.searchForm.changdiTypes\r\n                }\r\n                                                                                                                                                                                                                                                                     \r\n                if (this.searchForm.yonghuName!= '' && this.searchForm.yonghuName!= undefined) {\r\n                    params['yonghuName'] = '%' + this.searchForm.yonghuName + '%'\r\n                }\r\n                     \r\n                if (this.searchForm.yonghuPhone!= '' && this.searchForm.yonghuPhone!= undefined) {\r\n                    params['yonghuPhone'] = '%' + this.searchForm.yonghuPhone + '%'\r\n                }\r\n                     \r\n                if (this.searchForm.yonghuIdNumber!= '' && this.searchForm.yonghuIdNumber!= undefined) {\r\n                    params['yonghuIdNumber'] = '%' + this.searchForm.yonghuIdNumber + '%'\r\n                }\r\n                                                                                                                                                                \r\n                params['changdiOrderDelete'] = 1// 逻辑删除字段 1 未删�?2 删除\r\n\r\n\r\n                this.$http({\r\n                    url: \"changdiOrder/page\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.dataList = data.data.list;\r\n                        this.totalPage = data.data.total;\r\n                    }else{\r\n                        this.dataList = [];\r\n                        this.totalPage = 0;\r\n                    }\r\n                    this.dataListLoading = false;\r\n                });\r\n\r\n                //查询级联表搜索条件所有列�?\r\n                this.$http({\r\n                    url: \"dictionary/page?dicCode=changdi_types&page=1&limit=100\",\r\n                    method: \"get\",\r\n                    page: 1,\r\n                    limit: 100,\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.changdiTypesSelectSearch = data.data.list;\r\n                    }\r\n                });\r\n                //查询当前表搜索条件所有列�?\r\n            },\r\n            //每页�?\r\n            sizeChangeHandle(val) {\r\n                this.pageSize = val;\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 当前�?\r\n            currentChangeHandle(val) {\r\n                this.pageIndex = val;\r\n                this.getDataList();\r\n            },\r\n            // 多�?\r\n            selectionChangeHandler(val) {\r\n                this.dataListSelections = val;\r\n            },\r\n            // 添加/修改\r\n            addOrUpdateHandler(id, type) {\r\n                this.showFlag = false;\r\n                this.addOrUpdateFlag = true;\r\n                this.crossAddOrUpdateFlag = false;\r\n                if (type != 'info') {\r\n                    type = 'else';\r\n                }\r\n                this.$nextTick(() => {\r\n                    this.$refs.addOrUpdate.init(id, type);\r\n                });\r\n            },\r\n            // 下载\r\n            download(file) {\r\n                window.open(\" ${file} \")\r\n            },\r\n            // 删除\r\n            deleteHandler(id) {\r\n                var ids = id ? [Number(id)] : this.dataListSelections.map(item => {\r\n                    return Number(item.id);\r\n                });\r\n\r\n                this.$confirm(`确定进行[${id ? \"删除\" : \"批量删除\"}]操作?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: \"changdiOrder/delete\",\r\n                        method: \"post\",\r\n                        data: ids\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            this.$message({\r\n                                message: \"操作成功\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }else{\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n            // 导入功能上传文件成功后调用导入方�?\r\n            changdiOrderUploadSuccess(data){\r\n                let _this = this;\r\n                _this.$http({\r\n                    url: \"changdiOrder/batchInsert?fileName=\" + data.file,\r\n                    method: \"get\"\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        _this.$message({\r\n                            message: \"导入场地预约数据成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.search();\r\n                            }\r\n                        });\r\n                    }else{\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n\r\n            },\r\n            // 导入功能上传文件失败后调用导入方�?\r\n            changdiOrderUploadError(data){\r\n                this.$message.error('上传失败');\r\n            },\r\n\r\n            //取消预约\r\n            refund(id){\r\n                this.$confirm(`确定要取消预约吗?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: \"changdiOrder/refund?id=\"+id,\r\n                        method: \"post\",\r\n                    }).then(({ data }) => {\r\n                        if (data && data.code === 0) {\r\n                            this.$message({\r\n                                message:\"取消成功\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {this.search();}\r\n                            });\r\n                        } else {\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.slt {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .ad {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .pages {\r\n    & ::v-deep el-pagination__sizes{\r\n      & ::v-deep el-input__inner {\r\n        height: 22px;\r\n        line-height: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .el-button+.el-button {\r\n    margin:0;\r\n  }\r\n\r\n  .tables {\r\n\t& ::v-deep .el-button--success {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(121, 244, 164, 0.54);\r\n\t}\r\n\r\n\t& ::v-deep .el-button--primary {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(169, 115, 232, 0.65);\r\n\t}\r\n\r\n\t& ::v-deep .el-button--danger {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(241, 120, 186, 1);\r\n\t}\r\n\r\n    & ::v-deep .el-button {\r\n      margin: 4px;\r\n    }\r\n  }\r\n</style>\r\n\r\n\r\n\r\n"]}]}