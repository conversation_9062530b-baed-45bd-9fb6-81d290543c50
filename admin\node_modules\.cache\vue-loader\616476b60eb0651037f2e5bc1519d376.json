{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdiOrder\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdiOrder\\list.vue", "mtime": 1750585694090}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";AAg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file": "list.vue", "sourceRoot": "src/views/modules/changdiOrder", "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"main-content\">\r\n\r\n        <!-- 条件查询 -->\r\n        <div v-if=\"showFlag\">\r\n            <el-form :inline=\"true\" :model=\"searchForm\" class=\"form-content\">\r\n                <el-row :gutter=\"20\" class=\"slt\" :style=\"{justifyContent:contents.searchBoxPosition=='1'?'flex-start':contents.searchBoxPosition=='2'?'center':'flex-end'}\">\r\n                                                                                \r\n                                                             \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '场地名称' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.changdiName\" placeholder=\"场地名称\" clearable></el-input>\r\n                    </el-form-item>\r\n                                         \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '场地类型' : ''\">\r\n                        <el-select v-model=\"searchForm.changdiTypes\" placeholder=\"请选择场地类型\">\r\n                            <el-option label=\"=-请选择-=\" value=\"\"></el-option>\r\n                            <el-option\r\n                                    v-for=\"(item,index) in changdiTypesSelectSearch\"\r\n                                    v-bind:key=\"index\"\r\n                                    :label=\"item.indexName\"\r\n                                    :value=\"item.codeIndex\">\r\n                                <!--lable是要展示的名�?->\r\n                                <!--value是�?->\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                                                                                                                                                                                                                                                                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户姓名' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuName\" placeholder=\"用户姓名\" clearable></el-input>\r\n                    </el-form-item>\r\n                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户手机�? : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuPhone\" placeholder=\"用户手机�? clearable></el-input>\r\n                    </el-form-item>\r\n                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户身份证号' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuIdNumber\" placeholder=\"用户身份证号\" clearable></el-input>\r\n                    </el-form-item>\r\n                                                                                \r\n\r\n                    <el-form-item>\r\n                        <el-button type=\"success\" @click=\"search()\">查询<i class=\"el-icon-search el-icon--right\"/></el-button>\r\n                    </el-form-item>\r\n                </el-row>\r\n                <el-row class=\"ad\" :style=\"{justifyContent:contents.btnAdAllBoxPosition=='1'?'flex-start':contents.btnAdAllBoxPosition=='2'?'center':'flex-end'}\">\r\n                    <el-form-item>\r\n                        <el-button\r\n                                v-if=\"isAuth('changdiOrder','新增')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-plus\"\r\n                                @click=\"addOrUpdateHandler()\"\r\n                        >新增</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('changdiOrder','删除')\"\r\n                                :disabled=\"dataListSelections.length <= 0\"\r\n                                type=\"danger\"\r\n                                icon=\"el-icon-delete\"\r\n                                @click=\"deleteHandler()\"\r\n                        >删除</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('changdiOrder','报表')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-pie-chart\"\r\n                                @click=\"chartDialog()\"\r\n                        >报表</el-button>\r\n                        &nbsp;\r\n                        <a style=\"text-decoration:none\" class=\"el-button el-button--success\"\r\n                           v-if=\"isAuth('changdiOrder','导入导出')\"\r\n                           icon=\"el-icon-download\"\r\n                           href=\"http://localhost:8080/tiyuguan/upload/changdiOrderMuBan.xls\"\r\n                        >批量导入场地预约数据模板</a>\r\n                        &nbsp;\r\n                        <el-upload\r\n                                v-if=\"isAuth('changdiOrder','导入导出')\"\r\n                                style=\"display: inline-block\"\r\n                                action=\"tiyuguan/file/upload\"\r\n                                :on-success=\"changdiOrderUploadSuccess\"\r\n                                :on-error=\"changdiOrderUploadError\"\r\n                                :show-file-list = false>\r\n                            <el-button\r\n                                    v-if=\"isAuth('changdiOrder','导入导出')\"\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-upload2\"\r\n                            >批量导入场地预约数据</el-button>\r\n                        </el-upload>\r\n                        &nbsp;\r\n                        <!-- 导出excel -->\r\n                        <download-excel v-if=\"isAuth('changdiOrder','导入导出')\" style=\"display: inline-block\" class = \"export-excel-wrapper\" :data = \"dataList\" :fields = \"json_fields\" name = \"changdiOrder.xls\">\r\n                            <!-- 导出excel -->\r\n                            <el-button\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-download\"\r\n                            >导出</el-button>\r\n                        </download-excel>\r\n                        &nbsp;\r\n                    </el-form-item>\r\n                </el-row>\r\n            </el-form>\r\n            <div class=\"table-content\">\r\n                <el-table class=\"tables\" :size=\"contents.tableSize\" :show-header=\"contents.tableShowHeader\"\r\n                          :header-row-style=\"headerRowStyle\" :header-cell-style=\"headerCellStyle\"\r\n                          :border=\"contents.tableBorder\"\r\n                          :fit=\"contents.tableFit\"\r\n                          :stripe=\"contents.tableStripe\"\r\n                          :row-style=\"rowStyle\"\r\n                          :cell-style=\"cellStyle\"\r\n                          :style=\"{width: '100%',fontSize:contents.tableContentFontSize,color:contents.tableContentFontColor}\"\r\n                          v-if=\"isAuth('changdiOrder','查看')\"\r\n                          :data=\"dataList\"\r\n                          v-loading=\"dataListLoading\"\r\n                          @selection-change=\"selectionChangeHandler\">\r\n                    <el-table-column  v-if=\"contents.tableSelection\"\r\n                                      type=\"selection\"\r\n                                      header-align=\"center\"\r\n                                      align=\"center\"\r\n                                      width=\"50\">\r\n                    </el-table-column>\r\n                    <el-table-column label=\"索引\" v-if=\"contents.tableIndex\" type=\"index\" width=\"50\" />\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiUuidNumber\"\r\n                                      header-align=\"center\"\r\n                                      label=\"场地编号\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiUuidNumber}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiName\"\r\n                                      header-align=\"center\"\r\n                                      label=\"场地名称\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\" prop=\"changdiPhoto\"\r\n                               header-align=\"center\"\r\n                               width=\"200\"\r\n                               label=\"场地照片\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div v-if=\"scope.row.changdiPhoto\">\r\n                                <img :src=\"scope.row.changdiPhoto\" width=\"100\" height=\"100\">\r\n                            </div>\r\n                            <div v-else>无图�?/div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuName\"\r\n                                      header-align=\"center\"\r\n                                      label=\"用户姓名\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuPhone\"\r\n                                      header-align=\"center\"\r\n                                      label=\"用户手机�?>\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuPhone}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"changdiOrderUuidNumber\"\r\n                                   header-align=\"center\"\r\n                                   label=\"订单�?>\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiOrderUuidNumber}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"changdiOrderTruePrice\"\r\n                                   header-align=\"center\"\r\n                                   label=\"实付价格\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiOrderTruePrice}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"changdiOrderTypes\"\r\n                                      header-align=\"center\"\r\n                                      label=\"订单类型\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.changdiOrderValue}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"shijianduan\"\r\n                                   header-align=\"center\"\r\n                                   label=\"预约时间�?>\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.shijianduan}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"buyTime\"\r\n                                   header-align=\"center\"\r\n                                   label=\"预约日期\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.buyTime}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"insertTime\"\r\n                                   header-align=\"center\"\r\n                                   label=\"订单创建时间\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.insertTime}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column width=\"300\" :align=\"contents.tableAlign\"\r\n                                     header-align=\"center\"\r\n                                     label=\"操作\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button v-if=\"isAuth('changdiOrder','查看')\" type=\"success\" icon=\"el-icon-tickets\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id,'info')\">详情</el-button>\r\n                            <el-button v-if=\"isAuth('changdiOrder','修改')\" type=\"primary\" icon=\"el-icon-edit\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id)\">修改</el-button>\r\n                            <el-button v-if=\"isAuth('changdiOrder','删除')\" type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteHandler(scope.row.id)\">删除</el-button>\r\n                            <el-button v-if=\"isAuth('changdiOrder','订单') && scope.row.changdiOrderTypes == 1 && role == '用户'\" type=\"primary\" icon=\"el-icon-sold-out\" size=\"mini\" @click=\"refund(scope.row.id)\">取消预约</el-button>\r\n\r\n\r\n\r\n\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n                <el-pagination\r\n                        clsss=\"pages\"\r\n                        :layout=\"layouts\"\r\n                        @size-change=\"sizeChangeHandle\"\r\n                        @current-change=\"currentChangeHandle\"\r\n                        :current-page=\"pageIndex\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"Number(contents.pageEachNum)\"\r\n                        :total=\"totalPage\"\r\n                        :small=\"contents.pageStyle\"\r\n                        class=\"pagination-content\"\r\n                        :background=\"contents.pageBtnBG\"\r\n                        :style=\"{textAlign:contents.pagePosition==1?'left':contents.pagePosition==2?'center':'right'}\"\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <!-- 添加/修改页面  将父组件的search方法传递给子组�?->\r\n        <add-or-update v-if=\"addOrUpdateFlag\" :parent=\"this\" ref=\"addOrUpdate\"></add-or-update>\r\n\r\n        <!-- 评价 -->\r\n\t\t<el-dialog\r\n                title=\"评价\"\r\n                :visible.sync=\"commentbackVisible\"\r\n                width=\"30%\">\r\n            <span>评价内容</span>\r\n            <el-input type=\"textarea\" v-model=\"commentbackContent\"></el-input>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t<el-button @click=\"commentbackVisible = false\">�?�?/el-button>\r\n\t\t\t<el-button type=\"primary\" @click=\"commentback()\">�?�?/el-button>\r\n\t\t  </span>\r\n        </el-dialog>\r\n\r\n        <el-dialog title=\"统计报表\" :visible.sync=\"chartVisiable\" width=\"800\">\r\n            <el-date-picker\r\n                    v-model=\"echartsDate\"\r\n                    type=\"year\"\r\n                    placeholder=\"选择�?>\r\n            </el-date-picker>\r\n            <el-button @click=\"chartDialog()\">查询</el-button>\r\n            <div id=\"statistic\" style=\"width:100%;height:600px;\"></div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"chartVisiable = false\">返回</el-button>\r\n\t\t\t</span>\r\n        </el-dialog>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import AddOrUpdate from \"./add-or-update\";\r\n    import styleJs from \"../../../utils/style.js\";\r\n    export default {\r\n        data() {\r\n        return {\r\n            searchForm: {\r\n                key: \"\"\r\n            },\r\n            sessionTable : \"\",//登录账户所在表�?\r\n            role : \"\",//权限\r\n    //级联表下拉框搜索条件\r\n              changdiTypesSelectSearch : [],\r\n    //当前表下拉框搜索条件\r\n            form:{\r\n                id : null,\r\n                changdiOrderUuidNumber : null,\r\n                changdiId : null,\r\n                yonghuId : null,\r\n                changdiOrderTruePrice : null,\r\n                changdiOrderTypes : null,\r\n                shijianduan : null,\r\n                buyTime : null,\r\n                insertTime : null,\r\n                createTime : null,\r\n            },\r\n            dataList: [],\r\n            pageIndex: 1,\r\n            pageSize: 10,\r\n            totalPage: 0,\r\n            dataListLoading: false,\r\n            dataListSelections: [],\r\n            showFlag: true,\r\n            sfshVisiable: false,\r\n            shForm: {},\r\n            chartVisiable: false,\r\n            addOrUpdateFlag:false,\r\n            contents:null,\r\n            layouts: '',\r\n\r\n            echartsDate: new Date(),//echarts的时间查询字�?\r\n\r\n            //导出excel\r\n            json_fields: {\r\n                //级联表字�?\r\n                     '场地编号': 'changdiUuidNumber',\r\n                     '场地名称': 'changdiName',\r\n                     '场地照片': 'changdiPhoto',\r\n                     '场地类型': 'changdiTypes',\r\n                     '场地原价': 'changdiOldMoney',\r\n                     '场地现价': 'changdiNewMoney',\r\n                     '时间段': 'shijianduan',\r\n                     '人数': 'shijianduanRen',\r\n                     '点击次数': 'changdiClicknum',\r\n                     '半全场': 'banquanTypes',\r\n                     '是否上架': 'shangxiaTypes',\r\n                     '推荐吃饭地点': 'tuijian',\r\n                     '用户姓名': 'yonghuName',\r\n                     '用户手机号': 'yonghuPhone',\r\n                     '用户身份证号': 'yonghuIdNumber',\r\n                     '用户头像': 'yonghuPhoto',\r\n                     '电子邮箱': 'yonghuEmail',\r\n                     '余额': 'newMoney',\r\n                //本表字段\r\n                     '订单号': \"changdiOrderUuidNumber\",\r\n                     '实付价格': \"changdiOrderTruePrice\",\r\n                     '订单类型': \"changdiOrderTypes\",\r\n                     '预约时间段': \"shijianduan\",\r\n                     '预约日期': \"buyTime\",\r\n                     '订单创建时间': \"insertTime\",\r\n            },\r\n\r\n        //评价\r\n\t\t\t//操作数据id\r\n\t\t\tcommentbackId:null,\r\n\t\t\t//评价内容\r\n\t\t\tcommentbackContent:null,\r\n\t\t\t//模态框状�?\r\n\t\t\tcommentbackVisible:false,            };\r\n        },\r\n        created() {\r\n            this.contents = styleJs.listStyle();\r\n            this.init();\r\n            this.getDataList();\r\n            this.contentStyleChange()\r\n        },\r\n        mounted() {\r\n            //获取当前登录用户的信�?\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n        },\r\n        filters: {\r\n            htmlfilter: function (val) {\r\n                return val.replace(/<[^>]*>/g).replace(/undefined/g,'');\r\n            }\r\n        },\r\n        components: {\r\n            AddOrUpdate,\r\n        },\r\n        computed: {\r\n        },\r\n        methods: {\r\n            chartDialog() {\r\n                let _this = this;\r\n                let params = {\r\n                    dateFormat :\"%Y\"\r\n                    ,riqi :_this.echartsDate.getFullYear()\r\n                    ,thisTable : {//当前�?\r\n                        tableName :\"shangdian_shouyin\"//当前表表�?\r\n                        ,sumColum : 'shangdian_shouyin_true_price' //求和字段\r\n                        ,date : 'insert_time'//分组日期字段\r\n                        // ,string : 'name,leixing'//分组字符串字�?\r\n                        // ,types : 'shangdian_shouyin_types'//分组下拉框字�?\r\n                    }\r\n                    // ,joinTable : {//级联表（可以不存在）\r\n                    //     tableName :\"yuangong\"//级联表表�?\r\n                    //     // ,date : 'insert_time'//分组日期字段\r\n                    //     ,string : 'yuangong_name'//分组字符串字�?\r\n                    //     // ,types : 'insertTime'//分组下拉框字�?\r\n                    // }\r\n                }\r\n                _this.chartVisiable = true;\r\n                _this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"barSum\",\r\n                        method: \"get\",\r\n                        params: params\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n\r\n\r\n                            //柱状�?求和 已成功使�?\r\n                            //start\r\n                            let series = [];//具体数据�?\r\n                            data.data.yAxis.forEach(function (item,index) {\r\n                                let tempMap = {};\r\n                                tempMap.name=data.data.legend[index];\r\n                                tempMap.type='bar';\r\n                                tempMap.data=item;\r\n                                series.push(tempMap);\r\n\r\n                            })\r\n\r\n                            var option = {\r\n                                tooltip: {\r\n                                    trigger: 'axis',\r\n                                    axisPointer: {\r\n                                        type: 'cross',\r\n                                        crossStyle: {\r\n                                            color: '#999'\r\n                                        }\r\n                                    }\r\n                                },\r\n                                toolbox: {\r\n                                    feature: {\r\n                                        // dataView: { show: true, readOnly: false },  // 数据查看\r\n                                        magicType: { show: true, type: ['line', 'bar'] },//切换图形展示方式\r\n                                        // restore: { show: true }, // 刷新\r\n                                        saveAsImage: { show: true }//保存\r\n                                    }\r\n                                },\r\n                                legend: {\r\n                                    data: data.data.legend//标题  可以点击导致某一列数据消�?\r\n                                },\r\n                                xAxis: [\r\n                                    {\r\n                                        type: 'category',\r\n                                        name: '月份',\r\n                                        data: data.data.xAxis,\r\n                                        axisPointer: {\r\n                                            type: 'shadow'\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                yAxis: [\r\n                                    {\r\n                                        type: 'value',//不能�?\r\n                                        name: '元',//y轴单位\r\n                                        axisLabel: {\r\n                                            formatter: '{value} 元' // 后缀\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                series:series//具体数据\r\n                            };\r\n                            // 使用刚指定的配置项和数据显示图表�?\r\n                            statistic.setOption(option);\r\n                            //根据窗口的大小变动图�?\r\n                            window.onresize = function () {\r\n                                statistic.resize();\r\n                            };\r\n                            //end\r\n\r\n\r\n\r\n                            //饼状�?原先自带�?未修改过\r\n                            //start\r\n                            /*let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表�?\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图�?\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }*/\r\n\r\n                            //end\r\n                        }else {\r\n                            this.$message({\r\n                                message: \"报表未查询到数据\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n                this.chartVisiable = !this.chartVisiable;\r\n                this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"group/xinzitongji/xinzi\",\r\n                        method: \"get\",\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            let res = data.data;\r\n                            let xAxis = [];\r\n                            let yAxis = [];\r\n                            let pArray = []\r\n                            for (let i = 0; i < res.length; i++) {\r\n                                xAxis.push(res[i].xinzi);\r\n                                yAxis.push(res[i].total);\r\n                                pArray.push({\r\n                                    value: res[i].total,\r\n                                    name: res[i].xinzi\r\n                                })\r\n                                var option = {};\r\n                                option = {\r\n                                    title: {\r\n                                        text: '统计',\r\n                                        left: 'center'\r\n                                    },\r\n                                    tooltip: {\r\n                                        trigger: 'item',\r\n                                        formatter: '{b} : {c} ({d}%)'\r\n                                    },\r\n                                    series: [{\r\n                                        type: 'pie',\r\n                                        radius: '55%',\r\n                                        center: ['50%', '60%'],\r\n                                        data: pArray,\r\n                                        emphasis: {\r\n                                            itemStyle: {\r\n                                                shadowBlur: 10,\r\n                                                shadowOffsetX: 0,\r\n                                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                                            }\r\n                                        }\r\n                                    }]\r\n                                };\r\n                                // 使用刚指定的配置项和数据显示图表�?\r\n                                statistic.setOption(option);\r\n                                //根据窗口的大小变动图�?\r\n                                window.onresize = function () {\r\n                                    statistic.resize();\r\n                                };\r\n                            }\r\n                        }\r\n                    });\r\n                // xcolumn ycolumn\r\n                });\r\n            },\r\n            contentStyleChange() {\r\n                this.contentSearchStyleChange()\r\n                this.contentBtnAdAllStyleChange()\r\n                this.contentSearchBtnStyleChange()\r\n                this.contentTableBtnStyleChange()\r\n                this.contentPageStyleChange()\r\n            },\r\n            contentSearchStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-input__inner').forEach(el => {\r\n                        let textAlign = 'left'\r\n                        if(this.contents.inputFontPosition == 2)\r\n                            textAlign = 'center'\r\n                            if (this.contents.inputFontPosition == 3) textAlign = 'right'\r\n                                el.style.textAlign = textAlign\r\n                            el.style.height = this.contents.inputHeight\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                            el.style.color = this.contents.inputFontColor\r\n                            el.style.fontSize = this.contents.inputFontSize\r\n                            el.style.borderWidth = this.contents.inputBorderWidth\r\n                            el.style.borderStyle = this.contents.inputBorderStyle\r\n                            el.style.borderColor = this.contents.inputBorderColor\r\n                            el.style.borderRadius = this.contents.inputBorderRadius\r\n                            el.style.backgroundColor = this.contents.inputBgColor\r\n                    })\r\n                    if (this.contents.inputTitle) {\r\n                        document.querySelectorAll('.form-content .slt .el-form-item__label').forEach(el => {\r\n                            el.style.color = this.contents.inputTitleColor\r\n                            el.style.fontSize = this.contents.inputTitleSize\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }\r\n                    setTimeout(() => {\r\n                        document.querySelectorAll('.form-content .slt .el-input__prefix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__suffix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__icon').forEach(el => {\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }, 10 )\r\n                })\r\n            },\r\n            // 搜索按钮\r\n            contentSearchBtnStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.searchBtnHeight\r\n                        el.style.color = this.contents.searchBtnFontColor\r\n                        el.style.fontSize = this.contents.searchBtnFontSize\r\n                        el.style.borderWidth = this.contents.searchBtnBorderWidth\r\n                        el.style.borderStyle = this.contents.searchBtnBorderStyle\r\n                        el.style.borderColor = this.contents.searchBtnBorderColor\r\n                        el.style.borderRadius = this.contents.searchBtnBorderRadius\r\n                        el.style.backgroundColor = this.contents.searchBtnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 新增、批量删�?\r\n            contentBtnAdAllStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .ad .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllAddFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllAddBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--danger').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllDelFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllDelBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--warning').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllWarnFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllWarnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 表格\r\n            rowStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {color: this.contents.tableStripeFontColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            cellStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {backgroundColor: this.contents.tableStripeBgColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            headerRowStyle({row, rowIndex}) {\r\n                return {color: this.contents.tableHeaderFontColor}\r\n            },\r\n            headerCellStyle({row, rowIndex}) {\r\n                return {backgroundColor: this.contents.tableHeaderBgColor}\r\n            },\r\n            // 表格按钮\r\n            contentTableBtnStyleChange() {\r\n                // this.$nextTick(()=>{\r\n                //   setTimeout(()=>{\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--success').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDetailFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDetailBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--primary').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnEditFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnEditBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--danger').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDelFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDelBgColor\r\n                //     })\r\n\r\n                //   }, 50)\r\n                // })\r\n            },\r\n            // 分页\r\n            contentPageStyleChange() {\r\n                let arr = []\r\n                if (this.contents.pageTotal) arr.push('total')\r\n                if (this.contents.pageSizes) arr.push('sizes')\r\n                if (this.contents.pagePrevNext) {\r\n                    arr.push('prev')\r\n                    if (this.contents.pagePager) arr.push('pager')\r\n                    arr.push('next')\r\n                }\r\n                if (this.contents.pageJumper) arr.push('jumper')\r\n                this.layouts = arr.join()\r\n                this.contents.pageEachNum = 10\r\n            },\r\n\r\n            init() {\r\n            },\r\n            search() {\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 获取数据列表\r\n            getDataList() {\r\n                this.dataListLoading = true;\r\n                let params = {\r\n                    page: this.pageIndex,\r\n                    limit: this.pageSize,\r\n                    sort: 'id',\r\n                }\r\n\r\n                                                             \r\n                if (this.searchForm.changdiName!= '' && this.searchForm.changdiName!= undefined) {\r\n                    params['changdiName'] = '%' + this.searchForm.changdiName + '%'\r\n                }\r\n                                         \r\n                if (this.searchForm.changdiTypes!= '' && this.searchForm.changdiTypes!= undefined) {\r\n                    params['changdiTypes'] = this.searchForm.changdiTypes\r\n                }\r\n                                                                                                                                                                                                                                                                     \r\n                if (this.searchForm.yonghuName!= '' && this.searchForm.yonghuName!= undefined) {\r\n                    params['yonghuName'] = '%' + this.searchForm.yonghuName + '%'\r\n                }\r\n                     \r\n                if (this.searchForm.yonghuPhone!= '' && this.searchForm.yonghuPhone!= undefined) {\r\n                    params['yonghuPhone'] = '%' + this.searchForm.yonghuPhone + '%'\r\n                }\r\n                     \r\n                if (this.searchForm.yonghuIdNumber!= '' && this.searchForm.yonghuIdNumber!= undefined) {\r\n                    params['yonghuIdNumber'] = '%' + this.searchForm.yonghuIdNumber + '%'\r\n                }\r\n                                                                                                                                                                \r\n                params['changdiOrderDelete'] = 1// 逻辑删除字段 1 未删�?2 删除\r\n\r\n\r\n                this.$http({\r\n                    url: \"changdiOrder/page\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.dataList = data.data.list;\r\n                        this.totalPage = data.data.total;\r\n                    }else{\r\n                        this.dataList = [];\r\n                        this.totalPage = 0;\r\n                    }\r\n                    this.dataListLoading = false;\r\n                });\r\n\r\n                //查询级联表搜索条件所有列�?\r\n                this.$http({\r\n                    url: \"dictionary/page?dicCode=changdi_types&page=1&limit=100\",\r\n                    method: \"get\",\r\n                    page: 1,\r\n                    limit: 100,\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.changdiTypesSelectSearch = data.data.list;\r\n                    }\r\n                });\r\n                //查询当前表搜索条件所有列�?\r\n            },\r\n            //每页�?\r\n            sizeChangeHandle(val) {\r\n                this.pageSize = val;\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 当前�?\r\n            currentChangeHandle(val) {\r\n                this.pageIndex = val;\r\n                this.getDataList();\r\n            },\r\n            // 多�?\r\n            selectionChangeHandler(val) {\r\n                this.dataListSelections = val;\r\n            },\r\n            // 添加/修改\r\n            addOrUpdateHandler(id, type) {\r\n                this.showFlag = false;\r\n                this.addOrUpdateFlag = true;\r\n                this.crossAddOrUpdateFlag = false;\r\n                if (type != 'info') {\r\n                    type = 'else';\r\n                }\r\n                this.$nextTick(() => {\r\n                    this.$refs.addOrUpdate.init(id, type);\r\n                });\r\n            },\r\n            // 下载\r\n            download(file) {\r\n                window.open(\" ${file} \")\r\n            },\r\n            // 删除\r\n            deleteHandler(id) {\r\n                var ids = id ? [Number(id)] : this.dataListSelections.map(item => {\r\n                    return Number(item.id);\r\n                });\r\n\r\n                this.$confirm(`确定进行[${id ? \"删除\" : \"批量删除\"}]操作?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: \"changdiOrder/delete\",\r\n                        method: \"post\",\r\n                        data: ids\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            this.$message({\r\n                                message: \"操作成功\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }else{\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n            // 导入功能上传文件成功后调用导入方�?\r\n            changdiOrderUploadSuccess(data){\r\n                let _this = this;\r\n                _this.$http({\r\n                    url: \"changdiOrder/batchInsert?fileName=\" + data.file,\r\n                    method: \"get\"\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        _this.$message({\r\n                            message: \"导入场地预约数据成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.search();\r\n                            }\r\n                        });\r\n                    }else{\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n\r\n            },\r\n            // 导入功能上传文件失败后调用导入方�?\r\n            changdiOrderUploadError(data){\r\n                this.$message.error('上传失败');\r\n            },\r\n\r\n            //取消预约\r\n            refund(id){\r\n                this.$confirm(`确定要取消预约吗?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: \"changdiOrder/refund?id=\"+id,\r\n                        method: \"post\",\r\n                    }).then(({ data }) => {\r\n                        if (data && data.code === 0) {\r\n                            this.$message({\r\n                                message:\"取消成功\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {this.search();}\r\n                            });\r\n                        } else {\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.slt {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .ad {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .pages {\r\n    & ::v-deep el-pagination__sizes{\r\n      & ::v-deep el-input__inner {\r\n        height: 22px;\r\n        line-height: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .el-button+.el-button {\r\n    margin:0;\r\n  }\r\n\r\n  .tables {\r\n\t& ::v-deep .el-button--success {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(121, 244, 164, 0.54);\r\n\t}\r\n\r\n\t& ::v-deep .el-button--primary {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(169, 115, 232, 0.65);\r\n\t}\r\n\r\n\t& ::v-deep .el-button--danger {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: rgba(241, 120, 186, 1);\r\n\t}\r\n\r\n    & ::v-deep .el-button {\r\n      margin: 4px;\r\n    }\r\n  }\r\n</style>\r\n\r\n\r\n\r\n"]}]}